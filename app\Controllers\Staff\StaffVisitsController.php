<?php

namespace App\Controllers\Staff;

use App\Models\FieldVisitsModel;
use App\Models\countryModel;
use App\Models\provinceModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\UsersModel;
use CodeIgniter\RESTful\ResourceController;

class StaffVisitsController extends ResourceController
{
    protected $fieldVisitsModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $usersModel;
    protected $helpers = ['form', 'url', 'file', 'text', 'info'];

    public function __construct()
    {
        $this->fieldVisitsModel = new FieldVisitsModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->usersModel = new UsersModel();
    }

    /**
     * Display a list of all field visits
     */
    public function index()
    {
        // Get all field visits
        $visits = $this->fieldVisitsModel
            ->select('field_visits.*, c.name as country_name, p.name as province_name, d.name as district_name, l.name as llg_name')
            ->join('adx_country c', 'c.id = field_visits.country_id', 'left')
            ->join('adx_province p', 'p.id = field_visits.province_id', 'left')
            ->join('adx_district d', 'd.id = field_visits.district_id', 'left')
            ->join('adx_llg l', 'l.id = field_visits.llg_id', 'left')
            ->where('field_visits.deleted_at IS NULL')
            ->orderBy('field_visits.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Field Visits',
            'page_header' => 'Field Visits Management',
            'visits' => $visits
        ];

        return view('staff/staff_visits/staff_visits_index', $data);
    }

    /**
     * Display the form to create a new field visit
     */
    public function new()
    {
        // Get the current user's district from session
        $districtId = session()->get('district_id');

        // Get LLGs for the current district
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        $data = [
            'title' => 'Create Field Visit',
            'page_header' => 'Create New Field Visit',
            'llgs' => $llgs,
            'officers' => $this->usersModel->where('status', 1)->findAll()
        ];

        return view('staff/staff_visits/staff_visits_create', $data);
    }

    /**
     * Process the creation of a new field visit
     */
    public function create()
    {
        // Validate form data
        $rules = [
            'llg_id' => 'required|numeric',
            'date_start' => 'required',
            'date_end' => 'required',
            'purpose' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Get location IDs from session
        $countryId = session()->get('orgcountry_id');
        $provinceId = session()->get('orgprovince_id');
        $districtId = session()->get('district_id');

        // Prepare JSON fields
        $locations = $this->request->getPost('locations') ? json_encode(explode("\n", $this->request->getPost('locations'))) : json_encode([]);

        // Process GPS coordinates from textarea (each line is a separate coordinate)
        $gpsCoordinates = [];
        $gpsText = $this->request->getPost('gps_coordinates') ?? '';
        if (!empty($gpsText)) {
            $gpsLines = explode("\n", $gpsText);
            foreach ($gpsLines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $gpsCoordinates[] = $line;
                }
            }
        }
        $gps = json_encode($gpsCoordinates);

        // Prepare officers data
        $selectedOfficers = $this->request->getPost('officers') ?? [];
        $officersData = [];

        foreach ($selectedOfficers as $officerId) {
            $officer = $this->usersModel->find($officerId);
            if ($officer) {
                $officersData[] = [
                    'id' => $officer['id'],
                    'name' => $officer['name']
                ];
            }
        }

        // Prepare achievements and beneficiaries
        $achievements = $this->request->getPost('achievements') ? json_encode(explode("\n", $this->request->getPost('achievements'))) : json_encode([]);
        $beneficiaries = $this->request->getPost('beneficiaries') ? json_encode(explode("\n", $this->request->getPost('beneficiaries'))) : json_encode([]);

        // Prepare data for insertion
        $data = [
            'country_id' => $countryId,
            'province_id' => $provinceId,
            'district_id' => $districtId,
            'llg_id' => $this->request->getPost('llg_id'),
            'locations' => $locations,
            'gps' => $gps,
            'officers' => json_encode($officersData),
            'date_start' => $this->request->getPost('date_start'),
            'date_end' => $this->request->getPost('date_end'),
            'purpose' => $this->request->getPost('purpose'),
            'achievements' => $achievements,
            'beneficiaries' => $beneficiaries,
            'status' => 1, // Default active status
            'created_by' => session()->get('emp_id') ?? 1
        ];

        // Insert data
        try {
            if ($this->fieldVisitsModel->insert($data)) {
                return redirect()->to(base_url('staff/extension/field-visits'))
                    ->with('success', 'Field visit created successfully');
            } else {
                // Get validation errors if any
                $errors = $this->fieldVisitsModel->errors();
                if (!empty($errors)) {
                    return redirect()->back()->withInput()
                        ->with('error', 'Validation failed: ' . implode(', ', $errors))
                        ->with('errors', $errors);
                } else {
                    return redirect()->back()->withInput()
                        ->with('error', 'Failed to create field visit. Please try again.');
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Field visit creation error: ' . $e->getMessage());
            return redirect()->back()->withInput()
                ->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Display a specific field visit
     */
    public function show($id = null)
    {
        $visit = $this->fieldVisitsModel
            ->select('field_visits.*, c.name as country_name, p.name as province_name, d.name as district_name, l.name as llg_name')
            ->join('adx_country c', 'c.id = field_visits.country_id', 'left')
            ->join('adx_province p', 'p.id = field_visits.province_id', 'left')
            ->join('adx_district d', 'd.id = field_visits.district_id', 'left')
            ->join('adx_llg l', 'l.id = field_visits.llg_id', 'left')
            ->find($id);

        if (!$visit) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('error', 'Field visit not found');
        }

        $data = [
            'title' => 'View Field Visit',
            'page_header' => 'Field Visit Details',
            'visit' => $visit
        ];

        return view('staff/staff_visits/staff_visits_view', $data);
    }

    /**
     * Display the form to edit a field visit
     */
    public function edit($id = null)
    {
        $visit = $this->fieldVisitsModel->find($id);

        if (!$visit) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('error', 'Field visit not found');
        }

        // Get the current user's district from session
        $districtId = session()->get('district_id');

        // Get LLGs for the current district
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        $data = [
            'title' => 'Edit Field Visit',
            'page_header' => 'Edit Field Visit',
            'visit' => $visit,
            'llgs' => $llgs,
            'officers' => $this->usersModel->where('status', 1)->findAll()
        ];

        return view('staff/staff_visits/staff_visits_edit', $data);
    }

    /**
     * Process the update of a field visit
     */
    public function update($id = null)
    {
        // Validate form data
        $rules = [
            'llg_id' => 'required|numeric',
            'date_start' => 'required',
            'date_end' => 'required',
            'purpose' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare JSON fields
        $locations = $this->request->getPost('locations') ? json_encode(explode("\n", $this->request->getPost('locations'))) : json_encode([]);

        // Process GPS coordinates from textarea (each line is a separate coordinate)
        $gpsCoordinates = [];
        $gpsText = $this->request->getPost('gps_coordinates') ?? '';
        if (!empty($gpsText)) {
            $gpsLines = explode("\n", $gpsText);
            foreach ($gpsLines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $gpsCoordinates[] = $line;
                }
            }
        }
        $gps = json_encode($gpsCoordinates);

        // Prepare officers data
        $selectedOfficers = $this->request->getPost('officers') ?? [];
        $officersData = [];

        foreach ($selectedOfficers as $officerId) {
            $officer = $this->usersModel->find($officerId);
            if ($officer) {
                $officersData[] = [
                    'id' => $officer['id'],
                    'name' => $officer['name']
                ];
            }
        }

        // Prepare achievements and beneficiaries
        $achievements = $this->request->getPost('achievements') ? json_encode(explode("\n", $this->request->getPost('achievements'))) : json_encode([]);
        $beneficiaries = $this->request->getPost('beneficiaries') ? json_encode(explode("\n", $this->request->getPost('beneficiaries'))) : json_encode([]);

        // Get location IDs from session
        $countryId = session()->get('orgcountry_id');
        $provinceId = session()->get('orgprovince_id');
        $districtId = session()->get('district_id');

        // Prepare data for update
        $data = [
            'country_id' => $countryId,
            'province_id' => $provinceId,
            'district_id' => $districtId,
            'llg_id' => $this->request->getPost('llg_id'),
            'locations' => $locations,
            'gps' => $gps,
            'officers' => json_encode($officersData),
            'date_start' => $this->request->getPost('date_start'),
            'date_end' => $this->request->getPost('date_end'),
            'purpose' => $this->request->getPost('purpose'),
            'achievements' => $achievements,
            'beneficiaries' => $beneficiaries,
            'updated_by' => session()->get('emp_id') ?? 1
        ];

        // Update data
        if ($this->fieldVisitsModel->update($id, $data)) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('success', 'Field visit updated successfully');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to update field visit. Please try again.');
        }
    }

    /**
     * Process the deletion of a field visit
     */
    public function delete($id = null)
    {
        // Soft delete by updating the deleted_by field
        $data = [
            'deleted_by' => session()->get('emp_id') ?? 1
        ];

        if ($this->fieldVisitsModel->update($id, $data) && $this->fieldVisitsModel->delete($id)) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('success', 'Field visit deleted successfully');
        } else {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('error', 'Failed to delete field visit. Please try again.');
        }
    }
}
