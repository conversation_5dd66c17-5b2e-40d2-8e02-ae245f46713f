<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\CropBuyersModel;
use App\Models\CropsFarmBlockModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsModel;
use App\Models\districtModel;
use App\Models\provinceModel;
use App\Models\llgModel;
use App\Models\usersModel;

class StaffMarket extends BaseController
{
    private $models = [];
    protected $helpers = ['url', 'form', 'info'];

    public function __construct()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        foreach ($this->helpers as $helper) {
            helper($helper);
        }
    }

    protected function getModel($modelName)
    {
        if (!isset($this->models[$modelName])) {
            switch ($modelName) {
                case 'farmMarketingData':
                    $this->models[$modelName] = new CropsFarmMarketingDataModel();
                    break;
                case 'cropBuyers':
                    $this->models[$modelName] = new CropBuyersModel();
                    break;
                case 'farmBlocks':
                    $this->models[$modelName] = new CropsFarmBlockModel();
                    break;
                case 'farmers':
                    $this->models[$modelName] = new FarmerInformationModel();
                    break;
                case 'crops':
                    $this->models[$modelName] = new CropsModel();
                    break;
                case 'districts':
                    $this->models[$modelName] = new districtModel();
                    break;
                case 'provinces':
                    $this->models[$modelName] = new provinceModel();
                    break;
                case 'llgs':
                    $this->models[$modelName] = new llgModel();
                    break;
                case 'users':
                    $this->models[$modelName] = new usersModel();
                    break;
            }
        }
        return $this->models[$modelName];
    }

    // Helper methods to get specific models
    protected function getFarmMarketingDataModel() { return $this->getModel('farmMarketingData'); }
    protected function getCropBuyersModel() { return $this->getModel('cropBuyers'); }
    protected function getFarmBlockModel() { return $this->getModel('farmBlocks'); }
    protected function getFarmersModel() { return $this->getModel('farmers'); }
    protected function getCropsModel() { return $this->getModel('crops'); }
    protected function getDistrictModel() { return $this->getModel('districts'); }
    protected function getProvinceModel() { return $this->getModel('provinces'); }
    protected function getLlgModel() { return $this->getModel('llgs'); }
    protected function getUsersModel() { return $this->getModel('users'); }

    protected function verifyDistrictAccess($districtId) 
    {
        return $districtId == session()->get('district_id');
    }

    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    public function market_data()
    {
        try {
            // Check if user is logged in and has district access
            if (!session()->get('district_id')) {
                log_message('error', '[Market Data] No district assigned to user');
                return redirect()->back()->with('error', 'No district assigned to your account');
            }

            $district = $this->getDistrictModel()->find(session()->get('district_id'));
            if (!$district) {
                log_message('error', '[Market Data] District not found: ' . session()->get('district_id'));
                return redirect()->back()->with('error', 'District not found');
            }

            $districtName = $district['name'] ?? 'No District Assigned';

            // Get farmers with proper error handling
            $farmers = $this->getFarmersModel()
                ->where('district_id', session()->get('district_id'))
                ->where('status', 'active')
                ->findAll();

            if ($farmers === null) {
                log_message('error', '[Market Data] Database error fetching farmers');
                throw new \Exception('Error retrieving farmers data');
            }

            $data = [
                'title' => 'Marketing Data',
                'page_header' => 'Marketing Data',
                'farmers' => $farmers,
                'district_name' => $districtName,
                'buyers' => $this->getCropBuyersModel()->where('status', 'active')->findAll()
            ];

            return view('staff/farms/market_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[Market Data] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    public function view_market_data($farmer_id)
    {
        try {
            // Fetch farmer with district access verification
            $farmer = $this->getFarmersModel()->where([
                'id' => $farmer_id,
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$farmer) {
                return redirect()->back()->with('error', 'Farmer not found or access denied');
            }

            // Get provinces with error handling
            try {
                $provinces = $this->getProvinceModel()->findAll();
                log_message('debug', '[View Market Data] Provinces query successful. Found ' . count($provinces) . ' provinces');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching provinces: ' . $e->getMessage());
                throw new \Exception('Error retrieving provinces data');
            }

            // Get farm blocks with error handling
            try {
                $farm_blocks = $this->getFarmBlockModel()
                    ->where('farmer_id', $farmer_id)
                    ->where('status', 'active')
                    ->findAll();
                log_message('debug', '[View Market Data] Farm blocks query successful. Found ' . count($farm_blocks) . ' blocks');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching farm blocks: ' . $e->getMessage());
                throw new \Exception('Error retrieving farm blocks data');
            }

            // Get marketing data with error handling
            try {
                $marketing_data = $this->getFarmMarketingDataModel()
                    ->where('farmer_id', $farmer_id)
                    ->where('status', 'active')
                    ->orderBy('market_date', 'DESC')
                    ->findAll();
                log_message('debug', '[View Market Data] Marketing data query successful. Found ' . count($marketing_data) . ' records');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching marketing data: ' . $e->getMessage());
                throw new \Exception('Error retrieving marketing data');
            }

            // Get crops list with error handling
            try {
                $crops = $this->getCropsModel()->findAll();
                log_message('debug', '[View Market Data] Crops query successful. Found ' . count($crops) . ' crops');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching crops: ' . $e->getMessage());
                throw new \Exception('Error retrieving crops data');
            }

            // Get buyers list with error handling
            try {
                $buyers = $this->getCropBuyersModel()->where('status', 'active')->findAll();
                log_message('debug', '[View Market Data] Buyers query successful. Found ' . count($buyers) . ' buyers');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching buyers: ' . $e->getMessage());
                throw new \Exception('Error retrieving buyers data');
            }

            // Get users list with error handling
            try {
                $users = $this->getUsersModel()
                    ->where('org_id', session()->get('org_id'))
                    ->findAll();
                log_message('debug', '[View Market Data] Users query successful. Found ' . count($users) . ' users');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching users: ' . $e->getMessage());
                throw new \Exception('Error retrieving users data');
            }

            $data = [
                'title' => 'Farmer Marketing Data',
                'page_header' => 'Farmer Marketing Data',
                'farmer' => $farmer,
                'provinces' => $provinces,
                'farm_blocks' => $farm_blocks,
                'marketing_data' => $marketing_data,
                'crops' => $crops,
                'buyers' => $buyers,
                'users' => $users
            ];

            return view('staff/farms/view_market_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[View Market Data] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    public function add_market_data($farmer_id)
    {
        try {
            // Verify farmer belongs to user's district
            $farmer = $this->getFarmersModel()->where('id', $farmer_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$farmer) {
                throw new \Exception('Access denied');
            }

            // Prepare market data with proper validation
            $data = $this->prepareMarketData($farmer_id);

            // Additional validation for market-specific fields
            $this->validateMarketData($data);

            // Save the data
            $this->getFarmMarketingDataModel()->save($data);

            // Calculate and cache total sales for reporting
            $this->updateFarmerMarketingStats($farmer_id);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Marketing data added successfully',
                'redirect' => base_url("staff/farms/view-market-data/{$farmer_id}")
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Market Data] ' . $e->getMessage());
            return $this->response->setStatusCode(400)->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function edit_market_data($id)
    {
        try {
            // Fetch marketing data record
            $marketing_data = $this->getFarmMarketingDataModel()->find($id);
            
            if (!$marketing_data) {
                return redirect()->back()->with('error', 'Record not found');
            }
            
            // Verify farmer belongs to user's district
            $farmer = $this->getFarmersModel()->where('id', $marketing_data['farmer_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$farmer) {
                return redirect()->back()->with('error', 'Access denied');
            }

            // Get required data for dropdowns
            $data = [
                'title' => 'Edit Marketing Data',
                'page_header' => 'Edit Marketing Data',
                'marketing_data' => $marketing_data,
                'farmer' => $farmer,
                'provinces' => $this->getModel('provinces')->findAll(),
                'crops' => $this->getCropsModel()->findAll(),
                'buyers' => $this->getCropBuyersModel()->where('status', 'active')->findAll(),
                'farm_blocks' => $this->getFarmBlockModel()
                    ->where('farmer_id', $marketing_data['farmer_id'])
                    ->where('status', 'active')
                    ->findAll()
            ];

            return view('staff/farms/edit_market_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[Edit Market Data] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    public function update_market_data()
    {
        try {
            $id = $this->request->getPost('id');
            $marketing_data = $this->getFarmMarketingDataModel()->find($id);
            
            if (!$marketing_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify farmer belongs to user's district
            $farmer = $this->getFarmersModel()->where('id', $marketing_data['farmer_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$farmer) {
                throw new \Exception('Access denied');
            }

            // Prepare update data with proper validation
            $data = $this->prepareMarketData($marketing_data['farmer_id'], true);

            // Additional validation for market-specific fields
            $this->validateMarketData($data);

            // Update the record
            $this->getFarmMarketingDataModel()->update($id, $data);

            // Recalculate and update cached stats
            $this->updateFarmerMarketingStats($marketing_data['farmer_id']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Marketing data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Market Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_market_data($id)
    {
        try {
            $marketing_data = $this->getFarmMarketingDataModel()->find($id);
            
            if (!$marketing_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify farmer belongs to user's district
            $farmer = $this->getFarmersModel()->where('id', $marketing_data['farmer_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$farmer) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmMarketingDataModel()->update($id, $data);

            // Recalculate and update cached stats after deletion
            $this->updateFarmerMarketingStats($marketing_data['farmer_id']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Market Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Prepare and validate market data for insert/update
     * @param int $farmer_id
     * @param bool $isUpdate
     * @return array
     */
    private function prepareMarketData(int $farmer_id, bool $isUpdate = false): array
    {
        $data = [
            'farmer_id' => $farmer_id,
            'block_id' => $this->request->getPost('block_id') ?: null,
            'crop_id' => $this->request->getPost('crop_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'market_date' => $this->request->getPost('market_date'),
            'market_stage' => $this->request->getPost('market_stage'),
            'buyer_id' => $this->request->getPost('buyer_id'),
            'selling_location' => $this->request->getPost('selling_location'),
            'product' => $this->request->getPost('product'),
            'product_type' => $this->request->getPost('product_type'),
            'unit' => $this->request->getPost('unit'),
            'unit_of_measure' => $this->request->getPost('unit_of_measure'),
            'quantity' => $this->request->getPost('quantity'),
            'market_price_per_unit' => $this->request->getPost('market_price_per_unit'),
            'total_freight_cost' => $this->request->getPost('total_freight_cost') ?: 0,
            'remarks' => $this->request->getPost('remarks')
        ];

        if ($isUpdate) {
            $data['updated_by'] = session()->get('emp_id');
            $data['updated_at'] = date('Y-m-d H:i:s');
        } else {
            $data['created_by'] = session()->get('emp_id');
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['status'] = 'active';
        }

        return $data;
    }

    /**
     * Validate market data fields
     * @param array $data
     * @throws \Exception
     */
    private function validateMarketData(array $data): void
    {
        // Required fields validation
        $required = [
            'crop_id',
            'market_stage',
            'market_date',
            'buyer_id',
            'product',
            'product_type',
            'unit',
            'unit_of_measure',
            'quantity',
            'market_price_per_unit',
            'province_id',
            'district_id',
            'llg_id'
        ];
        $this->validateInput($data, $required);

        // Numeric fields validation
        $numericFields = [
            'unit' => 'Unit value',
            'quantity' => 'Quantity',
            'market_price_per_unit' => 'Market price per unit'
        ];

        foreach ($numericFields as $field => $label) {
            if (!isset($data[$field])) continue;
            
            if (!is_numeric($data[$field])) {
                throw new \Exception("{$label} must be a number.");
            }
            
            if ($data[$field] <= 0) {
                throw new \Exception("{$label} must be a positive number.");
            }
        }

        // Validate total_freight_cost if provided
        if (!empty($data['total_freight_cost'])) {
            if (!is_numeric($data['total_freight_cost'])) {
                throw new \Exception("Total freight cost must be a number.");
            }
            if ($data['total_freight_cost'] < 0) {
                throw new \Exception("Total freight cost cannot be negative.");
            }
        }

        // Validate market date
        if (strtotime($data['market_date']) > time()) {
            throw new \Exception("Market date cannot be in the future.");
        }
    }

    /**
     * Update farmer marketing statistics for reporting
     * @param int $farmer_id
     */
    private function updateFarmerMarketingStats(int $farmer_id): void
    {
        try {
            // Calculate total sales
            $result = $this->getFarmMarketingDataModel()
                ->selectSum('quantity', 'total_quantity')
                ->selectSum('market_price_per_unit * quantity', 'total_sales')
                ->where('farmer_id', $farmer_id)
                ->where('status', 'active')
                ->first();

            // Update farmer record with marketing stats
            if ($result) {
                $this->getFarmersModel()->update($farmer_id, [
                    'total_sales' => $result['total_sales'] ?? 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', '[Update Farmer Marketing Stats] ' . $e->getMessage());
            // Don't throw exception here to prevent disrupting the main flow
        }
    }
}
