<?php namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\CropBuyersModel;
use App\Models\FarmerInformationModel;

class CropsMarket_Dashboard extends BaseController
{
    protected $session;
    protected $validation;
    protected $marketingModel;
    protected $buyersModel;
    protected $farmerModel;

    public function __construct()
    {
        $this->session = \Config\Services::session();
        $this->validation = \Config\Services::validation();
        helper(['info', 'form', 'url', 'text']);
        
        $this->marketingModel = new CropsFarmMarketingDataModel();
        $this->buyersModel = new CropBuyersModel();
        $this->farmerModel = new FarmerInformationModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Crops Market Data',
            'marketData' => $this->marketingModel->getMarketingReportData(),
            'menu' => 'crops-markets',
            'pageTitle' => 'Crops Market Dashboard',
            'breadcrumbs' => [
                'Dashboard' => base_url(),
                'Crops Market' => '#'
            ]
        ];

        return view('dashboard_reports/dashboard_crops_market', $data);
    }
}
