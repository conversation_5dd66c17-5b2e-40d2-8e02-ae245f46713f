<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Back Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/office/documents') ?>" class="text-success">Document Folders</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/office/documents/files/' . $folder['id']) ?>" class="text-success"><?= esc($folder['folder_name']) ?></a></li>
            <li class="breadcrumb-item active" aria-current="page">Upload File</li>
        </ol>
    </nav>
    <a href="<?= base_url('staff/office/documents/files/' . $folder['id']) ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Files
    </a>
</div>

<!-- Main Content -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-file-upload me-2"></i>Upload File to <?= esc($folder['folder_name']) ?></h5>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <form action="<?= base_url('staff/office/documents/files/store/' . $folder['id']) ?>" method="post" enctype="multipart/form-data">
            <?= csrf_field() ?>

            <div class="mb-3">
                <label for="document_file" class="form-label">Select File <span class="text-danger">*</span></label>
                <input type="file" class="form-control" id="document_file" name="document_file" required>
                <div class="form-text">Maximum file size: 500MB</div>
            </div>

            <div class="mb-3">
                <label for="file_name" class="form-label">File Name</label>
                <input type="text" class="form-control" id="file_name" name="file_name" value="<?= old('file_name') ?>">
                <div class="form-text">Optional. If left blank, the original file name will be used.</div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3"><?= old('description') ?></textarea>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="<?= base_url('staff/office/documents/files/' . $folder['id']) ?>" class="btn btn-outline-secondary me-md-2">Cancel</a>
                <button type="submit" class="btn btn-success">Upload File</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
