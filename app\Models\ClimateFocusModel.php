<?php

namespace App\Models;

use CodeIgniter\Model;

class ClimateFocusModel extends Model
{
    protected $table            = 'climate_focus';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    
    protected $allowedFields = [
        'country_id',
        'province_id',
        'district_id',
        'gps',
        'location',
        'remarks',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'country_id'  => 'required|numeric',
        'province_id' => 'required|numeric',
        'district_id' => 'permit_empty|numeric',
        'gps'         => 'required|max_length[100]',
        'location'    => 'required|max_length[255]',
        'status'      => 'required|numeric'
    ];
    
    protected $validationMessages = [];
    protected $skipValidation     = false;
    protected $cleanValidationRules = true;
}
