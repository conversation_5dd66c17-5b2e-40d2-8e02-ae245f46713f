<?php namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\PesticidesModel;
use App\Models\CropsFarmPesticidesDataModel;

class CropsPesticides_Dashboard extends BaseController
{
    protected $session;
    protected $pesticidesModel;
    protected $cropsFarmPesticidesDataModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = \Config\Services::session();
        $this->pesticidesModel = new PesticidesModel();
        $this->cropsFarmPesticidesDataModel = new CropsFarmPesticidesDataModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Crops Pesticides Dashboard',
            'menu' => 'crops-pesticides',
            'pesticideData' => $this->cropsFarmPesticidesDataModel
                ->select('
                    crops_farm_blocks.block_code,
                    crops_farm_blocks.block_site,
                    farmer_information.given_name,
                    farmer_information.surname,
                    adx_crops.crop_name,
                    adx_province.name as province_name,
                    adx_district.name as district_name,
                    adx_llg.name as llg_name,
                    adx_ward.name as ward_name,
                    adx_pesticides.name as pesticide_name,
                    crops_farm_pesticides_data.unit_of_measure,
                    SUM(crops_farm_pesticides_data.quantity) as total_quantity,
                    MAX(crops_farm_pesticides_data.action_date) as latest_action_date,
                    GROUP_CONCAT(DISTINCT crops_farm_pesticides_data.remarks) as remarks
                ')
                ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_pesticides_data.block_id')
                ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
                ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
                ->join('adx_pesticides', 'adx_pesticides.id = crops_farm_pesticides_data.pesticide_id', 'left')
                ->join('adx_province', 'adx_province.id = crops_farm_blocks.province_id')
                ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
                ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
                ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
                ->where('crops_farm_pesticides_data.status', 'active')
                ->where('crops_farm_blocks.status', 'active')
                ->groupBy('
                    crops_farm_blocks.block_code,
                    crops_farm_blocks.block_site,
                    farmer_information.given_name,
                    farmer_information.surname,
                    adx_crops.crop_name,
                    adx_province.name,
                    adx_district.name,
                    adx_llg.name,
                    adx_ward.name,
                    adx_pesticides.name,
                    crops_farm_pesticides_data.unit_of_measure
                ')
                ->orderBy('latest_action_date', 'DESC')
                ->findAll()
        ];

        return view('dashboard_reports/dashboard_crops_pesticides', $data);
    }
}
