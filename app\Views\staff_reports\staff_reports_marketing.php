<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Marketing Report</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/reports') ?>">Reports</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Marketing</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Total Revenue</h6>
                            <h2 class="mt-2 mb-0">K<?= number_format($stats['total_revenue'], 2) ?></h2>
                        </div>
                        <div class="fs-1 text-success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Total Transactions</h6>
                            <h2 class="mt-2 mb-0"><?= number_format($stats['total_transactions']) ?></h2>
                        </div>
                        <div class="fs-1 text-primary">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Total Quantity</h6>
                            <h2 class="mt-2 mb-0"><?= number_format($stats['total_quantity']) ?></h2>
                        </div>
                        <div class="fs-1 text-info">
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Revenue by Crop</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="cropRevenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Distribution by LLG</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="llgDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Top Buyers</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="buyersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Trend Chart -->
    <div class="card mb-4">
        <div class="card-body">
            <h6 class="card-title">Monthly Revenue Trend (<?= date('Y') ?>)</h6>
            <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="trendChart"></canvas>
            </div>
        </div>
    </div>

    <!-- LLG Distribution Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribution by LLG</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>LLG</th>
                            <?php
                            // Get unique crop names from the crops array
                            $unique_crops = array_unique(array_reduce($farmer_totals, function($carry, $item) {
                                return array_merge($carry, $item['crops']);
                            }, []));
                            sort($unique_crops);

                            foreach ($unique_crops as $crop_name): ?>
                                <th class="text-center" colspan="2"><?= esc($crop_name) ?></th>
                            <?php endforeach; ?>
                            <th class="text-center" colspan="2">Total</th>
                        </tr>
                        <tr>
                            <th></th>
                            <?php foreach ($unique_crops as $crop_name): ?>
                                <th class="text-center">Revenue</th>
                                <th class="text-center">Quantity</th>
                            <?php endforeach; ?>
                            <th class="text-center">Revenue</th>
                            <th class="text-center">Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Get unique LLGs and sort them
                        $unique_llgs = array_unique(array_map(function($item) {
                            return $item['llg_name'];
                        }, $farmer_totals));
                        sort($unique_llgs);

                        // Initialize totals
                        $crop_totals = array_fill_keys($unique_crops, ['revenue' => 0, 'quantity' => 0]);
                        $grand_total = ['revenue' => 0, 'quantity' => 0];

                        // Group data by LLG and Crop
                        $llg_data = [];
                        foreach ($farmer_totals as $farmer) {
                            $llg = $farmer['llg_name'];

                            if (!isset($llg_data[$llg])) {
                                $llg_data[$llg] = array_fill_keys($unique_crops, ['revenue' => 0, 'quantity' => 0]);
                            }

                            foreach ($farmer['crops'] as $crop_name) {
                                $llg_data[$llg][$crop_name]['revenue'] += $farmer['total_revenue'];
                                $llg_data[$llg][$crop_name]['quantity'] += $farmer['total_quantity'] ?? 0;
                            }
                        }

                        // Display data
                        foreach ($unique_llgs as $llg):
                            $llg_total = ['revenue' => 0, 'quantity' => 0];
                        ?>
                            <tr>
                                <td><?= esc($llg) ?></td>
                                <?php foreach ($unique_crops as $crop_name):
                                    $revenue = $llg_data[$llg][$crop_name]['revenue'] ?? 0;
                                    $quantity = $llg_data[$llg][$crop_name]['quantity'] ?? 0;

                                    // Update totals
                                    $crop_totals[$crop_name]['revenue'] += $revenue;
                                    $crop_totals[$crop_name]['quantity'] += $quantity;
                                    $llg_total['revenue'] += $revenue;
                                    $llg_total['quantity'] += $quantity;
                                ?>
                                    <td class="text-end"><?= $revenue ? 'K' . number_format($revenue, 2) : '-' ?></td>
                                    <td class="text-end"><?= $quantity ? number_format($quantity, 2) : '-' ?></td>
                                <?php endforeach; ?>
                                <td class="text-end fw-bold">K<?= number_format($llg_total['revenue'], 2) ?></td>
                                <td class="text-end fw-bold"><?= number_format($llg_total['quantity'], 2) ?></td>
                            </tr>
                        <?php
                            $grand_total['revenue'] += $llg_total['revenue'];
                            $grand_total['quantity'] += $llg_total['quantity'];
                        endforeach;
                        ?>
                        <tr class="table-light">
                            <td class="fw-bold">Total</td>
                            <?php foreach ($unique_crops as $crop_name): ?>
                                <td class="text-end fw-bold">K<?= number_format($crop_totals[$crop_name]['revenue'], 2) ?></td>
                                <td class="text-end fw-bold"><?= number_format($crop_totals[$crop_name]['quantity'], 2) ?></td>
                            <?php endforeach; ?>
                            <td class="text-end fw-bold">K<?= number_format($grand_total['revenue'], 2) ?></td>
                            <td class="text-end fw-bold"><?= number_format($grand_total['quantity'], 2) ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Table Card -->
    <div class="card">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Farmer Marketing Summary</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Farmer</th>
                            <th>Crops</th>
                            <th>Total Revenue</th>
                            <th>Total Freight Cost</th>
                            <th>Location (LLG)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $loop_count = 1;
                        foreach ($farmer_totals as $farmer): ?>
                            <tr>
                                <td><?= $loop_count++ ?></td>
                                <td><?= esc($farmer['farmer_name']) ?></td>
                                <td><?= esc(implode(', ', $farmer['crops'])) ?></td>
                                <td class="text-end">K<?= number_format($farmer['total_revenue'], 2) ?></td>
                                <td class="text-end">K<?= number_format($farmer['total_freight_cost'], 2) ?></td>
                                <td><?= esc($farmer['llg_name']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    const stats = <?= json_encode($stats) ?>;
    const farmer_totals = <?= json_encode($farmer_totals) ?>;
    const farmer_totals_array = Object.values(farmer_totals);

    // Check if we have any data
    if (farmer_totals_array.length === 0) {
        console.log('No marketing data available');
        // Add a message to the page
        $('.container-fluid').prepend('<div class="alert alert-info">No marketing data available for the current filters.</div>');
    }

    // Chart configurations
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 15
                }
            }
        }
    };

    // Process data for charts
    const cropStats = farmer_totals_array.reduce((acc, farmer) => {
        farmer.crops.forEach(cropName => {
            if (!acc[cropName]) {
                acc[cropName] = {
                    revenue: 0,
                    quantity: 0,
                    color: stats.by_crop[cropName]?.color || '#' + Math.floor(Math.random()*16777215).toString(16)
                };
            }
            // Divide revenue and quantity by number of crops for this farmer
            acc[cropName].revenue += farmer.total_revenue / farmer.crops.length;
            acc[cropName].quantity += (farmer.total_quantity || 0) / farmer.crops.length;
        });
        return acc;
    }, {});

    // Process data for LLG distribution
    const llgStats = farmer_totals_array.reduce((acc, farmer) => {
        const llgName = farmer.llg_name || 'Unknown';
        if (!acc[llgName]) {
            acc[llgName] = {
                crops: {},
                total_revenue: 0,
                total_quantity: 0
            };
        }
        farmer.crops.forEach(cropName => {
            if (!acc[llgName].crops[cropName]) {
                acc[llgName].crops[cropName] = {
                    revenue: 0,
                    quantity: 0
                };
            }
            // Divide revenue and quantity by number of crops for this farmer
            const revenuePerCrop = farmer.total_revenue / farmer.crops.length;
            const quantityPerCrop = (farmer.total_quantity || 0) / farmer.crops.length;

            acc[llgName].crops[cropName].revenue += revenuePerCrop;
            acc[llgName].crops[cropName].quantity += quantityPerCrop;
            acc[llgName].total_revenue += revenuePerCrop;
            acc[llgName].total_quantity += quantityPerCrop;
        });
        return acc;
    }, {});

    // Revenue by Crop Chart
    if (document.getElementById('cropRevenueChart')) {
        new Chart(document.getElementById('cropRevenueChart'), {
            type: 'pie',
            data: {
                labels: Object.keys(cropStats),
                datasets: [{
                    data: Object.values(cropStats).map(crop => crop.revenue),
                    backgroundColor: Object.values(cropStats).map(crop => crop.color)
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    ...chartOptions.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                return `${label}: K${value.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // LLG Distribution Chart
    if (document.getElementById('llgDistributionChart')) {
        const llgLabels = Object.keys(llgStats);
        const cropTypes = [...new Set(farmer_totals_array.flatMap(f => f.crops))];
        const llgDatasets = cropTypes.map(cropName => ({
            label: cropName,
            data: llgLabels.map(llg => llgStats[llg].crops[cropName]?.revenue || 0),
            backgroundColor: cropStats[cropName]?.color
        }));

        new Chart(document.getElementById('llgDistributionChart'), {
            type: 'bar',
            data: {
                labels: llgLabels,
                datasets: llgDatasets
            },
            options: {
                ...chartOptions,
                scales: {
                    x: {
                        stacked: true
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'K' + value.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }
                    }
                },
                plugins: {
                    ...chartOptions.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                return `${label}: K${value.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Top Buyers Chart
    if (document.getElementById('buyersChart')) {
        new Chart(document.getElementById('buyersChart'), {
            type: 'bar',
            data: {
                labels: Object.keys(stats.by_buyer),
                datasets: [{
                    label: 'Revenue',
                    data: Object.values(stats.by_buyer).map(buyer => buyer.revenue),
                    backgroundColor: '#36A2EB'
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'K' + value.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }
                    }
                }
            }
        });
    }

    // Monthly Trend Chart
    if (document.getElementById('trendChart')) {
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        new Chart(document.getElementById('trendChart'), {
            type: 'line',
            data: {
                labels: monthNames,
                datasets: [
                    {
                        label: 'Revenue',
                        data: stats.monthly_revenue,
                        borderColor: '#36A2EB',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        fill: true,
                        tension: 0.4
                    }
                ]
            },
            options: {
                ...chartOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'K' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    ...chartOptions.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                return `${label}: K${value.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
<?= $this->endSection() ?>
