<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item">Reports</li>
            <li class="breadcrumb-item active" aria-current="page">Farm Blocks Report</li>
        </ol>
    </nav>

    <!-- Summary Cards -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Total Blocks</h6>
                            <h2 class="mt-2 mb-0"><?= number_format($stats['total_blocks']) ?></h2>
                        </div>
                        <div class="fs-1 text-primary">
                            <i class="fas fa-th"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Active Blocks</h6>
                            <h2 class="mt-2 mb-0"><?= number_format($stats['total_active']) ?></h2>
                        </div>
                        <div class="fs-1 text-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Blocks by Crop</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="cropDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Blocks by LLG</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="llgChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-12 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Blocks by Ward</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="wardChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Tables Row -->
    <div class="row g-3 mb-4">
        <!-- LLG and Ward Distribution Table -->
        <div class="col-12 col-md-6">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Farm Blocks by LLG and Ward</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>LLG</th>
                                    <th>Ward</th>
                                    <th class="text-end">Number of Blocks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $total_blocks = 0;
                                foreach ($stats['by_llg'] as $llg => $llg_count):
                                    $first_ward = true;
                                    $llg_wards = array_filter($blocks_data, fn($block) => $block['llg_name'] === $llg);
                                    $ward_counts = [];
                                    
                                    foreach ($llg_wards as $block) {
                                        $ward = $block['ward_name'];
                                        if (!isset($ward_counts[$ward])) {
                                            $ward_counts[$ward] = 0;
                                        }
                                        $ward_counts[$ward]++;
                                    }

                                    foreach ($ward_counts as $ward => $ward_count):
                                        $total_blocks += $ward_count;
                                ?>
                                    <tr>
                                        <?php if ($first_ward): ?>
                                            <td rowspan="<?= count($ward_counts) ?>"><?= esc($llg) ?></td>
                                        <?php endif; ?>
                                        <td><?= esc($ward) ?></td>
                                        <td class="text-end"><?= number_format($ward_count) ?></td>
                                    </tr>
                                <?php
                                        $first_ward = false;
                                    endforeach;
                                endforeach;
                                ?>
                                <tr class="table-secondary">
                                    <td colspan="2"><strong>Total</strong></td>
                                    <td class="text-end"><strong><?= number_format($total_blocks) ?></strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crop Distribution by Location Table -->
        <div class="col-12 col-md-6">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Crop Distribution by LLG and Ward</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>LLG</th>
                                    <th>Ward</th>
                                    <th>Crops Distribution</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($stats['by_llg'] as $llg => $llg_count):
                                    $first_ward = true;
                                    $llg_wards = array_filter($blocks_data, fn($block) => $block['llg_name'] === $llg);
                                    $ward_crops = [];
                                    
                                    // Calculate crops per ward
                                    foreach ($llg_wards as $block) {
                                        $ward = $block['ward_name'];
                                        if (!isset($ward_crops[$ward])) {
                                            $ward_crops[$ward] = [];
                                        }
                                        if (!isset($ward_crops[$ward][$block['crop_name']])) {
                                            $ward_crops[$ward][$block['crop_name']] = 0;
                                        }
                                        $ward_crops[$ward][$block['crop_name']]++;
                                    }

                                    foreach ($ward_crops as $ward => $crops):
                                        $ward_total = array_sum($crops);
                                ?>
                                    <tr>
                                        <?php if ($first_ward): ?>
                                            <td rowspan="<?= count($ward_crops) ?>"><?= esc($llg) ?></td>
                                        <?php endif; ?>
                                        <td><?= esc($ward) ?></td>
                                        <td>
                                            <?php foreach ($crops as $crop => $count): ?>
                                                <div class="mb-1">
                                                    <i class="fas fa-circle me-2" style="color: <?= $cropColors[$crop] ?>"></i>
                                                    <?= esc($crop) ?>: <?= number_format($count) ?> 
                                                    (<?= number_format(($count / $ward_total) * 100, 1) ?>%)
                                                </div>
                                            <?php endforeach; ?>
                                        </td>
                                    </tr>
                                <?php
                                        $first_ward = false;
                                    endforeach;
                                endforeach;
                                ?>
                                <tr class="table-secondary">
                                    <td colspan="2"><strong>Overall Distribution</strong></td>
                                    <td>
                                        <?php foreach ($stats['by_crop'] as $crop => $count): ?>
                                            <div class="mb-1">
                                                <i class="fas fa-circle me-2" style="color: <?= $cropColors[$crop] ?>"></i>
                                                <strong><?= esc($crop) ?>: <?= number_format($count) ?> 
                                                (<?= number_format(($count / $total_blocks) * 100, 1) ?>%)</strong>
                                            </div>
                                        <?php endforeach; ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add this after the charts row and before the table card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">Farm Blocks Location Map</h6>
                    <div id="blocksMap" style="height: 500px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Card -->
    <div class="card">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-th me-2"></i>Farm Blocks Report</h5>
            <button class="btn btn-success" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-2"></i>Export to Excel
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="blocksTable" class="table table-striped table-bordered text-nowrap w-100">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Block Code</th>
                            <th>Farmer Name</th>
                            <th>Gender</th>
                            <th>Crop</th>
                            <th>LLG</th>
                            <th>Ward</th>
                            <th>Village</th>
                            <th>Block Site</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $loop_count = 1;
                        foreach ($blocks_data as $block): ?>
                            <tr class="clickable-row" data-href="<?= base_url() ?>staff/reports/block_profile/<?= $block['id'] ?>" style="cursor: pointer;">
                                <td><?= $loop_count++ ?></td>
                                <td><?= esc($block['block_code']) ?></td>
                                <td><?= esc($block['given_name']) . ' ' . esc($block['surname']) ?></td>
                                <td><?= esc($block['gender']) ?></td>
                                <td>
                                    <i class="fas fa-circle me-2" style="color: <?= esc($block['crop_color_code']) ?>"></i>
                                    <?= esc($block['crop_name']) ?>
                                </td>
                                <td><?= esc($block['llg_name']) ?></td>
                                <td><?= esc($block['ward_name']) ?></td>
                                <td><?= esc($block['village']) ?></td>
                                <td><?= esc($block['block_site']) ?></td>
                                <td>
                                    <span class="badge bg-<?= $block['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($block['status']) ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
$(document).ready(function() {
    // Make table rows clickable
    $(document).on('click', '.clickable-row', function() {
        window.location = $(this).data('href');
    });

    // Add hover effect for clickable rows
    $('.clickable-row').hover(
        function() {
            $(this).css('background-color', '#f5f5f5');
        },
        function() {
            $(this).css('background-color', '');
        }
    );

    // DataTable initialization
    $('#blocksTable').DataTable({
        dom: '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6"f>>rtip',
        buttons: [
            {
                extend: 'collection',
                text: 'Export',
                buttons: ['copy', 'csv', 'excel', 'pdf', 'print']
            }
        ],
        pageLength: 25,
        order: [[0, 'asc']],
        scrollX: true,
        responsive: false
    });

    // Chart configurations
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 15
                }
            }
        }
    };

    // Prepare data for charts
    const stats = <?= json_encode($stats) ?>;
    
    // Use PHP-provided crop colors
    const cropColors = <?= json_encode($cropColors) ?>;
    const blocks_data = <?= json_encode($blocks_data) ?>;

    // Update Crop Distribution Chart with correct colors
    new Chart(document.getElementById('cropDistributionChart'), {
        type: 'pie',
        data: {
            labels: Object.keys(stats.by_crop),
            datasets: [{
                data: Object.values(stats.by_crop),
                backgroundColor: Object.keys(stats.by_crop).map(crop => cropColors[crop])
            }]
        },
        options: {
            ...chartOptions,
            plugins: {
                ...chartOptions.plugins,
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} blocks (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });

    // LLG Chart
    new Chart(document.getElementById('llgChart'), {
        type: 'bar',
        data: {
            labels: Object.keys(stats.by_llg),
            datasets: [{
                label: 'Number of Blocks',
                data: Object.values(stats.by_llg),
                backgroundColor: '#36A2EB'
            }]
        },
        options: {
            ...chartOptions,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        precision: 0
                    }
                }
            }
        }
    });

    // Ward Chart
    new Chart(document.getElementById('wardChart'), {
        type: 'doughnut',
        data: {
            labels: Object.keys(stats.by_ward),
            datasets: [{
                data: Object.values(stats.by_ward),
                backgroundColor: [
                    '#4BC0C0', '#FF9F40', '#9966FF', '#FF6384',
                    '#36A2EB', '#FFCE56', '#FF6384', '#36A2EB'
                ]
            }]
        },
        options: chartOptions
    });

    // Initialize the map
    const map = L.map('blocksMap').setView([-6.314993, 143.95555], 7); // Center on PNG

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Add markers for each block
    const markers = [];
    blocks_data.forEach(block => {
        if (block.lat && block.lon) {
            const marker = L.circleMarker([block.lat, block.lon], {
                radius: 8,
                fillColor: cropColors[block.crop_name],
                color: '#fff',
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8
            }).addTo(map);

            // Add popup with block information
            marker.bindPopup(`
                <strong>Block Code:</strong> ${block.block_code}<br>
                <strong>Farmer:</strong> ${block.given_name} ${block.surname}<br>
                <strong>Crop:</strong> ${block.crop_name}<br>
                <strong>Location:</strong> ${block.block_site}<br>
                <strong>District:</strong> ${block.district_name}<br>
                <strong>LLG:</strong> ${block.llg_name}
            `);

            markers.push(marker);
        }
    });

    // Fit map bounds to show all markers if there are any
    if (markers.length > 0) {
        const group = new L.featureGroup(markers);
        map.fitBounds(group.getBounds().pad(0.1));
    }

    // Add legend for crops
    const legend = L.control({ position: 'bottomright' });
    legend.onAdd = function(map) {
        const div = L.DomUtil.create('div', 'info legend');
        div.style.backgroundColor = 'white';
        div.style.padding = '10px';
        div.style.borderRadius = '5px';
        div.style.border = '2px solid rgba(0,0,0,0.2)';

        let content = '<h6 class="mb-2">Crops</h6>';
        Object.entries(cropColors).forEach(([crop, color]) => {
            content += `
                <div class="mb-1">
                    <i style="background: ${color}; display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 5px;"></i>
                    ${crop}
                </div>
            `;
        });
        div.innerHTML = content;
        return div;
    };
    legend.addTo(map);
});

function exportToExcel() {
    $('.buttons-excel').click();
}
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
