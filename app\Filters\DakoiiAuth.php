<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class DakoiiAuth implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = \Config\Services::session();

        // Check if user is logged in to Dakoii system
        if (!$session->has('dakoii_logged_in') || !$session->get('dakoii_logged_in')) {
            return redirect()->to(base_url('dakoii/login'))->with('error', 'Please login to access Dakoii Admin Panel');
        }

        // Check if session has required data
        $requiredSessionVars = ['dakoii_user_id', 'dakoii_username', 'dakoii_role'];
        foreach ($requiredSessionVars as $var) {
            if (!$session->has($var)) {
                // Session is corrupted, destroy and redirect to login
                $session->destroy();
                return redirect()->to(base_url('dakoii/login'))->with('error', 'Session expired. Please login again.');
            }
        }

        // Optional: Check session timeout (24 hours)
        if ($session->has('dakoii_login_time')) {
            $loginTime = $session->get('dakoii_login_time');
            $currentTime = time();
            $sessionTimeout = 24 * 60 * 60; // 24 hours in seconds
            
            if (($currentTime - $loginTime) > $sessionTimeout) {
                $session->destroy();
                return redirect()->to(base_url('dakoii/login'))->with('error', 'Session expired. Please login again.');
            }
        }

        // Optional: Role-based access control
        if (isset($arguments[0])) {
            $requiredRole = $arguments[0];
            if ($session->get('dakoii_role') !== $requiredRole) {
                return redirect()->to(base_url('dakoii/ddash'))->with('error', 'Access denied. Insufficient privileges.');
            }
        }

        // Update last activity time
        $session->set('dakoii_last_activity', time());
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No after-filter needed for now
    }
}
