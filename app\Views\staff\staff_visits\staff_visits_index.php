<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Page Title -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Field Visits</li>
        </ol>
    </nav>
    <a href="<?= base_url('staff/extension/field-visits/new') ?>" class="btn btn-success">
        <i class="fas fa-plus-circle me-2"></i>New Field Visit
    </a>
</div>

<!-- Flash Messages -->
<?php if (session()->has('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= session('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (session()->has('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= session('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Field Visits List Card -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">Field Visits</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="fieldVisitsTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Location</th>
                        <th>Date Range</th>
                        <th>Purpose</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($visits)): ?>
                        <?php foreach ($visits as $index => $visit): ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td>
                                    <div>
                                        <?= esc($visit['llg_name'] ?? 'N/A') ?>
                                    </div>
                                    <?php
                                    // Display specific locations from the locations field
                                    $locations = [];
                                    if (!empty($visit['locations'])) {
                                        // Handle both string and already decoded JSON
                                        if (is_string($visit['locations'])) {
                                            $locationsArray = json_decode($visit['locations'], true);
                                            if (is_array($locationsArray)) {
                                                $locations = $locationsArray;
                                            }
                                        } elseif (is_array($visit['locations'])) {
                                            $locations = $visit['locations'];
                                        }
                                    }
                                    if (!empty($locations)):
                                    ?>
                                    <div class="mt-1 small">
                                        <strong>Specific Locations:</strong>
                                        <ul class="mb-0 ps-3">
                                            <?php foreach ($locations as $location): ?>
                                                <li><?= esc($location) ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= date('d M Y', strtotime($visit['date_start'])) ?> -
                                    <?= date('d M Y', strtotime($visit['date_end'])) ?>
                                </td>
                                <td><?= esc(substr($visit['purpose'], 0, 50)) . (strlen($visit['purpose']) > 50 ? '...' : '') ?></td>
                                <td>
                                    <span class="badge bg-<?= $visit['status'] == 1 ? 'success' : 'secondary' ?>">
                                        <?= $visit['status'] == 1 ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?= base_url('staff/extension/field-visits/' . $visit['id']) ?>"
                                           class="btn btn-sm btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('staff/extension/field-visits/' . $visit['id'] . '/edit') ?>"
                                           class="btn btn-sm btn-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="confirmDelete(<?= $visit['id'] ?>)" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this field visit? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="deleteLink" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        try {
            // Check if table has rows before initializing DataTable
            if ($('#fieldVisitsTable tbody tr').length > 0) {
                $('#fieldVisitsTable').DataTable({
                    "responsive": true,
                    "order": [[0, "asc"]],
                    "language": {
                        "emptyTable": "No field visits found"
                    }
                });
            } else {
                // If no rows, just add basic DataTable with message
                $('#fieldVisitsTable').DataTable({
                    "responsive": true,
                    "language": {
                        "emptyTable": "No field visits found"
                    }
                });
            }
        } catch (e) {
            console.error("DataTable initialization error:", e);
            // Fallback - don't initialize DataTable if there's an error
        }
    });

    function confirmDelete(id) {
        document.getElementById('deleteLink').href = '<?= base_url('staff/extension/field-visits') ?>/' + id + '/delete';
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
