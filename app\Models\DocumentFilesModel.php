<?php

namespace App\Models;

use CodeIgniter\Model;

class DocumentFilesModel extends Model
{
    protected $table            = 'document_files';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    protected $allowedFields = [
        'folder_id',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'description',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'folder_id'    => 'required|numeric',
        'file_name'    => 'required|min_length[2]|max_length[255]',
        'file_path'    => 'required|max_length[500]',
        'file_type'    => 'permit_empty|max_length[100]',
        'file_size'    => 'permit_empty|numeric',
        'status'       => 'permit_empty|numeric',
        'created_by'   => 'permit_empty|numeric'
    ];

    protected $validationMessages = [
        'folder_id' => [
            'required' => 'Folder ID is required',
            'numeric'  => 'Folder ID must be a number'
        ],
        'file_name' => [
            'required'   => 'File name is required',
            'min_length' => 'File name must be at least 2 characters long',
            'max_length' => 'File name cannot exceed 255 characters'
        ],
        'file_path' => [
            'required'   => 'File path is required',
            'max_length' => 'File path cannot exceed 500 characters'
        ]
    ];

    protected $skipValidation     = false;
    protected $cleanValidationRules = true;

    /**
     * Get files with folder information
     *
     * @return array
     */
    public function getFilesWithFolderInfo()
    {
        return $this->select('document_files.*, documents_folder.folder_name')
            ->join('documents_folder', 'documents_folder.id = document_files.folder_id')
            ->where('document_files.deleted_at IS NULL')
            ->findAll();
    }

    /**
     * Get files by folder ID
     *
     * @param int $folderId
     * @return array
     */
    public function getFilesByFolder($folderId)
    {
        return $this->where('folder_id', $folderId)
                    ->where('deleted_at IS NULL')
                    ->findAll();
    }

    /**
     * Get files by file type
     *
     * @param string $fileType
     * @return array
     */
    public function getFilesByType($fileType)
    {
        return $this->where('file_type', $fileType)
                    ->where('deleted_at IS NULL')
                    ->findAll();
    }
}
