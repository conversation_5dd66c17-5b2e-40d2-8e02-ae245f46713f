<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Dakoii Admin" content="This is Dakoii Admin Interface" />
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <!-- Toastr -->
    <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/toastr/toastr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootswatch@4.5.2/dist/solar/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- bs-custom-file-input -->
    <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <!-- Toastr -->
    <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/toastr/toastr.min.js"></script>

    <!-- Add DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
    
    <title><?= $title ?></title>

    <style>
        .nav-link {
            color: #ffffff !important;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            color: #17a2b8 !important;
            transform: translateX(5px);
        }
        .nav-link.active {
            color: #17a2b8 !important;
            font-weight: bold;
        }
        .nav-link i {
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        /* DataTables Custom Styling */
        .dataTables_wrapper {
            padding: 1rem;
        }
        
        .dataTables_filter {
            margin-bottom: 1rem;
        }
        
        .dataTables_length select {
            min-width: 65px;
        }
        
        table.dataTable {
            margin-top: 1rem !important;
            margin-bottom: 1rem !important;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0,0,0,.075);
        }
        
        .dataTables_info, .dataTables_paginate {
            margin-top: 1rem;
        }
        
        .page-link {
            color: #17a2b8;
        }
        
        .page-item.active .page-link {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }
    </style>

    <script>
        $(function() {
            bsCustomFileInput.init();
        });
    </script>

    <script>
        // Configure toastr options
        $(document).ready(function() {
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut",
                "preventDuplicates": true
            };
            
            // Show flash messages if they exist
            <?php if (session()->getFlashdata('success')): ?>
                toastr.success('<?= session()->getFlashdata('success') ?>', 'Success');
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                toastr.error('<?= session()->getFlashdata('error') ?>', 'Error');
            <?php endif; ?>

            <?php if (session()->getFlashdata('warning')): ?>
                toastr.warning('<?= session()->getFlashdata('warning') ?>', 'Warning');
            <?php endif; ?>

            <?php if (session()->getFlashdata('info')): ?>
                toastr.info('<?= session()->getFlashdata('info') ?>', 'Information');
            <?php endif; ?>
        });
    </script>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Brand Logo" width="30" height="30" class="d-inline-block align-text-top">
                <?= SYSTEM_NAME ?>
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mr-auto">
                    <!-- Dashboard -->
                    <li class="nav-item">
                        <a class="nav-link <?= ($menu == 'ddash') ? 'active' : '' ?>" href="<?= base_url('ddash') ?>">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    
                    <!-- Organizations -->
                    <li class="nav-item">
                        <a class="nav-link <?= ($menu == 'organizations') ? 'active' : '' ?>" href="<?= base_url('organizations') ?>">
                            <i class="fas fa-building"></i> Organizations
                        </a>
                    </li>
                    
                    <!-- Provinces -->
                    <li class="nav-item">
                        <a class="nav-link <?= ($menu == 'provinces') ? 'active' : '' ?>" href="<?= base_url('provinces') ?>">
                            <i class="fas fa-map-marker-alt"></i> Provinces
                        </a>
                    </li>
                </ul>
                
                <!-- Right side of navbar -->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link text-danger" href="<?= base_url('dlogout') ?>">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?= $this->renderSection('content') ?>
    </div>

    <footer class="footer bg-dark mt-4">
        <div class="container">
            <div class="row py-3">
                <div class="col-lg-12">
                    <p class="text-muted text-center mb-0">
                        &copy; 2024 <a href="https://www.dakoiims.com">Dakoii Systems</a>. <?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>

    <!-- Add DataTables JS before closing body tag -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap4.min.js"></script>

    
</body>
</html>