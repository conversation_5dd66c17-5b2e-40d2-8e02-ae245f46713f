<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmFertilizerDataModel extends Model
{
    protected $table = 'crops_farm_fertilizer_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'exercise_id',
        'block_id',
        'fertilizer_id',
        'crop_id',
        'name',
        'brand',
        'unit_of_measure',
        'unit',
        'quantity',
        'action_date',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $skipValidation = true;
    protected $cleanValidationRules = true;

    public function getFertilizerReportData()
    {
        try {
            $builder = $this->db->table($this->table);

            $result = $builder->select('
                    crops_farm_fertilizer_data.block_id,
                    crops_farm_blocks.block_code,
                    crops_farm_blocks.block_site,
                    crops_farm_blocks.status as block_status,
                    farmer_information.given_name,
                    farmer_information.surname,
                    adx_crops.crop_name,
                    adx_crops.crop_color_code,
                    IFNULL(adx_province.name, "Unknown") as province_name,
                    IFNULL(adx_district.name, "Unknown") as district_name,
                    IFNULL(adx_llg.name, "Unknown") as llg_name,
                    IFNULL(adx_ward.name, "Unknown") as ward_name,
                    IFNULL(adx_fertilizers.name, "Unknown") as fertilizer_name,
                    crops_farm_fertilizer_data.unit_of_measure,
                    crops_farm_fertilizer_data.quantity,
                    crops_farm_fertilizer_data.action_date,
                    crops_farm_fertilizer_data.status
                ')
                ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_fertilizer_data.block_id')
                ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
                ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
                ->join('adx_fertilizers', 'adx_fertilizers.id = crops_farm_fertilizer_data.fertilizer_id', 'left')
                ->join('adx_province', 'adx_province.id = crops_farm_blocks.province_id AND crops_farm_blocks.province_id > 0', 'left')
                ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id AND crops_farm_blocks.district_id > 0', 'left')
                ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id AND crops_farm_blocks.llg_id > 0', 'left')
                ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id AND crops_farm_blocks.ward_id > 0', 'left')
                ->where('crops_farm_fertilizer_data.status', 'active')
                ->where('crops_farm_blocks.status', 'active')
                ->orderBy('crops_farm_fertilizer_data.action_date', 'DESC')
                ->get()
                ->getResultArray();

            if (empty($result)) {
                log_message('error', 'No fertilizer data found in getFertilizerReportData');
                log_message('error', 'Last query: ' . $this->db->getLastQuery());
            }

            return $result;
        } catch (\Exception $e) {
            log_message('error', 'Error in getFertilizerReportData: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            throw $e;
        }
    }
}