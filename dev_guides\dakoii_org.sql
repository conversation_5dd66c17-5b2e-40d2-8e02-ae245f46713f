CREATE TABLE `dakoii_org` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orgcode` varchar(50) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `addlockprov` varchar(255) DEFAULT NULL,
  `addlockcountry` varchar(255) DEFAULT NULL,
  `orglogo` varchar(255) DEFAULT NULL,
  `is_locationlocked` tinyint(1) DEFAULT 0,
  `province_json` text DEFAULT NULL,
  `district_json` text DEFAULT NULL,
  `llg_json` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `license_status` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
