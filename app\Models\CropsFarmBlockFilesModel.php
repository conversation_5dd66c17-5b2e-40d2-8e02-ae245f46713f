<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * CropsFarmBlockFilesModel
 *
 * Model for managing crops_farm_block_files table
 */
class CropsFarmBlockFilesModel extends Model
{
    protected $table            = 'crops_farm_block_files';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    // Fields that can be set during insert/update
    protected $allowedFields    = [
        'exercise_id',
        'farm_block_id',
        'file_caption',
        'file_path',
        'uploaded_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true; // Now using CI's built-in timestamps
    protected $createdField  = 'uploaded_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'farm_block_id' => 'required|numeric',
        'file_caption'  => 'required|max_length[255]',
        'file_path'     => 'required|max_length[500]',
        'uploaded_by'   => 'required|numeric'
    ];

    protected $validationMessages = [
        'farm_block_id' => [
            'required' => 'Farm block ID is required',
            'numeric'  => 'Farm block ID must be a number'
        ],
        'file_caption' => [
            'required'   => 'File caption is required',
            'max_length' => 'File caption cannot exceed 255 characters'
        ],
        'file_path' => [
            'required'   => 'File path is required',
            'max_length' => 'File path cannot exceed 500 characters'
        ],
        'uploaded_by' => [
            'required' => 'Uploader ID is required',
            'numeric'  => 'Uploader ID must be a number'
        ]
    ];

    protected $skipValidation = false;
}