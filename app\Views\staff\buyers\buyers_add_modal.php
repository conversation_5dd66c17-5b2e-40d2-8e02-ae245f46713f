<!-- File Path: app/Views/staff/buyers/buyers_add_modal.php -->
<!-- Add Buyer Modal -->
<div class="modal fade" id="addBuyerModal" tabindex="-1" aria-labelledby="addBuyerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addBuyerModalLabel"><i class="fas fa-plus"></i> Add New Crop Buyer</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <!-- Form starts here -->
            <?= form_open('staff/buyers/add', ['id' => 'addBuyerForm']) ?>
            <div class="modal-body">
                <!-- Hidden CSRF Field -->
                <?= csrf_field() ?>
                
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="crop_id" class="form-label">Crop <span class="text-danger">*</span></label>
                        <select class="form-select" id="crop_id" name="crop_id" required>
                            <option value="">Select Crop...</option>
                            <?php if (!empty($crops)): ?>
                                <?php foreach ($crops as $crop): ?>
                                    <option value="<?= esc($crop['id']) ?>"><?= esc($crop['crop_name']) ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <!-- Placeholder for validation error -->
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Buyer Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                         <!-- Placeholder for validation error -->
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_number" class="form-label">Contact Number</label>
                        <input type="text" class="form-control" id="contact_number" name="contact_number">
                         <!-- Placeholder for validation error -->
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                         <!-- Placeholder for validation error -->
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="operation_span" class="form-label">Operation Span <span class="text-danger">*</span></label>
                        <select class="form-select" id="operation_span" name="operation_span" required>
                            <option value="">Select Operation Span...</option>
                            <option value="local">Local (Province Wide)</option>
                            <option value="national">National (Country Wide)</option>
                        </select>
                         <!-- Placeholder for validation error -->
                    </div>
                </div>

                <div class="mb-3">
                    <label for="address" class="form-label">Address</label>
                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                     <!-- Placeholder for validation error -->
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description / Notes</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                     <!-- Placeholder for validation error -->
                </div>

            </div> <!-- /modal-body -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success"><i class="fas fa-save"></i> Save Buyer</button>
            </div>
            <?= form_close() ?>
            <!-- Form ends here -->
        </div>
    </div>
</div>
<!-- /Add Buyer Modal --> 