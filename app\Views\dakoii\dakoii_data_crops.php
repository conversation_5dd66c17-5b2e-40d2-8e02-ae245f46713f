<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/data') ?>">Data Management</a></li>
        <li class="breadcrumb-item active">Crops</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Crops Management</h2>
        <p class="text-muted mb-0">Manage crop types and varieties for agricultural data collection</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/data') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Data
        </a>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCropModal">
            <i class="fas fa-plus"></i> Add Crop
        </button>
    </div>
</div>

<!-- Crops Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Total Crops</h6>
                        <h3 class="mb-0"><?= count($crops) ?></h3>
                        <small>Crop varieties available in the system</small>
                    </div>
                    <i class="fas fa-seedling fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Crops Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Crops List
            </h5>
            <input type="text" class="form-control form-control-sm" id="searchInput" 
                   placeholder="Search crops..." style="width: 250px;">
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="cropsTable">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>Icon</th>
                        <th>Crop Name</th>
                        <th>Color Code</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($crops)): ?>
                        <?php $i = 1; foreach ($crops as $crop): ?>
                        <tr>
                            <td><?= $i++ ?></td>
                            <td>
                                <?php if (!empty($crop['crop_icon'])): ?>
                                    <img src="<?= base_url($crop['crop_icon']) ?>" alt="Crop Icon" 
                                         class="rounded" style="width: 30px; height: 30px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                         style="width: 30px; height: 30px;">
                                        <i class="fas fa-seedling text-white"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="fw-medium"><?= esc($crop['crop_name']) ?></div>
                                <small class="text-muted">ID: <?= $crop['id'] ?></small>
                            </td>
                            <td>
                                <?php if (!empty($crop['crop_color_code'])): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 20px; height: 20px; background-color: <?= esc($crop['crop_color_code']) ?>; border: 1px solid #dee2e6;"></div>
                                        <code><?= esc($crop['crop_color_code']) ?></code>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">No color</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (!empty($crop['remarks'])): ?>
                                    <span title="<?= esc($crop['remarks']) ?>">
                                        <?= strlen($crop['remarks']) > 50 ? esc(substr($crop['remarks'], 0, 50)) . '...' : esc($crop['remarks']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">No remarks</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary edit-crop"
                                            data-id="<?= $crop['id'] ?>"
                                            data-name="<?= esc($crop['crop_name']) ?>"
                                            data-color="<?= esc($crop['crop_color_code']) ?>"
                                            data-remarks="<?= esc($crop['remarks']) ?>"
                                            data-bs-toggle="modal"
                                            data-bs-target="#editCropModal"
                                            title="Edit Crop">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-crop"
                                            data-id="<?= $crop['id'] ?>"
                                            data-name="<?= esc($crop['crop_name']) ?>"
                                            title="Delete Crop">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No crops found</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCropModal">
                                    <i class="fas fa-plus"></i> Add First Crop
                                </button>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Showing <span id="showingCount"><?= count($crops) ?></span> of <?= count($crops) ?> crops
            </small>
        </div>
    </div>
</div>

<!-- Add Crop Modal -->
<div class="modal fade" id="addCropModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> Add New Crop
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open_multipart('dakoii/data/crops', ['id' => 'addCropForm']) ?>
            <?= csrf_field() ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="crop_name" class="form-label">Crop Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="crop_name" name="crop_name" required>
                </div>

                <div class="mb-3">
                    <label for="crop_icon" class="form-label">Crop Icon</label>
                    <input type="file" class="form-control" id="crop_icon" name="crop_icon" accept="image/*">
                    <div class="form-text">Upload an icon for this crop (optional)</div>
                </div>

                <div class="mb-3">
                    <label for="crop_color_code" class="form-label">Color Code</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="crop_color_code" name="crop_color_code" value="#198754">
                        <input type="text" class="form-control" id="crop_color_text" placeholder="#198754" readonly>
                    </div>
                    <div class="form-text">Choose a color to represent this crop</div>
                </div>

                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Additional notes about this crop..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Add Crop
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Crop Modal -->
<div class="modal fade" id="editCropModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Crop
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCropForm" method="POST" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <input type="hidden" name="_method" value="PUT">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_crop_name" class="form-label">Crop Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_crop_name" name="crop_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_crop_icon" class="form-label">Crop Icon</label>
                        <input type="file" class="form-control" id="edit_crop_icon" name="crop_icon" accept="image/*">
                        <div class="form-text">Upload a new icon to replace the current one (optional)</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_crop_color_code" class="form-label">Color Code</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="edit_crop_color_code" name="crop_color_code">
                            <input type="text" class="form-control" id="edit_crop_color_text" readonly>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Crop
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteCropModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the crop "<strong id="deleteCropName"></strong>"?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteCropForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Crop
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.table td {
    vertical-align: middle;
}

.form-control-color {
    width: 3rem;
    height: calc(2.25rem + 2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('cropsTable');
    const tbody = table.querySelector('tbody');
    const showingCount = document.getElementById('showingCount');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        showingCount.textContent = visibleCount;
    });

    // Color picker sync for add modal
    const colorPicker = document.getElementById('crop_color_code');
    const colorText = document.getElementById('crop_color_text');
    
    colorPicker.addEventListener('input', function() {
        colorText.value = this.value;
    });

    // Color picker sync for edit modal
    const editColorPicker = document.getElementById('edit_crop_color_code');
    const editColorText = document.getElementById('edit_crop_color_text');
    
    editColorPicker.addEventListener('input', function() {
        editColorText.value = this.value;
    });

    // Handle edit crop button clicks
    document.querySelectorAll('.edit-crop').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;

            document.getElementById('edit_crop_name').value = name;
            document.getElementById('edit_crop_color_code').value = color || '#198754';
            document.getElementById('edit_crop_color_text').value = color || '#198754';
            document.getElementById('edit_remarks').value = remarks;

            // Update form action
            const form = document.getElementById('editCropForm');
            form.action = '<?= base_url('dakoii/data/crops/') ?>' + id;
        });
    });

    // Handle delete crop button clicks
    document.querySelectorAll('.delete-crop').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;

            document.getElementById('deleteCropName').textContent = name;
            
            // Update delete form action
            const deleteForm = document.getElementById('deleteCropForm');
            deleteForm.action = '<?= base_url('dakoii/data/crops/') ?>' + id;

            // Show delete modal
            const modal = new bootstrap.Modal(document.getElementById('deleteCropModal'));
            modal.show();
        });
    });

    // Initialize color text on page load
    colorText.value = colorPicker.value;
});
</script>

<?= $this->endSection() ?>
