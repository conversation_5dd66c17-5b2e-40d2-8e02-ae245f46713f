<?php

// File Path: app/Controllers/CropBuyersController.php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\CropBuyersModel;
use App\Models\CropsModel;

class CropBuyersController extends BaseController
{
    protected $cropBuyersModel;
    protected $cropsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->cropBuyersModel = new CropBuyersModel();
        $this->cropsModel = new CropsModel();

        // Verify staff role - Adjust role check as needed for buyer management specifically
        // if (session()->get('role') != "user") {
        //     echo 'Access denied';
        //     exit;
        // }
    }

    /**
     * Display the list of crop buyers.
     */
    public function index()
    {
        $data = [
            'title' => 'Crop Buyers',
            'page_header' => 'Crop Buyers Management',
            'buyers' => $this->cropBuyersModel
                ->select('crop_buyers.*, adx_crops.crop_name')
                ->join('adx_crops', 'adx_crops.id = crop_buyers.crop_id', 'left')
                // Removed status filter since the column might not exist yet
                ->orderBy('crop_buyers.name', 'ASC')
                ->findAll(),
            'crops' => $this->cropsModel->orderBy('crop_name', 'ASC')->findAll() // Fetch all crops for dropdowns
        ];

        // Load the main view which will include modals
        return view('staff/buyers/buyers_list', $data);
    }

    /**
     * Handle adding a new crop buyer via AJAX.
     */
    public function add()
    {
        if ($this->request->isAJAX()) {
            try {
                // Basic validation example (expand as needed)
                $validation = \Config\Services::validation();
                $rules = [
                    'crop_id' => 'required|integer',
                    'name' => 'required|min_length[3]',
                    'contact_number' => 'permit_empty|min_length[5]',
                    'email' => 'permit_empty|valid_email',
                    'operation_span' => 'required|in_list[local,national]',
                    'address' => 'permit_empty|max_length[255]',
                    'description' => 'permit_empty'
                ];

                if (!$this->validate($rules)) {
                     return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Validation failed',
                        'errors' => $validation->getErrors()
                    ])->setStatusCode(400); // Bad Request
                }

                // Generate buyer code
                $latestBuyer = $this->cropBuyersModel->orderBy('id', 'DESC')->first();
                $nextId = $latestBuyer ? ($latestBuyer['id'] + 1) : 1;
                $buyerCode = 'CB' . str_pad($nextId, 4, '0', STR_PAD_LEFT); // e.g., CB0001

                $data = [
                    'buyer_code' => $buyerCode,
                    'crop_id' => $this->request->getPost('crop_id'),
                    'name' => $this->request->getPost('name'),
                    'contact_number' => $this->request->getPost('contact_number'),
                    'email' => $this->request->getPost('email'),
                    'operation_span' => $this->request->getPost('operation_span'),
                    'address' => $this->request->getPost('address'),
                    'description' => $this->request->getPost('description'),
                    'created_by' => session()->get('emp_id') ?? 1, // Replace with actual logged-in user ID
                    'created_at' => date('Y-m-d H:i:s')
                    // Removed status field
                ];

                if ($this->cropBuyersModel->insert($data)) {
                    $newBuyerId = $this->cropBuyersModel->getInsertID();
                    // Fetch the newly added buyer data along with crop name
                    $newBuyerData = $this->cropBuyersModel
                        ->select('crop_buyers.*, adx_crops.crop_name')
                        ->join('adx_crops', 'adx_crops.id = crop_buyers.crop_id', 'left')
                        ->find($newBuyerId);

                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Buyer added successfully.',
                        'buyer' => $newBuyerData // Send back the new buyer data
                    ])->setStatusCode(201); // Created
                } else {
                    throw new \Exception('Failed to save buyer data.');
                }
            } catch (\Exception $e) {
                log_message('error', 'Error adding buyer: ' . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'An error occurred while adding the buyer. ' . $e->getMessage()
                ])->setStatusCode(500); // Internal Server Error
            }
        }
         // If not AJAX, redirect or show error
         return redirect()->to('/crop-buyers');
    }

    /**
     * Fetch a single buyer's details for editing via AJAX.
     */
    public function getBuyer($id)
    {
         if ($this->request->isAJAX()) {
            try {
                $buyer = $this->cropBuyersModel->find($id);

                if ($buyer) {
                    return $this->response->setJSON([
                        'status' => 'success',
                        'buyer' => $buyer
                    ]);
                } else {
                     return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Buyer not found.'
                    ])->setStatusCode(404); // Not Found
                }
            } catch (\Exception $e) {
                 log_message('error', 'Error fetching buyer: ' . $e->getMessage());
                 return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'An error occurred while fetching buyer details.'
                 ])->setStatusCode(500);
            }
        }
         return redirect()->to('/crop-buyers');
    }


    /**
     * Handle updating an existing crop buyer via AJAX.
     */
    public function update()
    {
        if ($this->request->isAJAX()) {
            try {
                $id = $this->request->getPost('edit_buyer_id'); // Ensure ID is sent from the form
                if (!$id) {
                     return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Buyer ID is missing.'
                    ])->setStatusCode(400);
                }

                // Basic validation example (expand as needed)
                 $validation = \Config\Services::validation();
                 $rules = [
                     'edit_crop_id' => 'required|integer',
                     'edit_name' => 'required|min_length[3]',
                     'edit_contact_number' => 'permit_empty|min_length[5]',
                     'edit_email' => 'permit_empty|valid_email',
                     'edit_operation_span' => 'required|in_list[local,national]',
                     'edit_address' => 'permit_empty|max_length[255]',
                     'edit_description' => 'permit_empty'
                 ];
                 // Use different field names prefixed with 'edit_' for validation
                 $postData = [
                    'edit_crop_id' => $this->request->getPost('edit_crop_id'),
                    'edit_name' => $this->request->getPost('edit_name'),
                    'edit_contact_number' => $this->request->getPost('edit_contact_number'),
                    'edit_email' => $this->request->getPost('edit_email'),
                    'edit_operation_span' => $this->request->getPost('edit_operation_span'),
                    'edit_address' => $this->request->getPost('edit_address'),
                    'edit_description' => $this->request->getPost('edit_description'),
                 ];


                 if (!$this->validate($rules, $postData)) {
                     return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Validation failed',
                        'errors' => $validation->getErrors()
                    ])->setStatusCode(400);
                 }

                $data = [
                    // Map edit_* fields back to model fields
                    'crop_id' => $this->request->getPost('edit_crop_id'),
                    'name' => $this->request->getPost('edit_name'),
                    'contact_number' => $this->request->getPost('edit_contact_number'),
                    'email' => $this->request->getPost('edit_email'),
                    'operation_span' => $this->request->getPost('edit_operation_span'),
                    'address' => $this->request->getPost('edit_address'),
                    'description' => $this->request->getPost('edit_description'),
                    'updated_by' => session()->get('emp_id') ?? 1, // Replace with actual logged-in user ID
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($this->cropBuyersModel->update($id, $data)) {
                     // Fetch the updated buyer data along with crop name
                     $updatedBuyerData = $this->cropBuyersModel
                        ->select('crop_buyers.*, adx_crops.crop_name')
                        ->join('adx_crops', 'adx_crops.id = crop_buyers.crop_id', 'left')
                        ->find($id);

                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Buyer updated successfully.',
                        'buyer' => $updatedBuyerData // Send back updated data
                    ]);
                } else {
                    // Check if the buyer exists or if the update simply failed
                    $exists = $this->cropBuyersModel->find($id);
                     return $this->response->setJSON([
                        'status' => 'error',
                        'message' => $exists ? 'Failed to update buyer data.' : 'Buyer not found.',
                    ])->setStatusCode($exists ? 500 : 404);
                }
            } catch (\Exception $e) {
                 log_message('error', 'Error updating buyer: ' . $e->getMessage());
                 return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'An error occurred while updating the buyer. ' . $e->getMessage()
                 ])->setStatusCode(500);
            }
        }
         return redirect()->to('/crop-buyers');
    }

    /**
     * Handle deleting a crop buyer via AJAX.
     * We'll perform a soft delete by updating the status.
     */
    public function delete($id = null)
    {
        if ($this->request->isAJAX()) {
            if (!$id) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Buyer ID is missing.'
                ])->setStatusCode(400);
            }

            try {
                // Find the buyer first to ensure it exists
                $buyer = $this->cropBuyersModel->find($id);
                if (!$buyer) {
                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Buyer not found.'
                    ])->setStatusCode(404);
                }

                // Perform a soft delete by updating timestamps only
                $data = [
                    // Removed status field
                    'updated_by' => session()->get('emp_id') ?? 1,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($this->cropBuyersModel->update($id, $data)) {
                // Or use $this->cropBuyersModel->delete($id) for hard delete if 'useSoftDeletes' is true in model
                // if ($this->cropBuyersModel->delete($id)) {
                    return $this->response->setJSON([
                        'status' => 'success',
                        'message' => 'Buyer deleted successfully.'
                    ]);
                } else {
                    throw new \Exception('Failed to delete buyer.');
                }
            } catch (\Exception $e) {
                log_message('error', 'Error deleting buyer: ' . $e->getMessage());
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'An error occurred while deleting the buyer. ' . $e->getMessage()
                ])->setStatusCode(500);
            }
        }
        return redirect()->to('/crop-buyers');
    }
}