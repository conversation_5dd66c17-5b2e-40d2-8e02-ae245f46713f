<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Farmers List</h5>
        <a href="<?= base_url('staff/farmers/create') ?>" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New Farmer
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="farmersTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Photo</th>
                        <th>Farmer Code</th>
                        <th>Name</th>
                        <th>Gender</th>
                        <th>Village</th>
                        <th>Phone</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($farmers as $index => $farmer): ?>
                    <tr>
                        <td><?= $index + 1 ?></td>
                        <td>
                            <?php if ($farmer['id_photo']): ?>
                                <img src="<?= imgcheck($farmer['id_photo']) ?>" 
                                     class="rounded-circle" width="40" height="40" alt="ID Photo">
                            <?php else: ?>
                                <img src="<?= base_url('public/assets/system_img/no-img.jpg') ?>" 
                                     class="rounded-circle" width="40" height="40" alt="No Photo">
                            <?php endif; ?>
                        </td>
                        <td><?= esc($farmer['farmer_code']) ?></td>
                        <td><?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></td>
                        <td><?= esc($farmer['gender']) ?></td>
                        <td><?= esc($farmer['village']) ?></td>
                        <td><?= esc($farmer['phone']) ?></td>
                        <td>
                            <span class="badge bg-<?= $farmer['status'] === 'active' ? 'success' : 'danger' ?>">
                                <?= esc($farmer['status']) ?>
                            </span>
                        </td>
                        <td>
                            <a href="<?= base_url('staff/farmers/view/' . $farmer['id']) ?>" 
                               class="btn btn-sm btn-info" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="<?= base_url('staff/farmers/edit/' . $farmer['id']) ?>" 
                               class="btn btn-sm btn-primary" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <!-- <a href="<?= base_url('staff/farmers/delete/' . $farmer['id']) ?>" 
                               class="btn btn-sm btn-danger" 
                               onclick="return confirm('Are you sure you want to delete this farmer?')"
                               title="Delete">
                                <i class="fas fa-trash"></i>
                            </a> -->
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#farmersTable').DataTable({
        "responsive": true,
        "order": [[0, "asc"]],
        "pageLength": 25
    });
});
</script>
<?= $this->endSection() ?> 