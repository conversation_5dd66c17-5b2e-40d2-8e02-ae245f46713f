<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Crops Pesticides Dashboard</h1>
            </div>
        </div>
    </div>
</div>

<div class="content">
<div class="container-fluid">
        <!-- Summary Cards Row -->
    <div class="row">
            <!-- Total Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="totalBlocksCount">0</h3>
                        <p>Total Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <!-- Total Pesticides Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3 id="totalPesticides">0</h3>
                        <p>Total Pesticide Types</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-spray-can"></i>
                    </div>
                </div>
            </div>
            <!-- Total Quantity Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3 id="totalQuantity">0</h3>
                        <p>Total Quantity</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-weight"></i>
                    </div>
                </div>
            </div>
            <!-- Total Farmers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3 id="totalFarmers">0</h3>
                        <p>Total Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pesticide Data Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Pesticide Usage Data</h3>
                </div>
                <div class="card-body">
                        <!-- Location Filters -->
                        <div id="filterContainer" class="mb-3 d-flex flex-wrap gap-2">
                            <!-- Filters will be inserted here by JavaScript -->
                        </div>
                        <div class="table-responsive">
                            <table id="pesticidesTable" class="table table-bordered text-nowrap table-striped">
                        <thead>
                            <tr>
                                        <th data-column-id="block_code">Block Code</th>
                                        <th data-column-id="farmer_name">Farmer Name</th>
                                        <th data-column-id="crop">Crop</th>
                                        <th data-column-id="pesticide">Pesticide</th>
                                        <th data-column-id="quantity">Total Quantity</th>
                                        <th data-column-id="province">Province</th>
                                        <th data-column-id="district">District</th>
                                        <th data-column-id="llg">LLG</th>
                                        <th data-column-id="ward">Ward</th>
                                        <th data-column-id="village">Village</th>
                                        <th data-column-id="action_date">Latest Action</th>
                                        <th data-column-id="remarks">Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                                    <?php foreach ($pesticideData as $data): ?>
                                    <tr>
                                        <td><?= $data['block_code'] ?></td>
                                        <td><?= trim($data['given_name'] . ' ' . $data['surname']) ?></td>
                                        <td><?= $data['crop_name'] ?></td>
                                        <td><?= $data['pesticide_name'] ?? 'N/A' ?></td>
                                        <td><?= number_format($data['total_quantity']) ?> <?= $data['unit_of_measure'] ?></td>
                                        <td><?= $data['province_name'] ?></td>
                                        <td><?= $data['district_name'] ?></td>
                                        <td><?= $data['llg_name'] ?></td>
                                        <td><?= $data['ward_name'] ?></td>
                                        <td><?= $data['block_site'] ?></td>
                                        <td><?= date('d/m/Y', strtotime($data['latest_action_date'])) ?></td>
                                        <td><?= $data['remarks'] ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

        <!-- Charts Row - Distribution -->
        <div class="row">
            <!-- Pesticide Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Pesticide Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="pesticideDistributionChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Crop Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Distribution by Pesticide Usage</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Location -->
        <div class="row">
            <!-- Location Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Location Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="locationChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Monthly Trend -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Monthly Pesticide Usage Trend</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
class FilterableTable {
    constructor(tableId) {
        this.table = document.getElementById(tableId);
        this.tableId = tableId;
        this.dataTable = $(`#${tableId}`).DataTable();
        this.filterContainer = document.getElementById('filterContainer');
        this.columnMap = {};
        this.filters = {
            crop: [],
            pesticide: [],
            province: [],
            district: [],
            llg: [],
            ward: []
        };
        
        // Initialize column map
        this.initColumnMap();
        this.init();

        // Add drawCallback after initialization
        this.dataTable.on('draw', () => {
            this.updateSummaryCards();
            this.initializeCharts();
            
            // Update dependent filters based on current selection
            const filterOrder = ['crop', 'pesticide', 'province', 'district', 'llg', 'ward'];
            filterOrder.forEach((filterId, index) => {
                if (index > 0 && this.filters[filterOrder[index - 1]].length > 0) {
                    this.updateFilterDropdown(filterId);
                }
            });
        });
    }

    initColumnMap() {
        const headers = this.table.querySelectorAll('th[data-column-id]');
        headers.forEach((header, index) => {
            this.columnMap[header.dataset.columnId] = index;
        });
    }

    init() {
        this.createLocationFilters();
        this.updateSummaryCards();
        this.initializeCharts();
    }

    createLocationFilters() {
        const locations = [
            { id: 'crop', label: 'Crop', columnId: 'crop' },
            { id: 'pesticide', label: 'Pesticide', columnId: 'pesticide' },
            { id: 'province', label: 'Province', columnId: 'province' },
            { id: 'district', label: 'District', columnId: 'district' },
            { id: 'llg', label: 'LLG', columnId: 'llg' },
            { id: 'ward', label: 'Ward', columnId: 'ward' }
        ];

        locations.forEach(location => {
            const filterGroup = this.createFilterDropdown(location);
            this.filterContainer.appendChild(filterGroup);
        });
    }

    createFilterDropdown(location) {
        const container = document.createElement('div');
        container.className = 'dropdown mr-2';
        container.innerHTML = `
            <button class="btn btn-default dropdown-toggle" type="button" 
                    data-toggle="dropdown" data-filter="${location.id}" aria-haspopup="true" aria-expanded="false">
                ${location.label} <span class="badge badge-light"></span>
            </button>
            <div class="dropdown-menu p-2" style="min-width: 250px; max-height: 300px; overflow-y: auto;" data-boundary="viewport">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" 
                           id="selectAll_${location.id}" data-filter="${location.id}">
                    <label class="custom-control-label" for="selectAll_${location.id}">Select All</label>
                </div>
                <hr class="my-2">
                <div id="options_${location.id}"></div>
            </div>
        `;

        // Prevent dropdown from closing when clicking inside
        container.querySelector('.dropdown-menu').addEventListener('click', (e) => {
            e.stopPropagation();
        });

        this.populateFilterOptions(location, container);
        return container;
    }

    populateFilterOptions(location, container) {
        const optionsContainer = container.querySelector(`#options_${location.id}`);
        const uniqueValues = new Set();
        const columnIndex = this.columnMap[location.columnId];
        
        // Get unique values from the column
        const columnData = this.dataTable
            .rows()
            .data()
            .toArray()
            .map(row => row[columnIndex])
            .filter(value => value);
        
        columnData.forEach(value => uniqueValues.add(value));

        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${location.id}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${location.id}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        this.setupFilterEvents(container, location.id);
    }

    setupFilterEvents(container, filterId) {
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        const checkboxes = container.querySelectorAll(`input[type="checkbox"][data-filter="${filterId}"]:not(#selectAll_${filterId})`);
        const button = container.querySelector('button');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (event) => {
                this.handleFilterChange(checkbox, filterId);
                this.updateBadgeCount(button, filterId);
                selectAll.checked = Array.from(checkboxes).every(cb => cb.checked);
                selectAll.indeterminate = Array.from(checkboxes).some(cb => cb.checked) && !selectAll.checked;
            });
        });

        selectAll.addEventListener('change', (event) => {
            checkboxes.forEach(cb => {
                if (!cb.disabled) {
                    cb.checked = selectAll.checked;
                    this.handleFilterChange(cb, filterId);
                }
            });
            this.updateBadgeCount(button, filterId);
            event.stopPropagation();
        });

        this.updateBadgeCount(button, filterId);
    }

    updateBadgeCount(button, filterId) {
        if (!button) return;
        
        const count = this.filters[filterId].length;
        const badge = button.querySelector('.badge');
        if (badge) {
            badge.textContent = count > 0 ? count : '';
            badge.style.display = count > 0 ? 'inline-block' : 'none';
        }
    }

    handleFilterChange(checkbox, filterId) {
        const value = checkbox.value;
        
        // Update filters array
        if (checkbox.checked) {
            if (!this.filters[filterId].includes(value)) {
                this.filters[filterId].push(value);
            }
        } else {
            this.filters[filterId] = this.filters[filterId].filter(v => v !== value);
        }

        // Reset dependent filters
        const filterOrder = ['crop', 'pesticide', 'province', 'district', 'llg', 'ward'];
        const currentIndex = filterOrder.indexOf(filterId);
        
        // Only reset filters that come after the current one
        if (currentIndex !== -1) {
            filterOrder.slice(currentIndex + 1).forEach(dependentFilterId => {
                this.filters[dependentFilterId] = [];
                this.resetFilterDropdown(dependentFilterId);
                this.updateFilterDropdown(dependentFilterId);
            });
        }

        // Apply filters to DataTable
        this.applyFilters();
    }

    resetFilterDropdown(filterId) {
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        if (!container) return;

        // Reset checkboxes
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = false;
            cb.disabled = false;
        });

        // Reset select all checkbox
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        if (selectAll) {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        }

        // Reset badge count
        const button = container.querySelector('button');
        this.updateBadgeCount(button, filterId);
    }

    updateFilterDropdown(filterId) {
        const filterOrder = ['crop', 'pesticide', 'province', 'district', 'llg', 'ward'];
        
        // Get all visible rows that match current filters
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data().toArray();
        
        // Get the current filter's container
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        const optionsContainer = container.querySelector(`#options_${filterId}`);
        
        // Clear existing options
        optionsContainer.innerHTML = '';
        
        // Get unique values for this filter that match parent filters
        const uniqueValues = new Set();
        
        visibleRows.forEach(row => {
            // Check if this row matches all parent filters
            const matchesParentFilters = filterOrder.slice(0, filterOrder.indexOf(filterId)).every(parentId => {
                const parentValues = this.filters[parentId];
                return parentValues.length === 0 || parentValues.includes(row[this.columnMap[parentId]]);
            });
            
            if (matchesParentFilters) {
                const value = row[this.columnMap[filterId]];
                if (value) uniqueValues.add(value);
            }
        });

        // Create new options
        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${filterId}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${filterId}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        // Reattach event listeners
        this.setupFilterEvents(container, filterId);
    }

    applyFilters() {
        // Clear existing search functions
        $.fn.dataTable.ext.search = [];

        // Add filter function
        $.fn.dataTable.ext.search.push((settings, searchData) => {
            if (settings.nTable.id !== this.tableId) return true;

            return Object.entries(this.filters).every(([key, values]) => {
                if (values.length === 0) return true;
                
                const columnIndex = this.columnMap[key];
                return values.includes(searchData[columnIndex]);
            });
        });

        // Redraw table and update UI
        this.dataTable.draw();
        this.updateSummaryCards();
        this.initializeCharts();
    }

    calculateSummaryData() {
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data();
        let totalQuantity = 0;
        const uniqueBlocks = new Set();
        const uniqueFarmers = new Set();
        const uniquePesticides = new Set();

        visibleRows.each(row => {
            const quantity = parseFloat(row[this.columnMap.quantity].split(' ')[0].replace(/[^\d.-]/g, '')) || 0;
            totalQuantity += quantity;
            uniqueBlocks.add(row[this.columnMap.block_code]);
            uniqueFarmers.add(row[this.columnMap.farmer_name]);
            uniquePesticides.add(row[this.columnMap.pesticide]);
        });

        return {
            totalBlocks: uniqueBlocks.size,
            totalQuantity,
            totalPesticides: uniquePesticides.size,
            totalFarmers: uniqueFarmers.size
        };
    }

    updateSummaryCards() {
        const summaryData = this.calculateSummaryData();
        
        const animateCounter = (element, value, decimals = 0) => {
            if (!element) return;
            
            const duration = 1000;
            const startValue = parseFloat(element.textContent.replace(/[^\d.-]/g, '')) || 0;
            const startTime = performance.now();
            
            const update = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentValue = startValue + (value - startValue) * progress;
                element.textContent = currentValue.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            };
            
            requestAnimationFrame(update);
        };

        animateCounter(document.getElementById('totalBlocksCount'), summaryData.totalBlocks);
        animateCounter(document.getElementById('totalPesticides'), summaryData.totalPesticides);
        animateCounter(document.getElementById('totalQuantity'), summaryData.totalQuantity, 2);
        animateCounter(document.getElementById('totalFarmers'), summaryData.totalFarmers);
    }

    initializeCharts() {
        const visibleData = this.dataTable.rows({ search: 'applied' }).data();
        const chartData = {
            pesticides: {},
            crops: {},
            locations: {},
            monthlyTrend: {}
        };

        visibleData.each(row => {
            const pesticide = row[this.columnMap.pesticide];
            const crop = row[this.columnMap.crop];
            const district = row[this.columnMap.district];
            const quantity = parseFloat(row[this.columnMap.quantity].split(' ')[0].replace(/[^\d.-]/g, '')) || 0;
            const date = row[this.columnMap.action_date];
            const month = new Date(date.split('/').reverse().join('-')).toLocaleString('default', { month: 'long', year: 'numeric' });

            chartData.pesticides[pesticide] = (chartData.pesticides[pesticide] || 0) + quantity;
            chartData.crops[crop] = (chartData.crops[crop] || 0) + quantity;
            chartData.locations[district] = (chartData.locations[district] || 0) + quantity;
            chartData.monthlyTrend[month] = (chartData.monthlyTrend[month] || 0) + quantity;
        });

        this.updateChart('pesticideDistributionChart', {
            type: 'pie',
            data: chartData.pesticides,
            title: 'Pesticide Distribution by Usage'
        });

        this.updateChart('cropDistributionChart', {
            type: 'pie',
            data: chartData.crops,
            title: 'Crop Distribution by Pesticide Usage'
        });

        this.updateChart('locationChart', {
            type: 'bar',
            data: chartData.locations,
            title: 'Location Distribution',
            yAxisLabel: 'Quantity Used'
        });

        const sortedMonthlyData = Object.fromEntries(
            Object.entries(chartData.monthlyTrend).sort((a, b) => {
                return new Date(a[0]) - new Date(b[0]);
            })
        );

        this.updateChart('monthlyTrendChart', {
            type: 'line',
            data: sortedMonthlyData,
            title: 'Monthly Pesticide Usage Trend',
            yAxisLabel: 'Quantity Used'
        });
    }

    updateChart(chartId, config) {
        const ctx = document.getElementById(chartId).getContext('2d');
        const chartInstance = Chart.getChart(ctx);
        if (chartInstance) {
            chartInstance.destroy();
        }

        const colors = [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 206, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
            'rgba(153, 102, 255, 0.2)',
            'rgba(255, 159, 64, 0.2)'
        ];
        
        const borderColors = colors.map(color => color.replace('0.2', '1'));

        new Chart(ctx, {
            type: config.type,
            data: {
                labels: Object.keys(config.data),
                datasets: [{
                    data: Object.values(config.data),
                    backgroundColor: config.type === 'pie' ? colors : colors[0],
                    borderColor: config.type === 'pie' ? borderColors : borderColors[0],
                    borderWidth: 1,
                    tension: config.type === 'line' ? 0.4 : 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: config.type === 'pie' ? 'right' : 'top'
                    },
                    title: {
                        display: true,
                        text: config.title
                    }
                },
                scales: (config.type === 'bar' || config.type === 'line') ? {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: config.yAxisLabel
                        }
                    }
                } : undefined
            }
        });
    }
}

$(document).ready(function() {
    // Initialize DataTable with all options
    const dataTable = $('#pesticidesTable').DataTable({
        responsive: false,
        lengthChange: true,
        autoWidth: false,
        buttons: ["copy", "excel", "pdf", "print"],
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'All']],
        order: [[10, 'desc']] // Sort by action_date column
    });

    // Move buttons to the correct container
    dataTable.buttons().container().appendTo('#pesticidesTable_wrapper .col-md-6:eq(0)');

    // Initialize FilterableTable
    new FilterableTable('pesticidesTable');
});
</script>

<style>
.card-body {
    min-height: 400px;
}
.dropdown {
    display: inline-block;
    margin-right: 1rem;
    margin-bottom: 1rem;
}
.dropdown-menu {
    padding: 10px;
}
.badge {
    margin-left: 5px;
}
.custom-control {
    margin-bottom: 5px;
}
.custom-control-input:disabled ~ .custom-control-label {
    opacity: 0.5;
}
.table-responsive {
    min-height: 400px;
}
.dt-buttons {
    margin-bottom: 1rem;
}
</style>
<?= $this->endSection() ?>
