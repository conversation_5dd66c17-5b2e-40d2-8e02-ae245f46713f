<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Locations Management</h2>
        <p class="text-muted mb-0">Manage geographical locations hierarchy</p>
    </div>
</div>

<!-- Location Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Countries</h6>
                        <h3 class="mb-0"><?= count($countries) ?></h3>
                    </div>
                    <i class="fas fa-globe fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Provinces</h6>
                        <h3 class="mb-0"><?= $provinces_count ?></h3>
                    </div>
                    <i class="fas fa-map fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Districts</h6>
                        <h3 class="mb-0"><?= $districts_count ?></h3>
                    </div>
                    <i class="fas fa-map-marked-alt fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">LLGs</h6>
                        <h3 class="mb-0"><?= $llgs_count ?></h3>
                    </div>
                    <i class="fas fa-map-marker-alt fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Navigation -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-compass"></i> Quick Navigation
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('dakoii/locations/provinces') ?>" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-map fa-2x mb-2"></i><br>
                            <strong>View Provinces</strong><br>
                            <small>Browse all provinces and their statistics</small>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="btn btn-outline-secondary btn-lg w-100" style="cursor: default;">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                            <strong>Location Analytics</strong><br>
                            <small>View detailed location statistics below</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Countries Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-globe"></i> Countries Overview
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($countries)): ?>
                    <div class="row">
                        <?php foreach ($countries as $country): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border">
                                <div class="card-body">
                                    <h6 class="card-title"><?= esc($country['name']) ?></h6>
                                    <p class="card-text">
                                        <small class="text-muted">Country Code: <?= esc($country['code'] ?? 'N/A') ?></small>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">ID: <?= $country['id'] ?></small>
                                        <span class="badge bg-primary"><?= $provinces_count ?> Provinces</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No countries found</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Province Statistics -->
<?php if (!empty($province_stats)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Province Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Province</th>
                                <th>Districts</th>
                                <th>LLGs</th>
                                <th>Wards</th>
                                <th>Total Locations</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($province_stats as $stat): ?>
                            <tr>
                                <td>
                                    <div class="fw-medium"><?= esc($stat['province']['name']) ?></div>
                                    <small class="text-muted">ID: <?= $stat['province']['id'] ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?= $stat['districts_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-success"><?= $stat['llgs_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= $stat['wards_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-dark"><?= $stat['total_locations'] ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('dakoii/locations/districts/' . $stat['province']['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" title="View Districts">
                                            <i class="fas fa-eye"></i> Districts
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Location Hierarchy Info -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sitemap"></i> Location Hierarchy
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Hierarchy Structure:</h6>
                        <ol class="list-group list-group-numbered">
                            <li class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">Countries</div>
                                    Top-level geographical divisions
                                </div>
                                <span class="badge bg-primary rounded-pill"><?= count($countries) ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">Provinces</div>
                                    Major administrative divisions within countries
                                </div>
                                <span class="badge bg-success rounded-pill"><?= $provinces_count ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">Districts</div>
                                    Administrative subdivisions within provinces
                                </div>
                                <span class="badge bg-warning rounded-pill"><?= $districts_count ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">Local Level Governments (LLGs)</div>
                                    Local administrative units within districts
                                </div>
                                <span class="badge bg-info rounded-pill"><?= $llgs_count ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">Wards</div>
                                    Smallest administrative units within LLGs
                                </div>
                                <span class="badge bg-secondary rounded-pill"><?= $wards_count ?></span>
                            </li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Navigation Tips:</h6>
                        <div class="alert alert-info">
                            <ul class="mb-0">
                                <li>Click on "View Provinces" to browse all provinces</li>
                                <li>Use the "Districts" button to view districts within a province</li>
                                <li>Navigate through the hierarchy to explore all locations</li>
                                <li>Each level shows statistics for its sub-locations</li>
                            </ul>
                        </div>
                        
                        <h6 class="text-muted mb-3">Total Summary:</h6>
                        <div class="alert alert-success">
                            <strong>Total Locations:</strong> <?= count($countries) + $provinces_count + $districts_count + $llgs_count + $wards_count ?><br>
                            <small>Across all administrative levels</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-lg {
    padding: 1rem 1.5rem;
}

.table td {
    vertical-align: middle;
}
</style>

<?= $this->endSection() ?>
