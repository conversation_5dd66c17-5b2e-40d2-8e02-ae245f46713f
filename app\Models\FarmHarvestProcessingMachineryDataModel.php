<?php

namespace App\Models;

use CodeIgniter\Model;

class FarmHarvestProcessingMachineryDataModel extends Model
{
    protected $table = 'farm_harvest_processing_machinery_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    
    protected $allowedFields = [
        'crop_id',
        'owner_type', //sme/farmer
        'owner_id',
        'machinery_code',
        'machinery_name',
        'machinery_type',
        'description',
        'machinery_conditions',
        'operation_issues',
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'ward_id',
        'lon',
        'lat',
        'inspection_date',
        'inspection_by',
        'remarks',
        'created_by',
        'created_at',
        'updated_by',
        'updated_at',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

   
} 