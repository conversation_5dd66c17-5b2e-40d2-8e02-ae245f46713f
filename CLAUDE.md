# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## System Overview

Agristats is a multi-portal agricultural data management system built on CodeIgniter 4.6.0. The application serves three distinct user types through separate portal interfaces:

- **Dakoii Portal** (`/dakoii/*`) - System administration and master data management
- **Admin Portal** (`/admin/*`) - Organization-level administration and oversight  
- **Staff Portal** (`/staff/*`) - Field data collection and farm management

## Development Commands

### Testing & Dependencies
```bash
composer test          # Run PHPUnit test suite
composer install       # Install dependencies
composer dump-autoload  # Regenerate autoloader
composer update        # Update dependencies
```

### CodeIgniter CLI (requires PHP in PATH)
```bash
./spark list           # Show available CLI commands
./spark serve          # Start development server (http://localhost:8080)
./spark serve --host=localhost --port=8080  # Custom host/port
./spark migrate        # Run database migrations
./spark migrate:rollback  # Rollback migrations
./spark db:seed        # Run database seeders
./spark make:controller # Create new controller
./spark make:model     # Create new model
./spark make:migration # Create new migration
```

### Development Server
```bash
# XAMPP Environment (Primary)
http://localhost/agristats_demo/

# Alternative using PHP built-in server
./spark serve --host=0.0.0.0 --port=8080
```

## Architecture & Code Organization

### Multi-Portal Structure
The system uses separate authentication and session management for each portal:

- **Dakoii**: System-wide control, organization management, master data (crops, fertilizers, livestock)
- **Admin**: Organization-specific user management and data oversight
- **Staff**: Field data collection, farmer management, farm operations

Each portal has independent authentication controllers (`DakoiiAuth`, `AdminAuth`, `Home`) and isolated routes with appropriate filters.

### RESTful Implementation
Controllers follow RESTful conventions with proper HTTP verb usage:

```php
// Standard RESTful pattern used throughout
GET    /resource           -> index()   // List resources
GET    /resource/create    -> create()  // Show create form
POST   /resource           -> store()   // Store new resource
GET    /resource/{id}      -> show($id) // Show specific resource
GET    /resource/{id}/edit -> edit($id) // Show edit form
PUT    /resource/{id}      -> update($id) // Update resource
DELETE /resource/{id}      -> destroy($id) // Delete resource
```

**Form Compatibility**: Most controllers include form compatibility routes using method spoofing:
```php
$routes->post('(:num)', 'Controller::update/$1');              // Form with _method=PUT
$routes->post('(:num)/delete', 'Controller::destroy/$1');      // Delete via form
```

### Key Controllers
- **DakoiiAuth/AdminAuth/Home** - Authentication for each portal
- **Staff/FarmerController** - Farmer management (RESTful pattern)
- **Staff/StaffFarmsController** - Farm management
- **Staff/CropsFarmBlocksController** - Crop farm blocks (RESTful CRUD)
- **Staff/LivestockFarmBlocksController** - Livestock farm blocks
- **Staff/Staff_Reports** - Agricultural reporting and analytics
- **DakoiiData** - Master data management (crops, fertilizers, pesticides, livestock)
- **DakoiiLocations** - Geographic hierarchy management with CSV import/export
- **AdminUsers/AdminGroups** - Organization-level user management

### Database Structure
Geographic hierarchy: `Country → Province → District → LLG → Ward`

**Master Data Tables** (managed via Dakoii portal):
- `adx_crops`, `adx_fertilizers`, `adx_pesticides`, `adx_livestock`, `adx_education`
- Geographic tables: `adx_country`, `adx_province`, `adx_district`, `adx_llg`, `adx_ward`
- Organizations: `dakoii_org`, `dakoii_users`

**Agricultural Data Structure** (organization-specific):
```
farmer_information
├── crops_farm_blocks
│   ├── crops_farm_crops_data
│   ├── crops_farm_fertilizer_data
│   ├── crops_farm_pesticides_data
│   ├── crops_farm_disease_data
│   ├── crops_farm_harvest_data
│   └── crops_farm_marketing_data
└── livestock_farm_blocks
    └── livestock_farm_data

Extension Activities:
├── field_visits
├── trainings
└── inputs
```

**User Management**:
- `users` (organization staff with role-based permissions)
- `permissions_user_districts` (district-level access control)
- `groupings` (hierarchical user groups)

## Development Guidelines

### File Organization
- Controllers: `app/Controllers/` (with subdirectories for Staff/, Admin/, etc.)
- Models: `app/Models/` (follow Entity + Model naming pattern)
- Views: `app/Views/` (organized by controller/feature)
- Routes: `app/Config/Routes.php` (grouped by portal)

### Authentication & Security
- Each portal has independent authentication (session isolation)
- Use filters for route protection: `AdminAuth`, `Auth` filters
- CSRF protection enabled by default
- All database interactions use Query Builder or Model methods

### RESTful Best Practices
When adding new controllers:
1. Implement standard CRUD methods (index, create, store, show, edit, update, destroy)
2. Use proper HTTP verbs in routes
3. Add form compatibility routes for HTML forms using method spoofing
4. Follow the existing naming conventions

### View Patterns
Views follow consistent patterns:
- List pages: `{feature}_index.php`
- Create forms: `{feature}_create.php`  
- Edit forms: `{feature}_edit.php`
- Detail pages: `{feature}_show.php`

### Data Models
Models extend `CodeIgniter\Model` and include:
- Proper table definitions and primary keys
- Validation rules in `$validationRules` 
- Relationship methods when needed
- Consistent naming (e.g., `FarmerInformationModel` for `farmer_information` table)
- Soft deletes enabled for most data models (`$useSoftDeletes = true`)
- Proper `$allowedFields` arrays for mass assignment protection

**Key Model Patterns**:
```php
protected $table = 'table_name';
protected $primaryKey = 'id';
protected $useSoftDeletes = true;
protected $allowedFields = [...];
protected $validationRules = [...];
```

## Key Features & Implementation Notes

### Multi-Organization Support
- Organizations are isolated through database design
- Users belong to specific organizations with role-based permissions
- Geographic data is shared but agricultural data is organization-specific

### Geographic Location Management
Implements full hierarchy with CRUD operations for each level. Import/export functionality available via CSV for bulk operations.

### Agricultural Data Collection
Comprehensive data collection system covering:
- Farmer demographics and contact information
- Farm blocks with GPS coordinates (`crops_farm_blocks`, `livestock_farm_blocks`)
- Crop planting, fertilizer application, pesticide use, and harvest data
- Disease and pest management records
- Livestock management and production records
- Market pricing and sales information
- Extension activities (field visits, trainings, inputs distribution)

**Exercise System**: Data collection is organized around `exercises` - time-bounded data collection periods with assigned officers and specific geographic coverage.

### Reporting System
Dashboard-style reporting with filtering capabilities across all agricultural data types. Reports are role-based and organization-specific.

## Development Environment

**Local Setup**: XAMPP environment
**Base URL**: `http://localhost/agristats_demo/`
**Database**: MySQL with comprehensive agricultural schema
**PHP Version**: 8.1+ required (CodeIgniter 4.6.0 compatibility)
**Framework**: CodeIgniter 4.6.0

**Key Environment Settings**:
- Public folder: `public/` (framework security pattern)
- Uploads: `public/uploads/` for user-generated content
- Database config: `app/Config/Database.php`

## Common Development Tasks

### Adding New RESTful Controllers
1. Create controller in appropriate namespace (`App\Controllers\Staff\`)
2. Implement standard RESTful methods (`index`, `create`, `store`, `show`, `edit`, `update`, `destroy`)
3. Add form compatibility routes with method spoofing for HTML forms
4. Create corresponding model with proper validation rules
5. Create views following naming pattern: `{controller}_index.php`, `{controller}_create.php`, etc.
6. Update routes in appropriate portal group with proper filters

### Portal-Specific Development
Each portal has distinct user flows and permissions:
- **Dakoii**: System-wide functionality and master data management
- **Admin**: Organization-level oversight, user management, and reporting  
- **Staff**: Field operations and data collection workflows

**Route Organization**: Routes are grouped by portal in `app/Config/Routes.php` with appropriate authentication filters (`dakoii_auth`, `admin_auth`, `auth`).

### Database Migrations
Use CodeIgniter 4 migration system for schema changes. Existing system has extensive relational structure - always verify foreign key constraints when modifying.

**Critical Relationships**:
- Geographic hierarchy: `adx_country` → `adx_province` → `adx_district` → `adx_llg` → `adx_ward`
- Agricultural data tied to organizations via `org_id`
- Farm blocks link farmers to geographic locations and crops/livestock
- Extension data uses JSON fields for flexible data structures

## Testing & Quality Assurance

- PHPUnit test suite configured with code coverage
- Follow existing patterns for controller and model testing
- Comprehensive development guides available in `/dev_guides/` directory
- RESTful compliance analysis and architecture documentation maintained