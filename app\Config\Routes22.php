<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

// Load the system's routing file first, so that the app and ENVIRONMENT
// can override as needed.
if (file_exists(SYSTEMPATH . 'Config/Routes.php')) {
	require SYSTEMPATH . 'Config/Routes.php';
}

/**
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(true);

/**
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'Home::index');
$routes->get('events', 'Home::events');
$routes->get('logout', 'Home::logout');
$routes->get('events', 'Home::events');
$routes->get('manage_events', 'Home::manage_events');
$routes->get('home', 'Home::home');
$routes->get('about', 'Home::about');
$routes->get('fullcal', 'Home::fullcal');
$routes->get('ycal', 'Home::ycal');
$routes->post('save_events', 'Home::save_events');
$routes->post('update_events', 'Home::update_events');

$routes->post('main/add_test', 'Home::add_test');

$routes->group('', ['filter' => 'datefilter'], function ($routes) {
	$routes->get('/', 'Main::index');
	$routes->get('main/index', 'Home::index');
	$routes->get('main/admin', 'Home::admin');
	$routes->get('admindash', 'Home::index');
});

/**
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (file_exists(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
	require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
