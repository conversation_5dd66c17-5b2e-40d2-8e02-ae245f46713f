# Dakoii System Admins Updates

## Overview
Updated the Dakoii System Admins functionality to align with the new database structure where:
- `role` enum only contains 'user' and 'guest' 
- Admin privileges are controlled by `is_admin = 1`
- Email field is now optional

## Database Structure Changes

### Users Table Structure
```sql
users (
    id int(11) unsigned NOT NULL AUTO_INCREMENT,
    sys_no int(20) NOT NULL,
    org_id int(11) NOT NULL,
    name varchar(255) NOT NULL,
    password varchar(255) NOT NULL,
    role enum('user','guest') NOT NULL DEFAULT 'user',  -- Only 'user' and 'guest'
    is_admin tinyint(5) DEFAULT NULL,                   -- Admin privileges flag
    is_supervisor tinyint(5) DEFAULT NULL,              -- Supervisor privileges flag
    position varchar(255) DEFAULT NULL,
    phone varchar(200) NOT NULL,
    email varchar(500) NOT NULL,                        -- Now optional in practice
    status tinyint(1) NOT NULL DEFAULT 0,
    -- ... other fields
)
```

## Controller Changes Made

### File: `app/Controllers/DakoiiSystemAdmins.php`

#### 1. Updated `index()` method
**Before:**
```php
->where('users.role', 'admin')
```

**After:**
```php
->where('users.is_admin', 1)
```

#### 2. Updated `store()` method
**Changes:**
- Made email validation optional
- Updated data array to use new structure

**Before:**
```php
if (empty($this->request->getPost('email'))) {
    session()->setFlashdata('error', 'Email is required');
    return redirect()->to('dakoii/system-admins/create');
}

$data = [
    // ...
    'email' => $this->request->getPost('email'),
    'role' => 'admin', // Fixed as admin
    // ...
];
```

**After:**
```php
// Check if email already exists (only if email is provided)
$email = trim($this->request->getPost('email'));
if (!empty($email)) {
    $existingUser = $this->usersModel->where('email', $email)->first();
    if ($existingUser) {
        session()->setFlashdata('error', 'Email already exists');
        return redirect()->to('dakoii/system-admins/create');
    }
}

$data = [
    // ...
    'email' => $email ?: '', // Use email if provided, otherwise empty string
    'role' => 'user', // Set role as user
    'is_admin' => 1, // Set is_admin flag to 1 for admin privileges
    // ...
];
```

#### 3. Updated `edit()` method
**Before:**
```php
->where('users.role', 'admin')
```

**After:**
```php
->where('users.is_admin', 1)
```

#### 4. Updated `update()` method
**Changes:**
- Made email validation optional
- Updated query condition
- Updated data array

**Before:**
```php
$admin = $this->usersModel->where('id', $id)
                         ->where('role', 'admin')
                         ->first();

$rules = [
    'email' => 'required|valid_email|max_length[500]',
    // ...
];

$data = [
    // ...
    'role' => 'admin', // Force role to admin for system administrators
    // ...
];
```

**After:**
```php
$admin = $this->usersModel->where('id', $id)
                         ->where('is_admin', 1)
                         ->first();

$rules = [
    'email' => 'permit_empty|valid_email|max_length[500]',
    // ...
];

// Manual email uniqueness check (only if email is provided)
$email = trim($this->request->getPost('email'));
if (!empty($email)) {
    $existingUser = $this->usersModel->where('email', $email)
                                    ->where('id !=', $id)
                                    ->first();
    if ($existingUser) {
        session()->setFlashdata('error', 'Email address is already in use by another user.');
        return redirect()->to('dakoii/system-admins/edit/' . $id)->withInput();
    }
}

$data = [
    // ...
    'email' => $email ?: '', // Use email if provided, otherwise empty string
    'role' => 'user', // Set role as user
    'is_admin' => 1, // Maintain admin privileges
    // ...
];
```

#### 5. Updated `storeForOrg()` method
**Changes:**
- Made email validation optional
- Updated data array to use new structure

**Before:**
```php
if (empty($this->request->getPost('email'))) {
    session()->setFlashdata('error', 'Email is required');
    return redirect()->to('dakoii/organizations/' . $orgId . '/admins/create');
}

$data = [
    // ...
    'email' => $this->request->getPost('email'),
    'role' => 'admin', // Fixed as admin
    // ...
];
```

**After:**
```php
// Check if email already exists (only if email is provided)
$email = trim($this->request->getPost('email'));
if (!empty($email)) {
    $existingUser = $this->usersModel->where('email', $email)->first();
    if ($existingUser) {
        session()->setFlashdata('error', 'Email already exists');
        return redirect()->to('dakoii/organizations/' . $orgId . '/admins/create');
    }
}

$data = [
    // ...
    'email' => $email ?: '', // Use email if provided, otherwise empty string
    'role' => 'user', // Set role as user (only 'user' and 'guest' are valid)
    'is_admin' => 1, // Set is_admin flag to 1 for admin privileges
    // ...
];
```

## View Changes Made

### File: `app/Views/dakoii/dakoii_system_admins_create.php`

#### Updated Email Field
**Before:**
```html
<label class="form-label">Email <span class="text-danger">*</span></label>
<input type="email" class="form-control" name="email"
       placeholder="Enter email address" value="<?= old('email') ?>">
<div class="form-text">Must be unique across the system</div>
```

**After:**
```html
<label class="form-label">Email <span class="text-muted">(Optional)</span></label>
<input type="email" class="form-control" name="email"
       placeholder="Enter email address (optional)" value="<?= old('email') ?>">
<div class="form-text">Must be unique across the system if provided</div>
```

## Model Compatibility

### File: `app/Models/UsersModel.php`
The model already supports the new structure with:
- `is_admin` field in `allowedFields` array
- `getAdminsByOrgId()` method using `is_admin = 1`
- `getActiveAdminsByOrgId()` method using `is_admin = 1`

## Impact Summary

### ✅ Fixed Issues
1. **Email Required Error**: Email is now optional during system admin creation
2. **Role Structure**: Updated to use `role = 'user'` and `is_admin = 1`
3. **Database Compatibility**: All queries now use `is_admin = 1` instead of `role = 'admin'`
4. **Form Validation**: Updated validation rules to make email optional
5. **User Interface**: Updated form labels to indicate email is optional

### 🔄 Behavior Changes
1. **Email Field**: Now optional instead of required
2. **Role Assignment**: System admins now have `role = 'user'` with `is_admin = 1`
3. **Query Logic**: All admin queries now use `is_admin = 1` filter
4. **Validation**: Email uniqueness only checked if email is provided

### 📋 Testing Checklist
- [ ] Create system admin without email
- [ ] Create system admin with email
- [ ] Verify admin privileges work with `is_admin = 1`
- [ ] Test email uniqueness validation when email is provided
- [ ] Verify admin listing shows users with `is_admin = 1`
- [ ] Test edit functionality with optional email
- [ ] Verify organization-specific admin creation works

## Route Testing
The route `http://localhost/agristats_demo/dakoii/system-admins/create` should now work without the "Email is required" error, allowing creation of system administrators with:
- `role = 'user'`
- `is_admin = 1`
- Optional email field

---

**Note**: These changes maintain backward compatibility while aligning with the new database structure where admin privileges are controlled by the `is_admin` flag rather than the role field.
