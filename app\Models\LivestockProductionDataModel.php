<?php

namespace App\Models;

use CodeIgniter\Model;

class LivestockProductionDataModel extends Model
{
    protected $table = 'livestock_production_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'exercise_id',
        'livestock_id',
        'item',
        'unit_of_measure',
        'unit',
        'price_per_unit',
        'cost_per_unit',
        'quantity',
        'clients',
        'action_date',
        'comments',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'livestock_id' => 'required|numeric',
        'item' => 'required|max_length[255]',
        'unit_of_measure' => 'required|max_length[50]',
        'unit' => 'required|decimal',
        'quantity' => 'permit_empty|numeric',
        'status' => 'permit_empty|in_list[active,inactive,deleted]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Get production data with livestock details
    public function getProductionDataWithDetails($conditions = [])
    {
        $builder = $this->builder();
        $builder->select('
            livestock_production_data.*,
            adx_livestock.livestock_name,
            adx_livestock.livestock_color_code
        ')
        ->join('adx_livestock', 'adx_livestock.id = livestock_production_data.livestock_id')
        ->where('livestock_production_data.status', 'active');

        // Apply conditions if any
        if (!empty($conditions)) {
            $builder->where($conditions);
        }

        return $builder->get()->getResultArray();
    }
}
