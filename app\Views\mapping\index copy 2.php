<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item active"><?= $page_header ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="card-title">Farm Blocks Distribution Map</h3>
                            <div class="d-flex align-items-center">
                                <select id="cropFilter" class="form-control mr-2" style="width: 200px;">
                                    <option value="all">All Crops</option>
                                    <?php foreach ($crops as $crop): ?>
                                        <option value="<?= $crop['value'] ?>"><?= esc($crop['item']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="map" style="height: 600px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?= $this->endSection() ?>

<?= $this->section('calendar') ?>
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<!-- Leaflet Extra Markers -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet-extra-markers/1.2.1/js/leaflet.extra-markers.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet-extra-markers/1.2.1/css/leaflet.extra-markers.min.css" />

<script>
    // Initialize the map
    var map = L.map('map').setView([-6.314993, 143.95555], 8);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: ' OpenStreetMap contributors'
    }).addTo(map);

    // Create icon mapping for crops
    var cropIcons = {
        <?php foreach ($crops as $crop): ?>
            '<?= $crop['value'] ?>': L.icon({
                iconUrl: '<?= base_url() ?><?= $crop['icons'] ?>',
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32]
            }),
        <?php endforeach; ?>
    };

    // Store all markers in an array
    var allMarkers = [];

    // Debug: Log all crop IDs
    console.log('Available crops:', <?= json_encode($crops) ?>);
    console.log('Farm blocks:', <?= json_encode($farm_blocks) ?>);

    // Add markers for each farm block
    <?php foreach ($farm_blocks as $block): ?>
        var marker = L.marker([<?= $block['lat'] ?>, <?= $block['lon'] ?>], {
            icon: cropIcons['<?= $block['crop_id'] ?>'] || L.icon({
                iconUrl: '<?= base_url() ?><?= $block['icons'] ?>',
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32]
            }),
            cropId: <?= json_encode($block['crop_id']) ?> // Store the crop_id for filtering
        }).bindPopup(
            '<div class="popup-content">' +
            '<h5 class="mb-2"><img src="<?= base_url() ?><?= $block['icons'] ?>" style="width: 24px; height: 24px; margin-right: 8px;"><?= esc($block['crop_name']) ?></h5>' +
            '<strong>Block Code:</strong> <?= esc($block['block_code']) ?><br>' +
            '<strong>Block Site:</strong> <?= esc($block['block_site']) ?><br>' +
            '<strong>Farmer:</strong> <?= esc($block['given_name']) ?> <?= esc($block['surname']) ?>' +
            '</div>'
        ).addTo(map);
        
        allMarkers.push(marker);
    <?php endforeach; ?>

    // Filter markers based on selected crop
    document.getElementById('cropFilter').addEventListener('change', function() {
        var selectedCrop = this.value;
        console.log('Selected crop:', selectedCrop);
        
        allMarkers.forEach(function(marker) {
            console.log('Comparing - Selected:', selectedCrop, 'Marker cropId:', marker.options.cropId);
            if (selectedCrop === 'all') {
                marker.addTo(map);
            } else if (marker.options.cropId === selectedCrop) {
                console.log('Match found - showing marker');
                marker.addTo(map);
            } else {
                console.log('No match - hiding marker');
                map.removeLayer(marker);
            }
        });

        // Update map bounds to fit visible markers
        var visibleMarkers = allMarkers.filter(function(marker) {
            return map.hasLayer(marker);
        });
        
        if (visibleMarkers.length > 0) {
            var bounds = L.latLngBounds(visibleMarkers.map(function(marker) {
                return marker.getLatLng();
            }));
            map.fitBounds(bounds);
        }
    });

    // Fit map bounds to markers
    var bounds = L.latLngBounds(allMarkers.map(m => m.getLatLng()));
    map.fitBounds(bounds);
</script>

<style>
.legend {
    padding: 6px 8px;
    font-size: 14px;
    background: white;
    box-shadow: 0 0 15px rgba(0,0,0,0.2);
    border-radius: 5px;
    line-height: 18px;
}
.legend i {
    width: 18px;
    height: 18px;
    text-align: center;
    margin-right: 8px;
    opacity: 0.7;
}
.legend span {
    display: inline-block;
    margin-right: 15px;
}
</style>
<?= $this->endSection() ?>
