<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/data') ?>">Data Management</a></li>
        <li class="breadcrumb-item active">Fertilizers</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Fertilizers Management</h2>
        <p class="text-muted mb-0">Manage fertilizer types and compositions for crop nutrition</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/data') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Data
        </a>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addFertilizerModal">
            <i class="fas fa-plus"></i> Add Fertilizer
        </button>
    </div>
</div>

<!-- Fertilizers Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Total Fertilizers</h6>
                        <h3 class="mb-0"><?= count($fertilizers) ?></h3>
                        <small>Fertilizer types available in the system</small>
                    </div>
                    <i class="fas fa-flask fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fertilizers Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Fertilizers List
            </h5>
            <input type="text" class="form-control form-control-sm" id="searchInput" 
                   placeholder="Search fertilizers..." style="width: 250px;">
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="fertilizersTable">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>Icon</th>
                        <th>Fertilizer Name</th>
                        <th>Color Code</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($fertilizers)): ?>
                        <?php $i = 1; foreach ($fertilizers as $fertilizer): ?>
                        <tr>
                            <td><?= $i++ ?></td>
                            <td>
                                <?php if (!empty($fertilizer['icon'])): ?>
                                    <img src="<?= base_url('public/' . $fertilizer['icon']) ?>" alt="Fertilizer Icon"
                                         class="rounded" style="width: 30px; height: 30px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-secondary rounded d-flex align-items-center justify-content-center"
                                         style="width: 30px; height: 30px;">
                                        <i class="fas fa-flask text-white"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="fw-medium"><?= esc($fertilizer['name']) ?></div>
                                <small class="text-muted">ID: <?= $fertilizer['id'] ?></small>
                            </td>
                            <td>
                                <?php if (!empty($fertilizer['color_code'])): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 20px; height: 20px; background-color: <?= esc($fertilizer['color_code']) ?>; border: 1px solid #dee2e6;"></div>
                                        <code><?= esc($fertilizer['color_code']) ?></code>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">No color</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (!empty($fertilizer['remarks'])): ?>
                                    <span title="<?= esc($fertilizer['remarks']) ?>">
                                        <?= strlen($fertilizer['remarks']) > 50 ? esc(substr($fertilizer['remarks'], 0, 50)) . '...' : esc($fertilizer['remarks']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">No remarks</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary edit-fertilizer"
                                            data-id="<?= $fertilizer['id'] ?>"
                                            data-name="<?= esc($fertilizer['name']) ?>"
                                            data-color="<?= esc($fertilizer['color_code']) ?>"
                                            data-remarks="<?= esc($fertilizer['remarks']) ?>"
                                            data-bs-toggle="modal"
                                            data-bs-target="#editFertilizerModal"
                                            title="Edit Fertilizer">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?= form_open('dakoii/data/fertilizers/delete/' . $fertilizer['id'], ['style' => 'display: inline;', 'onsubmit' => 'return confirm("Are you sure you want to delete this fertilizer?")']) ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Fertilizer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?= form_close() ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-flask fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No fertilizers found</p>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addFertilizerModal">
                                    <i class="fas fa-plus"></i> Add First Fertilizer
                                </button>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Showing <span id="showingCount"><?= count($fertilizers) ?></span> of <?= count($fertilizers) ?> fertilizers
            </small>
        </div>
    </div>
</div>

<!-- Add Fertilizer Modal -->
<div class="modal fade" id="addFertilizerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> Add New Fertilizer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open_multipart('dakoii/data/fertilizers/store', ['id' => 'addFertilizerForm']) ?>
            <?= csrf_field() ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Fertilizer Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>

                <div class="mb-3">
                    <label for="icon" class="form-label">Fertilizer Icon</label>
                    <input type="file" class="form-control" id="icon" name="icon" accept="image/*">
                    <div class="form-text">Upload an icon for this fertilizer (optional)</div>
                </div>

                <div class="mb-3">
                    <label for="color_code" class="form-label">Color Code</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="color_code" name="color_code" value="#198754">
                        <input type="text" class="form-control" id="color_text" placeholder="#198754" readonly>
                    </div>
                    <div class="form-text">Choose a color to represent this fertilizer</div>
                </div>

                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Additional notes about this fertilizer..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> Add Fertilizer
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Fertilizer Modal -->
<div class="modal fade" id="editFertilizerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Fertilizer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open_multipart('', ['id' => 'editFertilizerForm']) ?>
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Fertilizer Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_icon" class="form-label">Fertilizer Icon</label>
                        <input type="file" class="form-control" id="edit_icon" name="icon" accept="image/*">
                        <div class="form-text">Upload a new icon to replace the current one (optional)</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_color_code" class="form-label">Color Code</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="edit_color_code" name="color_code">
                            <input type="text" class="form-control" id="edit_color_text" readonly>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Update Fertilizer
                    </button>
                </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.table td {
    vertical-align: middle;
}

.form-control-color {
    width: 3rem;
    height: calc(2.25rem + 2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('fertilizersTable');
    const tbody = table.querySelector('tbody');
    const showingCount = document.getElementById('showingCount');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        showingCount.textContent = visibleCount;
    });

    // Color picker sync for add modal
    const colorPicker = document.getElementById('color_code');
    const colorText = document.getElementById('color_text');

    colorPicker.addEventListener('input', function() {
        colorText.value = this.value;
    });

    // Color picker sync for edit modal
    const editColorPicker = document.getElementById('edit_color_code');
    const editColorText = document.getElementById('edit_color_text');

    editColorPicker.addEventListener('input', function() {
        editColorText.value = this.value;
    });

    // Handle edit fertilizer button clicks
    document.querySelectorAll('.edit-fertilizer').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;

            document.getElementById('edit_name').value = name;
            document.getElementById('edit_color_code').value = color || '#198754';
            document.getElementById('edit_color_text').value = color || '#198754';
            document.getElementById('edit_remarks').value = remarks;

            // Update form action
            const form = document.getElementById('editFertilizerForm');
            form.action = '<?= base_url('dakoii/data/fertilizers/update/') ?>' + id;
        });
    });

    // Initialize color text on page load
    colorText.value = colorPicker.value;
});
</script>

<?= $this->endSection() ?>
