<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item active"><?= $page_header ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="card-title">Farm Blocks Distribution Map</h3>
                            <div class="d-flex align-items-center">
                                <select id="cropFilter" class="form-control mr-2" style="width: 200px;">
                                    <option value="all">All Crops</option>
                                    <?php foreach ($crops as $crop): ?>
                                        <option value="<?= $crop['id'] ?>"><?= esc($crop['item']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="map" style="height: 600px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?= $this->endSection() ?>

<?= $this->section('calendar') ?>
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<!-- Leaflet Extra Markers -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet-extra-markers/1.2.1/js/leaflet.extra-markers.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet-extra-markers/1.2.1/css/leaflet.extra-markers.min.css" />

<style>
    .custom-marker-icon {
        width: 40px !important;
        height: 40px !important;
        background: white;
        border: 3px solid #2196F3;
        border-radius: 50%;
        display: flex !important;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .custom-marker-icon::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 10px solid #2196F3;
    }

    .custom-marker-icon img {
        width: 24px;
        height: 24px;
        position: absolute;
        top: 5px;
        left: 5px;
    }

    .popup-content {
        padding: 10px;
    }

    .popup-content h5 {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .popup-content img {
        margin-right: 8px;
    }
</style>

<script>
    // Initialize the map
    var map = L.map('map').setView([-6.314993, 143.95555], 8);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: ' OpenStreetMap contributors'
    }).addTo(map);

    // Function to create custom marker icon HTML
    function createCustomMarkerIcon(iconUrl) {
        var customIcon = L.divIcon({
            className: 'custom-marker-icon',
            html: '<img src="' + iconUrl + '" alt="Crop Icon"/>',
            iconSize: [40, 40],
            iconAnchor: [20, 40],
            popupAnchor: [0, -40]
        });
        return customIcon;
    }

    // Store all markers in an array
    var allMarkers = [];

    // Debug: Log all crop IDs
    console.log('Available crops:', <?= json_encode($crops) ?>);
    console.log('Farm blocks:', <?= json_encode($farm_blocks) ?>);

    // Add markers for each farm block
    <?php foreach ($farm_blocks as $block): ?>
        var marker = L.marker([<?= $block['lat'] ?>, <?= $block['lon'] ?>], {
            icon: createCustomMarkerIcon('<?= base_url() ?><?= $block['icons'] ?>'),
            cropId: <?= json_encode($block['crop_id']) ?>
        }).bindPopup(
            '<div class="popup-content">' +
            '<h5><img src="<?= base_url() ?><?= $block['icons'] ?>" style="width: 24px; height: 24px; margin-right: 8px;"><?= esc($block['crop_name']) ?></h5>' +
            '<strong>Block Code:</strong> <?= esc($block['block_code']) ?><br>' +
            '<strong>Block Site:</strong> <?= esc($block['block_site']) ?><br>' +
            '<strong>Farmer:</strong> <?= esc($block['given_name']) ?> <?= esc($block['surname']) ?>' +
            '</div>'
        ).addTo(map);
        
        allMarkers.push(marker);
    <?php endforeach; ?>

    // Filter markers based on selected crop
    document.getElementById('cropFilter').addEventListener('change', function() {
        var selectedCrop = this.value;
        
        allMarkers.forEach(function(marker) {
            if (selectedCrop === 'all') {
                marker.addTo(map);
            } else if (marker.options.cropId === selectedCrop) {
                marker.addTo(map);
            } else {
                map.removeLayer(marker);
            }
        });

        // Update map bounds to fit visible markers
        var visibleMarkers = allMarkers.filter(function(marker) {
            return map.hasLayer(marker);
        });
        
        if (visibleMarkers.length > 0) {
            var bounds = L.latLngBounds(visibleMarkers.map(function(marker) {
                return marker.getLatLng();
            }));
            map.fitBounds(bounds);
        }
    });

    // Fit map bounds to markers
    var bounds = L.latLngBounds(allMarkers.map(m => m.getLatLng()));
    map.fitBounds(bounds);
</script>
<?= $this->endSection() ?>
