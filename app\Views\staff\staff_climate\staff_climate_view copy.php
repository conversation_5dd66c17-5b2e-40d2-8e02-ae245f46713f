<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= $page_header ?? 'Climate Focus Details' ?></h5>
                <div>
                    <a href="<?= base_url('staff/tools/climate-data/' . $climate_focus['id'] . '/edit') ?>" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i> Edit
                    </a>
                    <a href="<?= base_url('staff/tools/climate-data') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">ID</th>
                                <td><?= $climate_focus['id'] ?></td>
                            </tr>
                            <tr>
                                <th>Location</th>
                                <td><?= esc($climate_focus['location']) ?></td>
                            </tr>
                            <tr>
                                <th>GPS Coordinates</th>
                                <td><?= esc($climate_focus['gps']) ?></td>
                            </tr>
                            <tr>
                                <th>District</th>
                                <td><?= esc($climate_focus['district_name']) ?></td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    <?php if ($climate_focus['status'] == 1): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Created At</th>
                                <td><?= date('d M Y H:i', strtotime($climate_focus['created_at'])) ?></td>
                            </tr>
                            <tr>
                                <th>Created By</th>
                                <td><?= $climate_focus['created_by'] ?></td>
                            </tr>
                            <?php if ($climate_focus['updated_at'] && $climate_focus['updated_at'] != $climate_focus['created_at']): ?>
                            <tr>
                                <th>Updated At</th>
                                <td><?= date('d M Y H:i', strtotime($climate_focus['updated_at'])) ?></td>
                            </tr>
                            <tr>
                                <th>Updated By</th>
                                <td><?= $climate_focus['updated_by'] ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <th>Remarks</th>
                                <td><?= nl2br(esc($climate_focus['remarks'] ?? 'No remarks')) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Location Map</h5>
                            </div>
                            <div class="card-body">
                                <div id="map" style="height: 400px; width: 100%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Climate Data (Last 6 Months)</h5>
                                <div class="spinner-border text-primary d-none" id="weatherSpinner" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info mb-4" id="weatherInfo">
                                    <i class="fas fa-info-circle me-2"></i> Loading climate data for this location...
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">Temperature (°C)</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="temperatureChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0">Precipitation (mm)</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="precipitationChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">Wind Speed (km/h)</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="windSpeedChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0">Cloud Cover (%)</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="cloudCoverChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-secondary text-white">
                                                <h6 class="mb-0">Humidity (%)</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="humidityChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header bg-dark text-white">
                                        <h6 class="mb-0">Monthly Climate Summary</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="weatherTable">
                                                <thead>
                                                    <tr>
                                                        <th>Month</th>
                                                        <th>Temperature (°C)</th>
                                                        <th>Precipitation (mm)</th>
                                                        <th>Wind Speed (km/h)</th>
                                                        <th>Cloud Cover (%)</th>
                                                        <th>Humidity (%)</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Will be populated by JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <!-- Climate Elements Summary Card -->
                                        <div class="card mt-4">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">Climate Elements Summary (6-Month Period)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered" id="summaryTable">
                                                        <thead>
                                                            <tr>
                                                                <th>Climate Element</th>
                                                                <th>Average</th>
                                                                <th>High</th>
                                                                <th>Low</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <!-- Will be populated by JavaScript -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                                
                                                <!-- Extreme Weather Events Table -->
                                                <div class="card mt-4">
                                                    <div class="card-header bg-danger text-white">
                                                        <h6 class="mb-0">Extreme Weather Events</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="table-responsive">
                                                            <table class="table table-striped table-bordered" id="extremeEventsTable">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Climate Element</th>
                                                                        <th>Highest Value</th>
                                                                        <th>Date Occurred</th>
                                                                        <th>Lowest Value</th>
                                                                        <th>Date Occurred</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <!-- Will be populated by JavaScript -->
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Leaflet CSS and JS for OpenStreetMap -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<!-- Chart.js for climate data charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Load Chart.js annotation plugin -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.0/dist/chartjs-plugin-annotation.min.js"></script>

<script>
    // Initialize the map using Leaflet and OpenStreetMap
    function initMap() {
        // Parse GPS coordinates
        const gpsString = "<?= $climate_focus['gps'] ?>".trim();
        const parts = gpsString.split(',');
        let lat = -6.314993; // Default latitude (PNG)
        let lng = 143.95555; // Default longitude (PNG)

        // Process the coordinate
        if (parts.length >= 2) {
            const parsedLat = parseFloat(parts[0].trim());
            const parsedLng = parseFloat(parts[1].trim());
            if (!isNaN(parsedLat) && !isNaN(parsedLng)) {
                lat = parsedLat;
                lng = parsedLng;
            }
        }

        // Create map centered on the coordinate
        const map = L.map('map').setView([lat, lng], 12);

        // Add OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add marker for the coordinate
        L.marker([lat, lng])
            .addTo(map)
            .bindPopup("<?= esc($climate_focus['location']) ?>")
            .openPopup();

        // Fetch climate data after map is initialized
        fetchClimateData(lat, lng);
    }

    // Weather code descriptions
    const weatherCodeDescriptions = {
        0: "Clear sky",
        1: "Mainly clear",
        2: "Partly cloudy",
        3: "Overcast",
        45: "Fog",
        48: "Depositing rime fog",
        51: "Light drizzle",
        53: "Moderate drizzle",
        55: "Dense drizzle",
        56: "Light freezing drizzle",
        57: "Dense freezing drizzle",
        61: "Slight rain",
        63: "Moderate rain",
        65: "Heavy rain",
        66: "Light freezing rain",
        67: "Heavy freezing rain",
        71: "Slight snow fall",
        73: "Moderate snow fall",
        75: "Heavy snow fall",
        77: "Snow grains",
        80: "Slight rain showers",
        81: "Moderate rain showers",
        82: "Violent rain showers",
        85: "Slight snow showers",
        86: "Heavy snow showers",
        95: "Thunderstorm",
        96: "Thunderstorm with slight hail",
        99: "Thunderstorm with heavy hail"
    };

    // Chart instances
    let temperatureChart, precipitationChart, windSpeedChart, cloudCoverChart, humidityChart;

    // Fetch climate data from Open-Meteo API
    async function fetchClimateData(lat, lng) {
        try {
            // Show loading state
            document.getElementById('weatherSpinner').classList.remove('d-none');
            document.getElementById('weatherInfo').classList.remove('d-none');
            document.getElementById('weatherInfo').innerHTML = '<i class="fas fa-info-circle me-2"></i> Loading climate data for this location...';

            // Calculate dates for the past 6 months - with validation to prevent future dates
            const today = new Date();
            // Check if the system date might be incorrectly set in the future
            const referenceDate = new Date();
            const currentYear = referenceDate.getFullYear();
            if (currentYear > 2025) {
                // System date is likely wrong, use a fixed recent date instead
                console.warn('System date may be incorrect. Using a fixed recent date.');
                referenceDate.setFullYear(2024);
                referenceDate.setMonth(4); // May
                referenceDate.setDate(1);
            }
            
            // Calculate end date (today or a fixed date if system date is incorrect)
            // Make sure we don't request future data beyond today's actual date
            const actualToday = new Date();
            const maxYear = Math.min(referenceDate.getFullYear(), actualToday.getFullYear());
            const endDateObj = new Date(actualToday);
            endDateObj.setFullYear(maxYear);
            // If we're in the same year, make sure we don't go beyond the current month
            if (maxYear === actualToday.getFullYear()) {
                endDateObj.setMonth(Math.min(referenceDate.getMonth(), actualToday.getMonth()));
                endDateObj.setDate(Math.min(referenceDate.getDate(), actualToday.getDate()));
            }
            const endDate = endDateObj.toISOString().split('T')[0];
            
            // Calculate start date (6 months before end date)
            const sixMonthsAgo = new Date(endDateObj);
            sixMonthsAgo.setMonth(endDateObj.getMonth() - 6);
            const startDate = sixMonthsAgo.toISOString().split('T')[0];

            // Log parameters for debugging
            console.log('Climate data request parameters:', { lat, lng, startDate, endDate });

            // Try different API endpoints in sequence
            let response = null;
            let data = null;
            let url = null;
            
            // First try: Combined hourly and daily data
            try {
                url = `https://archive-api.open-meteo.com/v1/archive?latitude=${encodeURIComponent(lat)}&longitude=${encodeURIComponent(lng)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}&hourly=temperature_2m,relative_humidity_2m,precipitation,windspeed_10m,cloudcover,weather_code&daily=temperature_2m_max,temperature_2m_min,precipitation_sum,windspeed_10m_max,cloudcover_mean,relativehumidity_2m_mean&timezone=auto`;
                console.log('Climate data API URL (first attempt):', url);
                
                response = await fetch(url, { timeout: 10000 });
                if (response.ok) {
                    data = await response.json();
                }
            } catch (firstError) {
                console.warn('First API attempt failed:', firstError);
            }
            
            // Second try: Only hourly data (fallback)
            if (!data) {
                try {
                    url = `https://archive-api.open-meteo.com/v1/archive?latitude=${encodeURIComponent(lat)}&longitude=${encodeURIComponent(lng)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}&hourly=temperature_2m,relative_humidity_2m,precipitation,windspeed_10m,cloudcover,weather_code&timezone=auto`;
                    console.log('Climate data API URL (second attempt):', url);
                    
                    response = await fetch(url, { timeout: 10000 });
                    if (response.ok) {
                        data = await response.json();
                    }
                } catch (secondError) {
                    console.warn('Second API attempt failed:', secondError);
                }
            }
            
            // Third try: Only daily data (original approach)
            if (!data) {
                try {
                    url = `https://archive-api.open-meteo.com/v1/archive?latitude=${encodeURIComponent(lat)}&longitude=${encodeURIComponent(lng)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}&daily=temperature_2m_max,temperature_2m_min,precipitation_sum,windspeed_10m_max,cloudcover_mean,relativehumidity_2m_mean,weather_code&timezone=auto`;
                    console.log('Climate data API URL (third attempt):', url);
                    
                    response = await fetch(url, { timeout: 10000 });
                    if (response.ok) {
                        data = await response.json();
                    }
                } catch (thirdError) {
                    console.warn('Third API attempt failed:', thirdError);
                }
            }
            
            // Fourth try: Alternative endpoint (forecast API with past days)
            if (!data) {
                try {
                    const pastDays = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));
                    url = `https://api.open-meteo.com/v1/forecast?latitude=${encodeURIComponent(lat)}&longitude=${encodeURIComponent(lng)}&past_days=${Math.min(pastDays, 92)}&daily=temperature_2m_max,temperature_2m_min,precipitation_sum,windspeed_10m_max,weathercode&timezone=auto`;
                    console.log('Climate data API URL (fourth attempt):', url);
                    
                    response = await fetch(url, { timeout: 10000 });
                    if (response.ok) {
                        data = await response.json();
                        // Rename weathercode to weather_code for consistency
                        if (data.daily && data.daily.weathercode) {
                            data.daily.weather_code = data.daily.weathercode;
                            delete data.daily.weathercode;
                        }
                    }
                } catch (fourthError) {
                    console.warn('Fourth API attempt failed:', fourthError);
                }
            }
            
            // If all attempts failed, throw error
            if (!data) {
                throw new Error("All API endpoints failed to respond. Please try again later.");
            }

            console.log('Climate data API response:', data);

            if (data.hourly && data.hourly.time && data.daily && data.daily.time) {
                // Combined hourly and daily data
                document.getElementById('weatherInfo').classList.add('d-none');
                const processedData = processHourlyToDaily(data.hourly, data.daily);
                updateCharts(processedData);
                populateWeatherTable(processedData);
            } else if (data.hourly && data.hourly.time) {
                // Only hourly data
                document.getElementById('weatherInfo').classList.add('d-none');
                const processedData = processOnlyHourlyData(data.hourly);
                updateCharts(processedData);
                populateWeatherTable(processedData);
            } else if (data.daily && data.daily.time) {
                // Only daily data
                document.getElementById('weatherInfo').classList.add('d-none');
                updateCharts(data.daily);
                populateWeatherTable(data.daily);
            } else {
                // Handle case where data structure is not as expected
                let errorMsg = "Failed to load climate data: Invalid response format.";
                if (data && data.error) {
                    errorMsg = `Failed to load climate data: ${data.reason || data.error}`;
                }
                console.error('Invalid API response format:', data);
                showChartError(errorMsg);
            }
        } catch (error) {
            console.error('Error fetching climate data:', error);
            showChartError(`Error fetching climate data: ${error.message}`);
        } finally {
            // Hide loading spinner
            document.getElementById('weatherSpinner').classList.add('d-none');
        }
    }

    // Process hourly data to daily format
    function processHourlyToDaily(hourlyData, dailyData) {
        // We'll use the daily data that's already provided by the API
        // and supplement with any data we need to calculate from hourly
        return {
            time: dailyData.time,
            temperature_2m_max: dailyData.temperature_2m_max,
            temperature_2m_min: dailyData.temperature_2m_min,
            precipitation_sum: dailyData.precipitation_sum,
            windspeed_10m_max: dailyData.windspeed_10m_max,
            cloudcover_mean: dailyData.cloudcover_mean,
            relativehumidity_2m_mean: dailyData.relativehumidity_2m_mean,
            // Extract most common weather code for each day
            weather_code: extractDailyWeatherCodes(hourlyData.time, hourlyData.weather_code)
        };
    }

    // Extract most common weather code for each day from hourly data
    function extractDailyWeatherCodes(hourlyTimes, weatherCodes) {
        const dailyWeatherCodes = [];
        const dailyWeatherCodeMap = {};
        
        // Group weather codes by day
        for (let i = 0; i < hourlyTimes.length; i++) {
            const date = hourlyTimes[i].split('T')[0];
            if (!dailyWeatherCodeMap[date]) {
                dailyWeatherCodeMap[date] = {};
            }
            
            const code = weatherCodes[i];
            if (code !== null && code !== undefined) {
                if (!dailyWeatherCodeMap[date][code]) {
                    dailyWeatherCodeMap[date][code] = 0;
                }
                dailyWeatherCodeMap[date][code]++;
            }
        }
        
        // Find most common weather code for each day
        for (const date in dailyWeatherCodeMap) {
            let maxCount = 0;
            let mostCommonCode = 0;
            
            for (const code in dailyWeatherCodeMap[date]) {
                if (dailyWeatherCodeMap[date][code] > maxCount) {
                    maxCount = dailyWeatherCodeMap[date][code];
                    mostCommonCode = parseInt(code);
                }
            }
            
            dailyWeatherCodes.push(mostCommonCode);
        }
        
        return dailyWeatherCodes;
    }

    // Update all charts with new data
    function updateCharts(dailyData) {
        // Destroy existing charts if they exist
        if (temperatureChart) temperatureChart.destroy();
        if (precipitationChart) precipitationChart.destroy();
        if (windSpeedChart) windSpeedChart.destroy();
        if (cloudCoverChart) cloudCoverChart.destroy();
        if (humidityChart) humidityChart.destroy();

        // Create new charts
        createTemperatureChart(dailyData);
        createPrecipitationChart(dailyData);
        createWindSpeedChart(dailyData);
        createCloudCoverChart(dailyData);
        createHumidityChart(dailyData);
    }

    // Show error message if charts cannot be loaded
    function showChartError(message) {
        document.getElementById('weatherInfo').innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i> ${message}`;
        document.getElementById('weatherInfo').classList.remove('alert-info');
        document.getElementById('weatherInfo').classList.add('alert-danger');
        document.getElementById('weatherInfo').classList.remove('d-none');

        // Clear any existing charts
        const chartContainers = document.querySelectorAll('.card-body canvas');
        chartContainers.forEach(container => {
            const ctx = container.getContext('2d');
            ctx.clearRect(0, 0, container.width, container.height);
        });
    }

    // Populate the weather data table
    function populateWeatherTable(dailyData) {
        const tableBody = document.querySelector('#weatherTable tbody');
        tableBody.innerHTML = ''; // Clear previous data

        // Group data by month
        const monthlyData = {};

        // Find extreme values and their dates
        const extremeValues = {
            temperature: { 
                high: { value: -100, date: null, index: -1 },
                low: { value: 100, date: null, index: -1 }
            },
            precipitation: { 
                high: { value: 0, date: null, index: -1 },
                low: { value: 999999, date: null, index: -1 }
            },
            windSpeed: { 
                high: { value: 0, date: null, index: -1 },
                low: { value: 999999, date: null, index: -1 }
            },
            cloudCover: { 
                high: { value: 0, date: null, index: -1 },
                low: { value: 100, date: null, index: -1 }
            },
            humidity: { 
                high: { value: 0, date: null, index: -1 },
                low: { value: 100, date: null, index: -1 }
            }
        };

        for (let i = 0; i < dailyData.time.length; i++) {
            const date = new Date(dailyData.time[i]);
            const monthYear = date.toLocaleString('default', { month: 'long', year: 'numeric' });
            const formattedDate = date.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric' 
            });

            if (!monthlyData[monthYear]) {
                monthlyData[monthYear] = {
                    count: 0,
                    tempMax: 0,
                    tempMin: 0,
                    precipitation: 0,
                    windSpeed: 0,
                    cloudCover: 0,
                    humidity: 0,
                    // Track extremes for each climate element
                    highTemp: -100, // Initial low value
                    lowTemp: 100,   // Initial high value
                    highPrecip: 0,
                    highWind: 0,
                    highCloud: 0,
                    lowCloud: 100,
                    highHumidity: 0,
                    lowHumidity: 100
                };
            }

            monthlyData[monthYear].count++;
            
            const maxTemp = dailyData.temperature_2m_max[i] || 0;
            const minTemp = dailyData.temperature_2m_min[i] || 0;
            const precip = dailyData.precipitation_sum[i] || 0;
            const wind = dailyData.windspeed_10m_max[i] || 0;
            const cloud = dailyData.cloudcover_mean[i] || 0;
            const humidity = dailyData.relativehumidity_2m_mean[i] || 0;
            
            // Track extreme values with dates
            // Temperature (use max temp for high, min temp for low)
            if (maxTemp > extremeValues.temperature.high.value) {
                extremeValues.temperature.high.value = maxTemp;
                extremeValues.temperature.high.date = formattedDate;
                extremeValues.temperature.high.index = i;
            }
            if (minTemp < extremeValues.temperature.low.value) {
                extremeValues.temperature.low.value = minTemp;
                extremeValues.temperature.low.date = formattedDate;
                extremeValues.temperature.low.index = i;
            }
            
            // Precipitation
            if (precip > extremeValues.precipitation.high.value) {
                extremeValues.precipitation.high.value = precip;
                extremeValues.precipitation.high.date = formattedDate;
                extremeValues.precipitation.high.index = i;
            }
            if (precip < extremeValues.precipitation.low.value && precip > 0) {
                extremeValues.precipitation.low.value = precip;
                extremeValues.precipitation.low.date = formattedDate;
                extremeValues.precipitation.low.index = i;
            }
            
            // Wind speed
            if (wind > extremeValues.windSpeed.high.value) {
                extremeValues.windSpeed.high.value = wind;
                extremeValues.windSpeed.high.date = formattedDate;
                extremeValues.windSpeed.high.index = i;
            }
            if (wind < extremeValues.windSpeed.low.value && wind > 0) {
                extremeValues.windSpeed.low.value = wind;
                extremeValues.windSpeed.low.date = formattedDate;
                extremeValues.windSpeed.low.index = i;
            }
            
            // Cloud cover
            if (cloud > extremeValues.cloudCover.high.value) {
                extremeValues.cloudCover.high.value = cloud;
                extremeValues.cloudCover.high.date = formattedDate;
                extremeValues.cloudCover.high.index = i;
            }
            if (cloud < extremeValues.cloudCover.low.value) {
                extremeValues.cloudCover.low.value = cloud;
                extremeValues.cloudCover.low.date = formattedDate;
                extremeValues.cloudCover.low.index = i;
            }
            
            // Humidity
            if (humidity > extremeValues.humidity.high.value) {
                extremeValues.humidity.high.value = humidity;
                extremeValues.humidity.high.date = formattedDate;
                extremeValues.humidity.high.index = i;
            }
            if (humidity < extremeValues.humidity.low.value) {
                extremeValues.humidity.low.value = humidity;
                extremeValues.humidity.low.date = formattedDate;
                extremeValues.humidity.low.index = i;
            }
            
            // Sum values for averages
            monthlyData[monthYear].tempMax += maxTemp;
            monthlyData[monthYear].tempMin += minTemp;
            monthlyData[monthYear].precipitation += precip;
            monthlyData[monthYear].windSpeed += wind;
            monthlyData[monthYear].cloudCover += cloud;
            monthlyData[monthYear].humidity += humidity;
            
            // Track high/low values
            monthlyData[monthYear].highTemp = Math.max(monthlyData[monthYear].highTemp, maxTemp);
            monthlyData[monthYear].lowTemp = Math.min(monthlyData[monthYear].lowTemp, minTemp);
            monthlyData[monthYear].highPrecip = Math.max(monthlyData[monthYear].highPrecip, precip);
            monthlyData[monthYear].highWind = Math.max(monthlyData[monthYear].highWind, wind);
            monthlyData[monthYear].highCloud = Math.max(monthlyData[monthYear].highCloud, cloud);
            monthlyData[monthYear].lowCloud = Math.min(monthlyData[monthYear].lowCloud, cloud);
            monthlyData[monthYear].highHumidity = Math.max(monthlyData[monthYear].highHumidity, humidity);
            monthlyData[monthYear].lowHumidity = Math.min(monthlyData[monthYear].lowHumidity, humidity);
        }

        // Calculate averages and create table rows
        Object.entries(monthlyData).forEach(([month, data]) => {
            const avgTempMax = (data.tempMax / data.count).toFixed(1);
            const avgTempMin = (data.tempMin / data.count).toFixed(1);
            const avgTemp = ((parseFloat(avgTempMax) + parseFloat(avgTempMin)) / 2).toFixed(1);
            const totalPrecip = data.precipitation.toFixed(1);
            const avgWindSpeed = (data.windSpeed / data.count).toFixed(1);
            const avgCloudCover = (data.cloudCover / data.count).toFixed(0);
            const avgHumidity = (data.humidity / data.count).toFixed(0);

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${month}</td>
                <td>${avgTemp}°C<br><small>(High: ${data.highTemp.toFixed(1)}°C, Low: ${data.lowTemp.toFixed(1)}°C)</small></td>
                <td>${totalPrecip} mm<br><small>(High: ${data.highPrecip.toFixed(1)} mm)</small></td>
                <td>${avgWindSpeed} km/h<br><small>(High: ${data.highWind.toFixed(1)} km/h)</small></td>
                <td>${avgCloudCover}%<br><small>(Range: ${data.lowCloud.toFixed(0)}-${data.highCloud.toFixed(0)}%)</small></td>
                <td>${avgHumidity}%<br><small>(Range: ${data.lowHumidity.toFixed(0)}-${data.highHumidity.toFixed(0)}%)</small></td>
            `;
            tableBody.appendChild(row);
        });
        
        // Populate the extreme events table
        const extremeEventsTable = document.querySelector('#extremeEventsTable tbody');
        extremeEventsTable.innerHTML = ''; // Clear previous data
        
        // Temperature row
        const extremeTempRow = document.createElement('tr');
        extremeTempRow.innerHTML = `
            <td><strong>Temperature</strong></td>
            <td class="text-danger fw-bold">${extremeValues.temperature.high.value.toFixed(1)}°C</td>
            <td class="text-danger">${extremeValues.temperature.high.date || 'N/A'}</td>
            <td class="text-primary fw-bold">${extremeValues.temperature.low.value.toFixed(1)}°C</td>
            <td class="text-primary">${extremeValues.temperature.low.date || 'N/A'}</td>
        `;
        extremeEventsTable.appendChild(extremeTempRow);
        
        // Precipitation row
        const extremePrecipRow = document.createElement('tr');
        const lowPrecipValue = extremeValues.precipitation.low.value < 999999 ? 
            extremeValues.precipitation.low.value.toFixed(1) + ' mm' : 
            '0.0 mm';
        const lowPrecipDate = extremeValues.precipitation.low.value < 999999 ? 
            extremeValues.precipitation.low.date : 
            'Multiple dates';
            
        extremePrecipRow.innerHTML = `
            <td><strong>Precipitation</strong></td>
            <td class="text-danger fw-bold">${extremeValues.precipitation.high.value.toFixed(1)} mm</td>
            <td class="text-danger">${extremeValues.precipitation.high.date || 'N/A'}</td>
            <td class="text-primary fw-bold">${lowPrecipValue}</td>
            <td class="text-primary">${lowPrecipDate}</td>
        `;
        extremeEventsTable.appendChild(extremePrecipRow);
        
        // Wind speed row
        const extremeWindRow = document.createElement('tr');
        const lowWindValue = extremeValues.windSpeed.low.value < 999999 ? 
            extremeValues.windSpeed.low.value.toFixed(1) + ' km/h' : 
            '0.0 km/h';
        const lowWindDate = extremeValues.windSpeed.low.value < 999999 ? 
            extremeValues.windSpeed.low.date : 
            'Multiple dates';
            
        extremeWindRow.innerHTML = `
            <td><strong>Wind Speed</strong></td>
            <td class="text-danger fw-bold">${extremeValues.windSpeed.high.value.toFixed(1)} km/h</td>
            <td class="text-danger">${extremeValues.windSpeed.high.date || 'N/A'}</td>
            <td class="text-primary fw-bold">${lowWindValue}</td>
            <td class="text-primary">${lowWindDate}</td>
        `;
        extremeEventsTable.appendChild(extremeWindRow);
        
        // Cloud cover row
        const extremeCloudRow = document.createElement('tr');
        extremeCloudRow.innerHTML = `
            <td><strong>Cloud Cover</strong></td>
            <td class="text-danger fw-bold">${extremeValues.cloudCover.high.value.toFixed(0)}%</td>
            <td class="text-danger">${extremeValues.cloudCover.high.date || 'N/A'}</td>
            <td class="text-primary fw-bold">${extremeValues.cloudCover.low.value.toFixed(0)}%</td>
            <td class="text-primary">${extremeValues.cloudCover.low.date || 'N/A'}</td>
        `;
        extremeEventsTable.appendChild(extremeCloudRow);
        
        // Humidity row
        const extremeHumidityRow = document.createElement('tr');
        extremeHumidityRow.innerHTML = `
            <td><strong>Humidity</strong></td>
            <td class="text-danger fw-bold">${extremeValues.humidity.high.value.toFixed(0)}%</td>
            <td class="text-danger">${extremeValues.humidity.high.date || 'N/A'}</td>
            <td class="text-primary fw-bold">${extremeValues.humidity.low.value.toFixed(0)}%</td>
            <td class="text-primary">${extremeValues.humidity.low.date || 'N/A'}</td>
        `;
        extremeEventsTable.appendChild(extremeHumidityRow);
        
        // Calculate overall summary statistics
        const summaryData = {
            temperature: { sum: 0, count: 0, high: -100, low: 100 },
            precipitation: { sum: 0, count: 0, high: 0, low: 0 },
            windSpeed: { sum: 0, count: 0, high: 0, low: 0 },
            cloudCover: { sum: 0, count: 0, high: 0, low: 100 },
            humidity: { sum: 0, count: 0, high: 0, low: 100 }
        };
        
        for (let i = 0; i < dailyData.time.length; i++) {
            // Temperature (average of max and min)
            const maxTemp = dailyData.temperature_2m_max[i];
            const minTemp = dailyData.temperature_2m_min[i];
            if (maxTemp !== null && maxTemp !== undefined && minTemp !== null && minTemp !== undefined) {
                const avgTemp = (maxTemp + minTemp) / 2;
                summaryData.temperature.sum += avgTemp;
                summaryData.temperature.count++;
                summaryData.temperature.high = Math.max(summaryData.temperature.high, maxTemp);
                summaryData.temperature.low = Math.min(summaryData.temperature.low, minTemp);
            }
            
            // Precipitation
            const precip = dailyData.precipitation_sum[i];
            if (precip !== null && precip !== undefined) {
                summaryData.precipitation.sum += precip;
                summaryData.precipitation.count++;
                summaryData.precipitation.high = Math.max(summaryData.precipitation.high, precip);
            }
            
            // Wind speed
            const wind = dailyData.windspeed_10m_max[i];
            if (wind !== null && wind !== undefined) {
                summaryData.windSpeed.sum += wind;
                summaryData.windSpeed.count++;
                summaryData.windSpeed.high = Math.max(summaryData.windSpeed.high, wind);
            }
            
            // Cloud cover
            const cloud = dailyData.cloudcover_mean[i];
            if (cloud !== null && cloud !== undefined) {
                summaryData.cloudCover.sum += cloud;
                summaryData.cloudCover.count++;
                summaryData.cloudCover.high = Math.max(summaryData.cloudCover.high, cloud);
                summaryData.cloudCover.low = Math.min(summaryData.cloudCover.low, cloud);
            }
            
            // Humidity
            const humidity = dailyData.relativehumidity_2m_mean[i];
            if (humidity !== null && humidity !== undefined) {
                summaryData.humidity.sum += humidity;
                summaryData.humidity.count++;
                summaryData.humidity.high = Math.max(summaryData.humidity.high, humidity);
                summaryData.humidity.low = Math.min(summaryData.humidity.low, humidity);
            }
        }
        
        // Populate summary table
        const summaryTableBody = document.querySelector('#summaryTable tbody');
        summaryTableBody.innerHTML = ''; // Clear previous data
        
        // Temperature row
        const tempAvg = summaryData.temperature.count > 0 ? (summaryData.temperature.sum / summaryData.temperature.count).toFixed(1) : 'N/A';
        const tempRow = document.createElement('tr');
        tempRow.innerHTML = `
            <td><strong>Temperature</strong></td>
            <td>${tempAvg}°C</td>
            <td>${summaryData.temperature.high.toFixed(1)}°C</td>
            <td>${summaryData.temperature.low.toFixed(1)}°C</td>
        `;
        summaryTableBody.appendChild(tempRow);
        
        // Precipitation row
        const precipTotal = summaryData.precipitation.sum.toFixed(1);
        const precipAvg = summaryData.precipitation.count > 0 ? (summaryData.precipitation.sum / summaryData.precipitation.count).toFixed(1) : 'N/A';
        const precipRow = document.createElement('tr');
        precipRow.innerHTML = `
            <td><strong>Precipitation</strong></td>
            <td>${precipAvg} mm/day (Total: ${precipTotal} mm)</td>
            <td>${summaryData.precipitation.high.toFixed(1)} mm</td>
            <td>0.0 mm</td>
        `;
        summaryTableBody.appendChild(precipRow);
        
        // Wind speed row
        const windAvg = summaryData.windSpeed.count > 0 ? (summaryData.windSpeed.sum / summaryData.windSpeed.count).toFixed(1) : 'N/A';
        const windRow = document.createElement('tr');
        windRow.innerHTML = `
            <td><strong>Wind Speed</strong></td>
            <td>${windAvg} km/h</td>
            <td>${summaryData.windSpeed.high.toFixed(1)} km/h</td>
            <td>0.0 km/h</td>
        `;
        summaryTableBody.appendChild(windRow);
        
        // Cloud cover row
        const cloudAvg = summaryData.cloudCover.count > 0 ? (summaryData.cloudCover.sum / summaryData.cloudCover.count).toFixed(0) : 'N/A';
        const cloudRow = document.createElement('tr');
        cloudRow.innerHTML = `
            <td><strong>Cloud Cover</strong></td>
            <td>${cloudAvg}%</td>
            <td>${summaryData.cloudCover.high.toFixed(0)}%</td>
            <td>${summaryData.cloudCover.low.toFixed(0)}%</td>
        `;
        summaryTableBody.appendChild(cloudRow);
        
        // Humidity row
        const humidityAvg = summaryData.humidity.count > 0 ? (summaryData.humidity.sum / summaryData.humidity.count).toFixed(0) : 'N/A';
        const humidityRow = document.createElement('tr');
        humidityRow.innerHTML = `
            <td><strong>Humidity</strong></td>
            <td>${humidityAvg}%</td>
            <td>${summaryData.humidity.high.toFixed(0)}%</td>
            <td>${summaryData.humidity.low.toFixed(0)}%</td>
        `;
        summaryTableBody.appendChild(humidityRow);
    }

    // Create temperature chart
    function createTemperatureChart(dailyData) {
        const ctx = document.getElementById('temperatureChart').getContext('2d');

        // Format dates for better display
        const formattedDates = dailyData.time.map(date => {
            const d = new Date(date);
            return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        // Find highest and lowest temperature points
        let maxTempIndex = 0;
        let minTempIndex = 0;
        let maxTemp = -100;
        let minTemp = 100;

        for (let i = 0; i < dailyData.temperature_2m_max.length; i++) {
            if (dailyData.temperature_2m_max[i] > maxTemp) {
                maxTemp = dailyData.temperature_2m_max[i];
                maxTempIndex = i;
            }
        }

        for (let i = 0; i < dailyData.temperature_2m_min.length; i++) {
            if (dailyData.temperature_2m_min[i] < minTemp) {
                minTemp = dailyData.temperature_2m_min[i];
                minTempIndex = i;
            }
        }

        // Create point styles arrays
        const maxPointStyles = Array(dailyData.temperature_2m_max.length).fill(1);
        maxPointStyles[maxTempIndex] = 7; // Larger point for max
        
        const minPointStyles = Array(dailyData.temperature_2m_min.length).fill(1);
        minPointStyles[minTempIndex] = 7; // Larger point for min

        // Create point background colors arrays
        const maxPointColors = Array(dailyData.temperature_2m_max.length).fill('rgba(255, 99, 132, 1)');
        maxPointColors[maxTempIndex] = 'rgba(255, 0, 0, 1)'; // Red for max
        
        const minPointColors = Array(dailyData.temperature_2m_min.length).fill('rgba(54, 162, 235, 1)');
        minPointColors[minTempIndex] = 'rgba(0, 0, 255, 1)'; // Blue for min

        temperatureChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: formattedDates,
                datasets: [
                    {
                        label: 'Max Temperature',
                        data: dailyData.temperature_2m_max,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1,
                        pointRadius: maxPointStyles,
                        pointBackgroundColor: maxPointColors,
                        pointHoverRadius: 5
                    },
                    {
                        label: 'Min Temperature',
                        data: dailyData.temperature_2m_min,
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.1,
                        pointRadius: minPointStyles,
                        pointBackgroundColor: minPointColors,
                        pointHoverRadius: 5
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Temperature (°C)',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(1) + '°C';
                                    if (context.datasetIndex === 0 && context.dataIndex === maxTempIndex) {
                                        label += ' (Highest)';
                                    }
                                    if (context.datasetIndex === 1 && context.dataIndex === minTempIndex) {
                                        label += ' (Lowest)';
                                    }
                                }
                                return label;
                            }
                        }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            autoSkip: true,
                            maxTicksLimit: 20
                        },
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Temperature (°C)'
                        }
                    }
                }
            }
        });
    }

    // Create precipitation chart
    function createPrecipitationChart(dailyData) {
        const ctx = document.getElementById('precipitationChart').getContext('2d');

        // Format dates for better display
        const formattedDates = dailyData.time.map(date => {
            const d = new Date(date);
            return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        // Find highest precipitation point
        let maxPrecipIndex = 0;
        let maxPrecip = 0;
        
        for (let i = 0; i < dailyData.precipitation_sum.length; i++) {
            if (dailyData.precipitation_sum[i] > maxPrecip) {
                maxPrecip = dailyData.precipitation_sum[i];
                maxPrecipIndex = i;
            }
        }

        // Create background colors array for bars
        const barColors = Array(dailyData.precipitation_sum.length).fill('rgba(54, 162, 235, 0.5)');
        barColors[maxPrecipIndex] = 'rgba(255, 0, 0, 0.7)'; // Red for max
        
        // Create border colors array for bars
        const borderColors = Array(dailyData.precipitation_sum.length).fill('rgba(54, 162, 235, 1)');
        borderColors[maxPrecipIndex] = 'rgba(255, 0, 0, 1)'; // Red for max

        precipitationChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: formattedDates,
                datasets: [{
                    label: 'Precipitation',
                    data: dailyData.precipitation_sum,
                    backgroundColor: barColors,
                    borderColor: borderColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Precipitation (mm)',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(1) + ' mm';
                                    if (context.dataIndex === maxPrecipIndex) {
                                        label += ' (Highest)';
                                    }
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            autoSkip: true,
                            maxTicksLimit: 20
                        },
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Precipitation (mm)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Create wind speed chart
    function createWindSpeedChart(dailyData) {
        const ctx = document.getElementById('windSpeedChart').getContext('2d');

        // Format dates for better display
        const formattedDates = dailyData.time.map(date => {
            const d = new Date(date);
            return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        // Find highest wind speed point
        let maxWindIndex = 0;
        let maxWind = 0;
        
        for (let i = 0; i < dailyData.windspeed_10m_max.length; i++) {
            if (dailyData.windspeed_10m_max[i] > maxWind) {
                maxWind = dailyData.windspeed_10m_max[i];
                maxWindIndex = i;
            }
        }

        // Create point styles array
        const pointStyles = Array(dailyData.windspeed_10m_max.length).fill(1);
        pointStyles[maxWindIndex] = 7; // Larger point for max
        
        // Create point colors array
        const pointColors = Array(dailyData.windspeed_10m_max.length).fill('rgba(75, 192, 192, 1)');
        pointColors[maxWindIndex] = 'rgba(255, 0, 0, 1)'; // Red for max

        windSpeedChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: formattedDates,
                datasets: [{
                    label: 'Wind Speed',
                    data: dailyData.windspeed_10m_max,
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1,
                    pointRadius: pointStyles,
                    pointBackgroundColor: pointColors,
                    pointHoverRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Max Wind Speed (km/h)',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(1) + ' km/h';
                                    if (context.dataIndex === maxWindIndex) {
                                        label += ' (Highest)';
                                    }
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            autoSkip: true,
                            maxTicksLimit: 20
                        },
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Wind Speed (km/h)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Create cloud cover chart
    function createCloudCoverChart(dailyData) {
        const ctx = document.getElementById('cloudCoverChart').getContext('2d');

        // Format dates for better display
        const formattedDates = dailyData.time.map(date => {
            const d = new Date(date);
            return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        // Find highest and lowest cloud cover points
        let maxCloudIndex = 0;
        let minCloudIndex = 0;
        let maxCloud = 0;
        let minCloud = 100;
        
        for (let i = 0; i < dailyData.cloudcover_mean.length; i++) {
            if (dailyData.cloudcover_mean[i] > maxCloud) {
                maxCloud = dailyData.cloudcover_mean[i];
                maxCloudIndex = i;
            }
            if (dailyData.cloudcover_mean[i] < minCloud) {
                minCloud = dailyData.cloudcover_mean[i];
                minCloudIndex = i;
            }
        }

        // Create point styles array
        const pointStyles = Array(dailyData.cloudcover_mean.length).fill(1);
        pointStyles[maxCloudIndex] = 7; // Larger point for max
        pointStyles[minCloudIndex] = 7; // Larger point for min
        
        // Create point colors array
        const pointColors = Array(dailyData.cloudcover_mean.length).fill('rgba(255, 159, 64, 1)');
        pointColors[maxCloudIndex] = 'rgba(255, 0, 0, 1)'; // Red for max
        pointColors[minCloudIndex] = 'rgba(0, 0, 255, 1)'; // Blue for min

        cloudCoverChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: formattedDates,
                datasets: [{
                    label: 'Cloud Cover',
                    data: dailyData.cloudcover_mean,
                    borderColor: 'rgba(255, 159, 64, 1)',
                    backgroundColor: 'rgba(255, 159, 64, 0.2)',
                    tension: 0.1,
                    fill: true,
                    pointRadius: pointStyles,
                    pointBackgroundColor: pointColors,
                    pointHoverRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Mean Cloud Cover (%)',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(0) + '%';
                                    if (context.dataIndex === maxCloudIndex) {
                                        label += ' (Highest)';
                                    }
                                    if (context.dataIndex === minCloudIndex) {
                                        label += ' (Lowest)';
                                    }
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            autoSkip: true,
                            maxTicksLimit: 20
                        },
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        min: 0,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Cloud Cover (%)'
                        }
                    }
                }
            }
        });
    }

    // Create humidity chart
    function createHumidityChart(dailyData) {
        const ctx = document.getElementById('humidityChart').getContext('2d');

        // Format dates for better display
        const formattedDates = dailyData.time.map(date => {
            const d = new Date(date);
            return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        // Find highest and lowest humidity points
        let maxHumidityIndex = 0;
        let minHumidityIndex = 0;
        let maxHumidity = 0;
        let minHumidity = 100;
        
        for (let i = 0; i < dailyData.relativehumidity_2m_mean.length; i++) {
            if (dailyData.relativehumidity_2m_mean[i] > maxHumidity) {
                maxHumidity = dailyData.relativehumidity_2m_mean[i];
                maxHumidityIndex = i;
            }
            if (dailyData.relativehumidity_2m_mean[i] < minHumidity) {
                minHumidity = dailyData.relativehumidity_2m_mean[i];
                minHumidityIndex = i;
            }
        }

        // Create point styles array
        const pointStyles = Array(dailyData.relativehumidity_2m_mean.length).fill(1);
        pointStyles[maxHumidityIndex] = 7; // Larger point for max
        pointStyles[minHumidityIndex] = 7; // Larger point for min
        
        // Create point colors array
        const pointColors = Array(dailyData.relativehumidity_2m_mean.length).fill('rgba(153, 102, 255, 1)');
        pointColors[maxHumidityIndex] = 'rgba(255, 0, 0, 1)'; // Red for max
        pointColors[minHumidityIndex] = 'rgba(0, 0, 255, 1)'; // Blue for min

        humidityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: formattedDates,
                datasets: [{
                    label: 'Relative Humidity',
                    data: dailyData.relativehumidity_2m_mean,
                    borderColor: 'rgba(153, 102, 255, 1)',
                    backgroundColor: 'rgba(153, 102, 255, 0.2)',
                    tension: 0.1,
                    fill: true,
                    pointRadius: pointStyles,
                    pointBackgroundColor: pointColors,
                    pointHoverRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Mean Relative Humidity (%)',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(0) + '%';
                                    if (context.dataIndex === maxHumidityIndex) {
                                        label += ' (Highest)';
                                    }
                                    if (context.dataIndex === minHumidityIndex) {
                                        label += ' (Lowest)';
                                    }
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            autoSkip: true,
                            maxTicksLimit: 20
                        },
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        min: 0,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Humidity (%)'
                        }
                    }
                }
            }
        });
    }

    // Process only hourly data to daily format
    function processOnlyHourlyData(hourlyData) {
        const dailyData = {
            time: [],
            temperature_2m_max: [],
            temperature_2m_min: [],
            precipitation_sum: [],
            windspeed_10m_max: [],
            cloudcover_mean: [],
            relativehumidity_2m_mean: [],
            weather_code: []
        };
        
        // Group hourly data by day
        const dailyMap = {};
        
        for (let i = 0; i < hourlyData.time.length; i++) {
            const date = hourlyData.time[i].split('T')[0];
            if (!dailyMap[date]) {
                dailyMap[date] = {
                    temps: [],
                    precip: 0,
                    windspeed: [],
                    cloudcover: [],
                    humidity: [],
                    weatherCodes: {}
                };
            }
            
            // Temperature
            if (hourlyData.temperature_2m && hourlyData.temperature_2m[i] !== null) {
                dailyMap[date].temps.push(hourlyData.temperature_2m[i]);
            }
            
            // Precipitation (sum)
            if (hourlyData.precipitation && hourlyData.precipitation[i] !== null) {
                dailyMap[date].precip += hourlyData.precipitation[i];
            }
            
            // Wind speed
            if (hourlyData.windspeed_10m && hourlyData.windspeed_10m[i] !== null) {
                dailyMap[date].windspeed.push(hourlyData.windspeed_10m[i]);
            }
            
            // Cloud cover
            if (hourlyData.cloudcover && hourlyData.cloudcover[i] !== null) {
                dailyMap[date].cloudcover.push(hourlyData.cloudcover[i]);
            }
            
            // Humidity
            if (hourlyData.relative_humidity_2m && hourlyData.relative_humidity_2m[i] !== null) {
                dailyMap[date].humidity.push(hourlyData.relative_humidity_2m[i]);
            }
            
            // Weather code
            if (hourlyData.weather_code && hourlyData.weather_code[i] !== null) {
                const code = hourlyData.weather_code[i];
                if (!dailyMap[date].weatherCodes[code]) {
                    dailyMap[date].weatherCodes[code] = 0;
                }
                dailyMap[date].weatherCodes[code]++;
            }
        }
        
        // Process daily data
        Object.keys(dailyMap).sort().forEach(date => {
            const dayData = dailyMap[date];
            
            dailyData.time.push(date);
            
            // Temperature max/min
            if (dayData.temps.length > 0) {
                dailyData.temperature_2m_max.push(Math.max(...dayData.temps));
                dailyData.temperature_2m_min.push(Math.min(...dayData.temps));
            } else {
                dailyData.temperature_2m_max.push(null);
                dailyData.temperature_2m_min.push(null);
            }
            
            // Precipitation sum
            dailyData.precipitation_sum.push(dayData.precip);
            
            // Wind speed max
            if (dayData.windspeed.length > 0) {
                dailyData.windspeed_10m_max.push(Math.max(...dayData.windspeed));
            } else {
                dailyData.windspeed_10m_max.push(null);
            }
            
            // Cloud cover mean
            if (dayData.cloudcover.length > 0) {
                const sum = dayData.cloudcover.reduce((a, b) => a + b, 0);
                dailyData.cloudcover_mean.push(sum / dayData.cloudcover.length);
            } else {
                dailyData.cloudcover_mean.push(null);
            }
            
            // Humidity mean
            if (dayData.humidity.length > 0) {
                const sum = dayData.humidity.reduce((a, b) => a + b, 0);
                dailyData.relativehumidity_2m_mean.push(sum / dayData.humidity.length);
            } else {
                dailyData.relativehumidity_2m_mean.push(null);
            }
            
            // Most common weather code
            let maxCount = 0;
            let mostCommonCode = 0;
            for (const code in dayData.weatherCodes) {
                if (dayData.weatherCodes[code] > maxCount) {
                    maxCount = dayData.weatherCodes[code];
                    mostCommonCode = parseInt(code);
                }
            }
            dailyData.weather_code.push(mostCommonCode);
        });
        
        return dailyData;
    }

    // Initialize map when page loads
    document.addEventListener('DOMContentLoaded', initMap);
</script>
<?= $this->endSection() ?>
