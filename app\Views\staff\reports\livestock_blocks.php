<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb bg-white shadow-sm py-2 px-3 rounded">
            <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>" class="text-decoration-none">Dashboard</a></li>
            <li class="breadcrumb-item">Reports</li>
            <li class="breadcrumb-item active" aria-current="page">Livestock Blocks Report</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800"><?= $page_header ?></h1>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-primary shadow-sm" onclick="window.print()">
                <i class="fas fa-print fa-sm text-white-50 mr-1"></i> Print Report
            </button>
        </div>
    </div>

    <!-- Statistics Cards Row -->
    <div class="row mb-4">
        <!-- Total Farm Blocks Card -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100">
                <div class="card-body py-3">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-2">Total Farm Blocks</div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800"><?= $stats['total_blocks'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-farm fa-2x text-primary opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Blocks Card -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100">
                <div class="card-body py-3">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-2">Active Blocks</div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800"><?= $stats['total_active'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-success opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inactive Blocks Card -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100">
                <div class="card-body py-3">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-2">Inactive Blocks</div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800"><?= $stats['total_inactive'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-danger opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribution Charts Row -->
    <div class="row mb-4">
        <!-- Distribution by LLG -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-white">
                    <h6 class="m-0 font-weight-bold text-primary">Distribution by LLG</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="llgChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribution by Livestock Type -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-white">
                    <h6 class="m-0 font-weight-bold text-primary">Distribution by Livestock Type</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="livestockTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Row -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 bg-white">
                    <h6 class="m-0 font-weight-bold text-primary">Farm Blocks Location Map</h6>
                </div>
                <div class="card-body p-0">
                    <div id="blocksMap" style="height: 500px; width: 100%;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Farm Blocks Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-white d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Livestock Farm Blocks</h6>
            <div class="btn-group">
                <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print fa-sm mr-1"></i> Print
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th>Block Code</th>
                            <th>Farmer Name</th>
                            <th>LLG</th>
                            <th>Ward</th>
                            <th>Block Site</th>
                            <th>Livestock Types</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($blocks_data as $block): ?>
                            <tr>
                                <td><?= $block['block_code'] ?></td>
                                <td><?= esc($block['given_name']) . ' ' . esc($block['surname']) ?></td>
                                <td><?= esc($block['llg_name']) ?></td>
                                <td><?= esc($block['ward_name']) ?></td>
                                <td><?= esc($block['block_site']) ?></td>
                                <td><?= esc($block['livestock_types'] ?? 'None') ?></td>
                                <td class="text-center">
                                    <?php if ($block['status'] == 'active'): ?>
                                        <span class="badge text-white bg-success rounded-pill px-3 py-2">
                                            <i class="fas fa-check-circle mr-1"></i>Active
                                        </span>
                                    <?php else: ?>
                                        <span class="badge text-white bg-danger rounded-pill px-3 py-2">
                                            <i class="fas fa-times-circle mr-1"></i>Inactive
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
    $(document).ready(function() {
        // Initialize Map with improved styling
        const map = L.map('blocksMap').setView([-6.314993, 143.95555], 7);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Add markers for each block with improved styling
        const markers = [];
        const blocks = <?= json_encode($blocks_data) ?>;
        
        blocks.forEach(block => {
            if (block.lat && block.lon) {
                let marker = L.circleMarker([block.lat, block.lon], {
                    radius: 8,
                    fillColor: block.status === 'active' ? '#1cc88a' : '#e74a3b',
                    color: '#fff',
                    weight: 1,
                    opacity: 1,
                    fillOpacity: 0.8
                }).addTo(map);

                marker.bindPopup(`
                    <div class="p-2">
                        <h6 class="mb-2">${block.block_code}</h6>
                        <p class="mb-1"><strong>Farmer:</strong> ${block.given_name} ${block.surname}</p>
                        <p class="mb-1"><strong>Location:</strong> ${block.block_site}</p>
                        <p class="mb-1"><strong>District:</strong> ${block.district_name}</p>
                        <p class="mb-1"><strong>LLG:</strong> ${block.llg_name}</p>
                        <p class="mb-1"><strong>Livestock:</strong> ${block.livestock_types || 'None'}</p>
                        <p class="mb-0"><strong>Status:</strong> 
                            <span class="badge badge-${block.status === 'active' ? 'success' : 'danger'}">
                                ${block.status.charAt(0).toUpperCase() + block.status.slice(1)}
                            </span>
                        </p>
                    </div>
                `, {
                    maxWidth: 300,
                    className: 'custom-popup'
                });

                markers.push(marker);
            }
        });

        // Fit map bounds
        if (markers.length > 0) {
            const group = new L.featureGroup(markers);
            map.fitBounds(group.getBounds().pad(0.1));
        }

        // Add legend with improved styling
        const legend = L.control({ position: 'bottomright' });
        legend.onAdd = function(map) {
            const div = L.DomUtil.create('div', 'info legend');
            div.style.backgroundColor = 'white';
            div.style.padding = '10px';
            div.style.borderRadius = '5px';
            div.style.border = '2px solid rgba(0,0,0,0.2)';
            div.style.fontSize = '12px';

            div.innerHTML = `
                <h6 class="mb-2 font-weight-bold">Block Status</h6>
                <div class="mb-1">
                    <i style="background: #1cc88a; display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 5px;"></i>
                    Active
                </div>
                <div class="mb-1">
                    <i style="background: #e74a3b; display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 5px;"></i>
                    Inactive
                </div>
            `;
            return div;
        };
        legend.addTo(map);

        // Chart Colors with improved palette
        const colors = [
            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
            '#858796', '#5a5c69', '#2e59d9', '#17a673', '#2c9faf',
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEEAD'
        ];

        // LLG Chart with improved styling
        const llgData = <?= json_encode(array_values($stats['by_llg'])) ?>;
        const llgLabels = <?= json_encode(array_keys($stats['by_llg'])) ?>;

        if (llgData.length > 0) {
            new Chart(document.getElementById('llgChart'), {
                type: 'pie',
                data: {
                    labels: llgLabels,
                    datasets: [{
                        data: llgData,
                        backgroundColor: colors.slice(0, llgData.length)
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                padding: 20,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    let value = context.raw || 0;
                                    let total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    let percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} blocks (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Livestock Type Chart with improved styling
        const livestockTypeData = <?= json_encode(array_values($stats['by_livestock_type'])) ?>;
        const livestockTypeLabels = <?= json_encode(array_keys($stats['by_livestock_type'])) ?>;
        const livestockColors = <?= json_encode($stats['livestock_colors']) ?>;

        if (livestockTypeData.length > 0) {
            new Chart(document.getElementById('livestockTypeChart'), {
                type: 'pie',
                data: {
                    labels: livestockTypeLabels,
                    datasets: [{
                        data: livestockTypeData,
                        backgroundColor: livestockTypeLabels.map(label => livestockColors[label] || colors[0])
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                padding: 20,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    let value = context.raw || 0;
                                    let total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    let percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} blocks (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }
    });
</script>

<style>
    .custom-popup .leaflet-popup-content-wrapper {
        border-radius: 8px;
    }
    .custom-popup .leaflet-popup-content {
        margin: 0;
    }
    .badge {
        font-size: 0.85em;
        font-weight: 500;
        letter-spacing: 0.3px;
    }
    .bg-success {
        background-color: #1cc88a !important;
    }
    .bg-danger {
        background-color: #e74a3b !important;
    }
    .chart-container {
        position: relative;
        margin: auto;
    }
    .table thead th {
        background-color: #f8f9fc;
        border-bottom: 2px solid #e3e6f0;
    }
    .table-hover tbody tr:hover {
        background-color: #f8f9fc;
    }
    .opacity-50 {
        opacity: 0.5;
    }
</style>

<?= $this->endSection() ?>

<?= $this->endSection() ?>