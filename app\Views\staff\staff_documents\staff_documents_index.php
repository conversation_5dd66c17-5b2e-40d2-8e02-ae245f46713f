<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Page Title -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Document Folders</li>
        </ol>
    </nav>
    <a href="<?= base_url('staff/office/documents/create') ?>" class="btn btn-success">
        <i class="fas fa-folder-plus"></i> New Folder
    </a>
</div>

<!-- Main Content -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-folder-open me-2"></i>Document Folders</h5>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('success')) : ?>
            <div class="alert alert-success">
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>
        
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <?php if (empty($folders)) : ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No document folders found. Create a new folder to get started.
            </div>
        <?php else : ?>
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead>
                        <tr>
                            <th>Folder Name</th>
                            <th>Parent Folder</th>
                            <th>Description</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($folders as $folder) : ?>
                            <tr>
                                <td>
                                    <a href="<?= base_url('staff/office/documents/files/' . $folder['id']) ?>" class="text-decoration-none">
                                        <i class="fas fa-folder text-warning me-2"></i>
                                        <?= esc($folder['folder_name']) ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if (!empty($folder['parent_folder_name'])) : ?>
                                        <i class="fas fa-folder text-warning me-1"></i>
                                        <?= esc($folder['parent_folder_name']) ?>
                                    <?php else : ?>
                                        <span class="text-muted">None</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= esc($folder['description']) ?></td>
                                <td><?= date('M d, Y', strtotime($folder['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?= base_url('staff/office/documents/files/' . $folder['id']) ?>" class="btn btn-sm btn-primary" title="View Files">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <a href="<?= base_url('staff/office/documents/edit/' . $folder['id']) ?>" class="btn btn-sm btn-info" title="Edit Folder">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="confirmDelete('<?= base_url('staff/office/documents/delete/' . $folder['id']) ?>')" 
                                                title="Delete Folder">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this folder? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="deleteLink" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(deleteUrl) {
        document.getElementById('deleteLink').href = deleteUrl;
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }
</script>

<?= $this->endSection() ?>
