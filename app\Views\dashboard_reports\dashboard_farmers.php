<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_title ?></h1>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <!-- Total Farmers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="totalFarmersCount">0</h3>
                        <p>Total Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            <!-- Crops Farmers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3 id="cropFarmersCount">0/0</h3>
                        <p>Male/Female Crop Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
            <!-- Livestock Farmers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3 id="livestockFarmersCount">0/0</h3>
                        <p>Male/Female Livestock Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-cow"></i>
                    </div>
                </div>
            </div>
            <!-- Gender Distribution Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3 id="genderDistribution">0/0</h3>
                        <p>Male/Female Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-venus-mars"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Summary Cards Row - Crops -->
        <div class="row">
            <!-- Total Crop Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-primary">
                    <div class="inner">
                        <h3 id="totalCropBlocksCount">0</h3>
                        <p>Total Crop Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <!-- Total Hectares Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="totalHectaresCount">0</h3>
                        <p>Total Hectares</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                </div>
            </div>
            <!-- Disease Affected Hectares Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3 id="diseaseHectaresCount">0</h3>
                        <p>Disease Affected Ha</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-biohazard"></i>
                    </div>
                </div>
            </div>
            <!-- Total Crops Revenue Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                      <h3 id="totalCropsRevenue">0</h3>
                        <p>Total Crops Revenue (<?= COUNTRY_CURRENCY ?>)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Summary Cards Row - Livestock -->
        <div class="row">
            <!-- Total Livestock Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-primary">
                    <div class="inner">
                        <h3 id="totalLivestockBlocksCount">0</h3>
                        <p>Total Livestock Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <!-- Total Livestock Value Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="totalLivestockValue">0</h3>
                        <p>Total Livestock Value (<?= COUNTRY_CURRENCY ?>)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farmers Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">All Farmers</h3>
                    </div>
                    <div class="card-body">
                        <div id="filterContainer" class="mb-3 d-flex flex-wrap gap-2">
                            <!-- Filters will be inserted here by JavaScript -->
                        </div>
                        <div class="table-responsive">
                            <table id="farmersTable" class="table table-bordered text-nowrap table-striped">
                                <thead>
                                    <tr>
                                        <th>Farmer Code</th>
                                        <th>Name</th>
                                        <th>Gender</th>
                                        <th>Age</th>
                                        <th>Child</th>
                                        <th>Marital Status</th>
                                        <th>Highest Education</th>
                                        <th>Province</th>
                                        <th>District</th>
                                        <th>LLG</th>
                                        <th>Ward</th>
                                        <th>Village</th>
                                        <th>Phone</th>
                                        <th>Status</th>
                                        <th>Crop Blocks</th>
                                        <th>Total Hectares</th>
                                        <th>Disease Affected Ha</th>
                                        <th>Crops Revenue</th>
                                        <th>Crop Types</th>
                                        <th>Livestock Blocks</th>
                                        <th>Avg Livestock Value</th>
                                        <th>Livestock Types</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($all_farmers as $farmer):
                                        $age = !empty($farmer['date_of_birth']) ?
                                            date_diff(date_create($farmer['date_of_birth']), date_create('today'))->y :
                                            'N/A';
                                    ?>
                                        <tr class="farmer-row" data-farmer-id="<?= $farmer['id'] ?>" style="cursor: pointer;">
                                            <td><?= $farmer['farmer_code'] ?></td>
                                            <td><?= $farmer['given_name'] . ' ' . $farmer['surname'] ?></td>
                                            <td><?= $farmer['gender'] ?></td>
                                            <td><?= $age ?></td>
                                            <td><?= $farmer['children_count'] ?? 0 ?></td>
                                            <td><?= ucfirst($farmer['marital_status'] ?? 'Not Specified') ?></td>
                                            <td><?= $farmer['highest_education'] ?? 'Not Specified' ?></td>
                                            <td><?= $farmer['province_name'] ?></td>
                                            <td><?= $farmer['district_name'] ?></td>
                                            <td><?= $farmer['llg_name'] ?></td>
                                            <td><?= $farmer['ward_name'] ?></td>
                                            <td><?= $farmer['village'] ?></td>
                                            <td><?= $farmer['phone'] ?></td>
                                            <td><?= $farmer['status'] ?? 'Active' ?></td>
                                            <td><?= $farmer['crops_blocks_count'] ?? 0 ?></td>
                                            <td><?= number_format($farmer['total_hectares'] ?? 0, 2) ?></td>
                                            <td><?= number_format($farmer['disease_affected_hectares'] ?? 0, 2) ?></td>
                                            <td><?= number_format($farmer['total_crops_revenue'] ?? 0, 2) ?></td>
                                            <td><?= $farmer['crop_types'] ?? 'None' ?></td>
                                            <td><?= $farmer['livestock_blocks_count'] ?? 0 ?></td>
                                            <td><?= number_format($farmer['avg_livestock_value'] ?? 0, 2) ?></td>
                                            <td><?= $farmer['livestock_types'] ?? 'None' ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            <script>
                                document.querySelectorAll('.farmer-row').forEach(row => {
                                    row.addEventListener('click', function() {
                                        const farmerId = this.getAttribute('data-farmer-id');
                                        window.location.href = `<?= base_url('dashboards/farmers/profile') ?>/${farmerId}`;
                                    });
                                });
                            </script>
                        </div>
                        <script>
                            class FilterableTable {
                                constructor(tableId, data) {
                                    this.table = document.getElementById(tableId);
                                    this.data = this.extractTableData();
                                    this.filters = {};
                                    this.columns = Object.keys(this.data[0]);
                                    this.init();
                                }

                                extractTableData() {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr'));
                                    return rows.map(row => {
                                        const cells = Array.from(row.cells);
                                        return {
                                            farmer_code: cells[0].textContent,
                                            name: cells[1].textContent,
                                            gender: cells[2].textContent,
                                            age: cells[3].textContent,
                                            children: cells[4].textContent,
                                            province: cells[5].textContent,
                                            district: cells[6].textContent,
                                            llg: cells[7].textContent,
                                            ward: cells[8].textContent,
                                            village: cells[9].textContent,
                                            phone: cells[10].textContent,
                                            status: cells[11].textContent,
                                            crop_blocks: cells[12].textContent,
                                            total_hectares: cells[13].textContent,
                                            disease_affected: cells[14].textContent,
                                            crops_revenue: cells[15].textContent,
                                            crop_types: cells[16].textContent,
                                            livestock_blocks: cells[17].textContent,
                                            livestock_value: cells[18].textContent,
                                            livestock_types: cells[19].textContent
                                        };
                                    });
                                }

                                init() {
                                    this.createFilters();
                                    this.setupEventListeners();
                                }

                                getUniqueValues(column) {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr:not([style*="display: none"])'));
                                    const values = new Set();

                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        let value;

                                        switch (column) {
                                            case 'gender':
                                                value = cells[2].textContent;
                                                break;
                                            case 'highest_education':
                                                value = cells[6].textContent;
                                                break;
                                            case 'province':
                                                value = cells[7].textContent;
                                                break;
                                            case 'district':
                                                value = cells[8].textContent;
                                                break;
                                            case 'llg':
                                                value = cells[9].textContent;
                                                break;
                                            case 'ward':
                                                value = cells[10].textContent;
                                                break;
                                            case 'village':
                                                value = cells[11].textContent;
                                                break;
                                            case 'status':
                                                value = cells[13].textContent;
                                                break;
                                            case 'crop_types':
                                                cells[18].textContent.split(',').forEach(v => {
                                                    const trimmed = v.trim();
                                                    if (trimmed && trimmed !== 'None') values.add(trimmed);
                                                });
                                                return Array.from(values).sort();
                                            case 'livestock_types':
                                                cells[21].textContent.split(',').forEach(v => {
                                                    const trimmed = v.trim();
                                                    if (trimmed && trimmed !== 'None') values.add(trimmed);
                                                });
                                                return Array.from(values).sort();
                                        }
                                        if (value) values.add(value);
                                    });

                                    return Array.from(values).sort();
                                }

                                updateFilterDropdowns() {
                                    const columns = [
                                        'gender', 'highest_education',
                                        'province', 'district', 'llg', 'ward', 'village',
                                        'status', 'crop_types', 'livestock_types'
                                    ];

                                    columns.forEach(column => {
                                        const dropdown = document.querySelector(`[data-filter="${column}"]`);
                                        if (dropdown) {
                                            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
                                            const checkboxes = dropdownMenu.querySelectorAll('input[type="checkbox"]:not(#selectAll_' + column + ')');
                                            const selectAllCheckbox = dropdownMenu.querySelector('#selectAll_' + column);
                                            const uniqueValues = this.getUniqueValues(column);
                                            
                                            // Store currently selected values
                                            const selectedValues = Array.from(checkboxes)
                                                .filter(cb => cb.checked)
                                                .map(cb => cb.value);

                                            // Clear existing checkboxes except "Select All"
                                            Array.from(dropdownMenu.querySelectorAll('.custom-control:not(:first-child)')).forEach(el => el.remove());

                                            // Add new checkboxes
                                            uniqueValues.forEach((value, index) => {
                                                const itemDiv = document.createElement('div');
                                                itemDiv.className = 'custom-control custom-checkbox';

                                                const checkbox = document.createElement('input');
                                                checkbox.type = 'checkbox';
                                                checkbox.className = 'custom-control-input';
                                                checkbox.id = `${column}_${index}`;
                                                checkbox.value = value;
                                                // Maintain selected state if value was previously selected
                                                checkbox.checked = selectedValues.includes(value);

                                                const label = document.createElement('label');
                                                label.className = 'custom-control-label';
                                                label.htmlFor = `${column}_${index}`;
                                                label.textContent = value;

                                                itemDiv.appendChild(checkbox);
                                                itemDiv.appendChild(label);
                                                dropdownMenu.appendChild(itemDiv);

                                                checkbox.addEventListener('change', (e) => {
                                                    e.stopPropagation();
                                                    if (checkbox.checked) {
                                                        this.filters[column].push(value);
                                                    } else {
                                                        this.filters[column] = this.filters[column].filter(v => v !== value);
                                                    }
                                                    const button = dropdown.querySelector('button');
                                                    this.updateSelectedCount(button, this.filters[column].length);
                                                    selectAllCheckbox.checked = Array.from(dropdownMenu.querySelectorAll('input[type="checkbox"]:not(#selectAll_' + column + ')')).every(cb => cb.checked);
                                                    this.filterTable();
                                                });
                                            });

                                            // Update select all checkbox state
                                            selectAllCheckbox.checked = Array.from(dropdownMenu.querySelectorAll('input[type="checkbox"]:not(#selectAll_' + column + ')')).every(cb => cb.checked);
                                        }
                                    });
                                }

                                updateSelectedCount(button, count) {
                                    const badge = button.querySelector('.badge');
                                    badge.textContent = count > 0 ? count : '';
                                    badge.style.display = count > 0 ? 'inline' : 'none';
                                }

                                createMultiSelectDropdown(column, uniqueValues) {
                                    const container = document.createElement('div');
                                    container.className = 'dropdown';
                                    container.setAttribute('data-filter', column);

                                    const button = document.createElement('button');
                                    button.className = 'btn btn-default dropdown-toggle';
                                    button.setAttribute('data-toggle', 'dropdown');
                                    button.innerHTML = `${column} <span class="badge badge-light"></span>`;

                                    const dropdownMenu = document.createElement('div');
                                    dropdownMenu.className = 'dropdown-menu p-2';
                                    dropdownMenu.style.minWidth = '200px';
                                    dropdownMenu.style.maxHeight = '300px';
                                    dropdownMenu.style.overflowY = 'auto';

                                    this.filters[column] = [];

                                    // Select All option
                                    const selectAllDiv = document.createElement('div');
                                    selectAllDiv.className = 'custom-control custom-checkbox';

                                    const selectAllCheckbox = document.createElement('input');
                                    selectAllCheckbox.type = 'checkbox';
                                    selectAllCheckbox.className = 'custom-control-input';
                                    selectAllCheckbox.id = `selectAll_${column}`;

                                    const selectAllLabel = document.createElement('label');
                                    selectAllLabel.className = 'custom-control-label';
                                    selectAllLabel.htmlFor = `selectAll_${column}`;
                                    selectAllLabel.textContent = 'Select All';

                                    selectAllDiv.appendChild(selectAllCheckbox);
                                    selectAllDiv.appendChild(selectAllLabel);
                                    dropdownMenu.appendChild(selectAllDiv);

                                    dropdownMenu.appendChild(document.createElement('hr'));

                                    const checkboxes = [];
                                    uniqueValues.forEach((value, index) => {
                                        const itemDiv = document.createElement('div');
                                        itemDiv.className = 'custom-control custom-checkbox';

                                        const checkbox = document.createElement('input');
                                        checkbox.type = 'checkbox';
                                        checkbox.className = 'custom-control-input';
                                        checkbox.id = `${column}_${index}`;
                                        checkbox.value = value;
                                        checkboxes.push(checkbox);

                                        const label = document.createElement('label');
                                        label.className = 'custom-control-label';
                                        label.htmlFor = `${column}_${index}`;
                                        label.textContent = value;

                                        itemDiv.appendChild(checkbox);
                                        itemDiv.appendChild(label);
                                        dropdownMenu.appendChild(itemDiv);

                                        checkbox.addEventListener('change', (e) => {
                                            e.stopPropagation();
                                            if (checkbox.checked) {
                                                this.filters[column].push(value);
                                            } else {
                                                this.filters[column] = this.filters[column].filter(v => v !== value);
                                            }
                                            this.updateSelectedCount(button, this.filters[column].length);
                                            selectAllCheckbox.checked = checkboxes.every(cb => cb.checked);
                                            this.filterTable();
                                        });
                                    });

                                    selectAllCheckbox.addEventListener('change', (e) => {
                                        e.stopPropagation();
                                        const checkboxes = dropdownMenu.querySelectorAll('input[type="checkbox"]:not(#selectAll_' + column + ')');
                                        checkboxes.forEach(checkbox => {
                                            checkbox.checked = selectAllCheckbox.checked;
                                            const changeEvent = new Event('change');
                                            checkbox.dispatchEvent(changeEvent);
                                        });
                                    });

                                    container.appendChild(button);
                                    container.appendChild(dropdownMenu);
                                    return container;
                                }

                                createFilters() {
                                    const filterContainer = document.getElementById('filterContainer');
                                    [
                                        'age', 'gender', 'highest_education',
                                        'province', 'district', 'llg', 'ward', 'village',
                                        'status', 'crop_types', 'livestock_types'
                                    ].forEach(column => {
                                        if (column === 'age') {
                                            const ageRanges = [
                                                '15-19', '20-24', '25-29', '30-34', '35-39',
                                                '40-44', '45-49', '50-54', '55-59', '60-64',
                                                '65-69', '70-74', '75-79', '80+'
                                            ];
                                            filterContainer.appendChild(this.createMultiSelectDropdown(column, ageRanges));
                                        } else {
                                            const uniqueValues = this.getUniqueValues(column);
                                            filterContainer.appendChild(this.createMultiSelectDropdown(column, uniqueValues));
                                        }
                                    });
                                }

                                setupEventListeners() {
                                    // Using Bootstrap's dropdown events instead of custom click handlers
                                    $('.dropdown-menu').on('click', function(e) {
                                        e.stopPropagation();
                                    });
                                }

                                calculateAgeDistribution() {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr:not([style*="display: none"])'));
                                    const ageRanges = {};
                                    const maleData = {};
                                    const femaleData = {};

                                    // Initialize age ranges from 15 to 80+ in 5-year intervals
                                    for (let age = 15; age <= 80; age += 5) {
                                        const rangeKey = age === 80 ? '80+' : `${age}-${age + 4}`;
                                        maleData[rangeKey] = 0;
                                        femaleData[rangeKey] = 0;
                                    }

                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        const age = parseInt(cells[3].textContent);
                                        const gender = cells[2].textContent;

                                        if (!isNaN(age)) {
                                            let rangeKey;
                                            if (age >= 80) {
                                                rangeKey = '80+';
                                            } else {
                                                const baseAge = Math.floor(age / 5) * 5;
                                                rangeKey = `${baseAge}-${baseAge + 4}`;
                                            }

                                            if (gender === 'Male') {
                                                maleData[rangeKey] = (maleData[rangeKey] || 0) + 1;
                                            } else if (gender === 'Female') {
                                                femaleData[rangeKey] = (femaleData[rangeKey] || 0) + 1;
                                            }
                                        }
                                    });

                                    return {
                                        labels: Object.keys(maleData),
                                        male: Object.values(maleData),
                                        female: Object.values(femaleData)
                                    };
                                }

                                updateAgeChart() {
                                    const ageData = this.calculateAgeDistribution();

                                    if (window.ageChart) {
                                        window.ageChart.data.labels = ageData.labels;
                                        window.ageChart.data.datasets[0].data = ageData.male;
                                        window.ageChart.data.datasets[1].data = ageData.female;
                                        window.ageChart.update();
                                    }
                                }

                                calculateEducationDistribution() {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr:not([style*="display: none"])'));
                                    const educationData = new Map();

                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        const education = cells[6].textContent;
                                        const gender = cells[2].textContent;

                                        if (!educationData.has(education)) {
                                            educationData.set(education, {
                                                male: 0,
                                                female: 0
                                            });
                                        }

                                        const data = educationData.get(education);
                                        if (gender === 'Male') {
                                            data.male++;
                                        } else if (gender === 'Female') {
                                            data.female++;
                                        }
                                    });

                                    // Convert Map to arrays for Chart.js
                                    const labels = Array.from(educationData.keys());
                                    const maleData = labels.map(label => educationData.get(label).male);
                                    const femaleData = labels.map(label => educationData.get(label).female);

                                    return {
                                        labels: labels,
                                        male: maleData,
                                        female: femaleData
                                    };
                                }

                                updateEducationChart() {
                                    const educationData = this.calculateEducationDistribution();

                                    if (window.eduChart) {
                                        window.eduChart.data.labels = educationData.labels;
                                        window.eduChart.data.datasets[0].data = educationData.male;
                                        window.eduChart.data.datasets[1].data = educationData.female;
                                        window.eduChart.update();
                                    }
                                }

                                filterTable() {
                                    const rows = this.table.querySelectorAll('tbody tr');
                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        const age = parseInt(cells[3].textContent);
                                        const ageRange = this.getAgeRange(age);

                                        const rowData = {
                                            age: ageRange,
                                            gender: cells[2].textContent,
                                            highest_education: cells[6].textContent,
                                            province: cells[7].textContent,
                                            district: cells[8].textContent,
                                            llg: cells[9].textContent,
                                            ward: cells[10].textContent,
                                            village: cells[11].textContent,
                                            status: cells[13].textContent,
                                            crop_types: cells[18].textContent,
                                            livestock_types: cells[21].textContent
                                        };

                                        const visible = Object.entries(this.filters).every(([column, selectedValues]) => {
                                            if (!selectedValues || selectedValues.length === 0) return true;
                                            if (column === 'crop_types' || column === 'livestock_types') {
                                                // Split comma-separated values and check if any selected value matches
                                                const values = rowData[column].split(',').map(v => v.trim());
                                                return selectedValues.some(selected => values.includes(selected));
                                            }
                                            if (column === 'age') {
                                                return selectedValues.includes(rowData.age);
                                            }
                                            return selectedValues.includes(rowData[column]);
                                        });

                                        row.style.display = visible ? '' : 'none';
                                    });

                                    // Update all charts and summary cards after filtering
                                    this.updateAgeChart();
                                    this.updateEducationChart();
                                    this.updateCropDistrictChart();
                                    this.updateLivestockDistrictChart();
                                    this.updateCropLLGChart();
                                    this.updateLivestockLLGChart();
                                    this.updateSummaryCards();
                                    // Update filter dropdowns after filtering
                                    this.updateFilterDropdowns();
                                }

                                calculateSummaryData() {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr'));
                                    let totalFarmers = 0;
                                    let maleCropFarmers = 0;
                                    let femaleCropFarmers = 0;
                                    let maleLivestockFarmers = 0;
                                    let femaleLivestockFarmers = 0;
                                    let maleFarmers = 0;
                                    let femaleFarmers = 0;
                                    let totalCropBlocks = 0;
                                    let totalHectares = 0;
                                    let diseaseHectares = 0;
                                    let cropsRevenue = 0;
                                    let totalLivestockBlocks = 0;
                                    let livestockValue = 0;

                                    rows.forEach(row => {
                                        if (row.style.display !== 'none' || !row.style.display) {
                                            const cells = Array.from(row.cells);
                                            totalFarmers++;

                                            // Get gender first
                                            const gender = cells[2].textContent;
                                            if (gender === 'Male') {
                                                maleFarmers++;
                                            } else if (gender === 'Female') {
                                                femaleFarmers++;
                                            }

                                            // Count crop farmers and related data
                                            const crops = cells[18].textContent.trim();
                                            if (crops && crops !== 'None') {
                                                if (gender === 'Male') {
                                                    maleCropFarmers++;
                                                } else if (gender === 'Female') {
                                                    femaleCropFarmers++;
                                                }
                                                // Add crop blocks
                                                totalCropBlocks += parseInt(cells[14].textContent) || 0;
                                                // Add hectares
                                                totalHectares += parseFloat(cells[15].textContent.replace(/,/g, '')) || 0;
                                                // Add disease affected hectares
                                                diseaseHectares += parseFloat(cells[16].textContent.replace(/,/g, '')) || 0;
                                                // Add crops revenue
                                                cropsRevenue += parseFloat(cells[17].textContent.replace(/,/g, '')) || 0;
                                            }

                                            // Count livestock farmers and related data
                                            const livestock = cells[21].textContent.trim();
                                            if (livestock && livestock !== 'None') {
                                                if (gender === 'Male') {
                                                    maleLivestockFarmers++;
                                                } else if (gender === 'Female') {
                                                    femaleLivestockFarmers++;
                                                }
                                                // Add livestock blocks
                                                totalLivestockBlocks += parseInt(cells[19].textContent) || 0;
                                                // Add livestock value
                                                livestockValue += parseFloat(cells[20].textContent.replace(/,/g, '')) || 0;
                                            }
                                        }
                                    });

                                    return {
                                        total: totalFarmers,
                                        maleCrops: maleCropFarmers,
                                        femaleCrops: femaleCropFarmers,
                                        maleLivestock: maleLivestockFarmers,
                                        femaleLivestock: femaleLivestockFarmers,
                                        male: maleFarmers,
                                        female: femaleFarmers,
                                        cropBlocks: totalCropBlocks,
                                        hectares: totalHectares.toFixed(2),
                                        diseaseHectares: diseaseHectares.toFixed(2),
                                        cropsRevenue: cropsRevenue.toFixed(2),
                                        livestockBlocks: totalLivestockBlocks,
                                        livestockValue: livestockValue.toFixed(2)
                                    };
                                }

                                updateSummaryCards() {
                                    const summaryData = this.calculateSummaryData();

                                    // Update the summary cards
                                    document.getElementById('totalFarmersCount').textContent = summaryData.total;
                                    document.getElementById('cropFarmersCount').textContent =
                                        `${summaryData.maleCrops}/${summaryData.femaleCrops}`;
                                    document.getElementById('livestockFarmersCount').textContent =
                                        `${summaryData.maleLivestock}/${summaryData.femaleLivestock}`;
                                    document.getElementById('genderDistribution').textContent =
                                        `${summaryData.male}/${summaryData.female}`;

                                    // Update new summary cards
                                    document.getElementById('totalCropBlocksCount').textContent = summaryData.cropBlocks;
                                    document.getElementById('totalHectaresCount').textContent = summaryData.hectares;
                                    document.getElementById('diseaseHectaresCount').textContent = summaryData.diseaseHectares;
                                    document.getElementById('totalCropsRevenue').textContent =
                                        parseFloat(summaryData.cropsRevenue).toLocaleString('en-US', {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2
                                        });
                                    document.getElementById('totalLivestockBlocksCount').textContent = summaryData.livestockBlocks;
                                    document.getElementById('totalLivestockValue').textContent =
                                        parseFloat(summaryData.livestockValue).toLocaleString('en-US', {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2
                                        });
                                }

                                calculateCropDistrictData() {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr:not([style*="display: none"])'));
                                    const cropsByDistrict = new Map();
                                    const cropTypes = new Set();
                                    const districts = new Set();

                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        const district = cells[8].textContent.trim();
                                        const crops = cells[18].textContent.split(',').map(c => c.trim()).filter(c => c && c !== 'None');

                                        if (crops.length > 0) {
                                            districts.add(district);
                                            crops.forEach(crop => cropTypes.add(crop));

                                            if (!cropsByDistrict.has(district)) {
                                                cropsByDistrict.set(district, new Map());
                                            }

                                            const districtCrops = cropsByDistrict.get(district);
                                            crops.forEach(crop => {
                                                districtCrops.set(crop, (districtCrops.get(crop) || 0) + 1);
                                            });
                                        }
                                    });

                                    const sortedDistricts = Array.from(districts).sort();
                                    const sortedCropTypes = Array.from(cropTypes).sort();

                                    return {
                                        labels: sortedDistricts,
                                        datasets: sortedCropTypes.map(cropType => ({
                                            label: cropType,
                                            data: sortedDistricts.map(district =>
                                                cropsByDistrict.get(district)?.get(cropType) || 0
                                            ),
                                            backgroundColor: this.getCropColor(cropType),
                                            borderColor: this.getCropColor(cropType),
                                            borderWidth: 1,
                                            stack: 'Stack 0'
                                        }))
                                    };
                                }

                                calculateLivestockDistrictData() {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr:not([style*="display: none"])'));
                                    const livestockByDistrict = new Map();
                                    const livestockTypes = new Set();
                                    const districts = new Set();

                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        const district = cells[8].textContent;
                                        const livestock = cells[21].textContent.split(',').map(l => l.trim()).filter(l => l !== 'None');

                                        districts.add(district);
                                        livestock.forEach(type => livestockTypes.add(type));

                                        if (!livestockByDistrict.has(district)) {
                                            livestockByDistrict.set(district, new Map());
                                        }

                                        livestock.forEach(type => {
                                            const districtLivestock = livestockByDistrict.get(district);
                                            districtLivestock.set(type, (districtLivestock.get(type) || 0) + 1);
                                        });
                                    });

                                    return {
                                        labels: Array.from(districts),
                                        datasets: Array.from(livestockTypes).map(livestockType => ({
                                            label: livestockType,
                                            data: Array.from(districts).map(district =>
                                                livestockByDistrict.get(district)?.get(livestockType) || 0
                                            ),
                                            backgroundColor: this.getLivestockColor(livestockType),
                                            stack: 'Stack 0'
                                        }))
                                    };
                                }

                                updateCropDistrictChart() {
                                    const cropData = this.calculateCropDistrictData();

                                    if (window.cropDistrictChart) {
                                        window.cropDistrictChart.clear();
                                        window.cropDistrictChart.data.labels = cropData.labels;
                                        window.cropDistrictChart.data.datasets = cropData.datasets;
                                        window.cropDistrictChart.options.animation = false;
                                        window.cropDistrictChart.update();
                                        requestAnimationFrame(() => {
                                            window.cropDistrictChart.resize();
                                            window.cropDistrictChart.render();
                                        });
                                    }
                                }

                                updateLivestockDistrictChart() {
                                    const livestockData = this.calculateLivestockDistrictData();

                                    if (window.livestockDistrictChart) {
                                        window.livestockDistrictChart.clear();
                                        window.livestockDistrictChart.data.labels = livestockData.labels;
                                        window.livestockDistrictChart.data.datasets = livestockData.datasets;
                                        window.livestockDistrictChart.options.animation = false;
                                        window.livestockDistrictChart.update();
                                        requestAnimationFrame(() => {
                                            window.livestockDistrictChart.resize();
                                            window.livestockDistrictChart.render();
                                        });
                                    }
                                }

                                calculateCropLLGData() {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr:not([style*="display: none"])'));
                                    const cropsByLLG = new Map();
                                    const cropTypes = new Set();
                                    const llgs = new Set();

                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        const llg = cells[9].textContent;
                                        const crops = cells[18].textContent.split(',').map(c => c.trim()).filter(c => c !== 'None');

                                        llgs.add(llg);
                                        crops.forEach(crop => cropTypes.add(crop));

                                        if (!cropsByLLG.has(llg)) {
                                            cropsByLLG.set(llg, new Map());
                                        }

                                        crops.forEach(crop => {
                                            const llgCrops = cropsByLLG.get(llg);
                                            llgCrops.set(crop, (llgCrops.get(crop) || 0) + 1);
                                        });
                                    });

                                    return {
                                        labels: Array.from(llgs),
                                        datasets: Array.from(cropTypes).map(cropType => ({
                                            label: cropType,
                                            data: Array.from(llgs).map(llg =>
                                                cropsByLLG.get(llg)?.get(cropType) || 0
                                            ),
                                            backgroundColor: this.getCropColor(cropType),
                                            borderColor: this.getCropColor(cropType),
                                            borderWidth: 1,
                                            stack: 'Stack 0'
                                        }))
                                    };
                                }

                                calculateLivestockLLGData() {
                                    const rows = Array.from(this.table.querySelectorAll('tbody tr:not([style*="display: none"])'));
                                    const livestockByLLG = new Map();
                                    const livestockTypes = new Set();
                                    const llgs = new Set();

                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        const llg = cells[9].textContent;
                                        const livestock = cells[21].textContent.split(',').map(l => l.trim()).filter(l => l !== 'None');

                                        llgs.add(llg);
                                        livestock.forEach(type => livestockTypes.add(type));

                                        if (!livestockByLLG.has(llg)) {
                                            livestockByLLG.set(llg, new Map());
                                        }

                                        livestock.forEach(type => {
                                            const llgLivestock = livestockByLLG.get(llg);
                                            llgLivestock.set(type, (llgLivestock.get(type) || 0) + 1);
                                        });
                                    });

                                    return {
                                        labels: Array.from(llgs),
                                        datasets: Array.from(livestockTypes).map(livestockType => ({
                                            label: livestockType,
                                            data: Array.from(llgs).map(llg =>
                                                livestockByLLG.get(llg)?.get(livestockType) || 0
                                            ),
                                            backgroundColor: this.getLivestockColor(livestockType),
                                            stack: 'Stack 0'
                                        }))
                                    };
                                }

                                updateCropLLGChart() {
                                    const cropData = this.calculateCropLLGData();

                                    if (window.cropLLGChart) {
                                        window.cropLLGChart.clear();
                                        window.cropLLGChart.data.labels = cropData.labels;
                                        window.cropLLGChart.data.datasets = cropData.datasets;
                                        window.cropLLGChart.options.animation = false;
                                        window.cropLLGChart.update();
                                        requestAnimationFrame(() => {
                                            window.cropLLGChart.resize();
                                            window.cropLLGChart.render();
                                        });
                                    }
                                }

                                updateLivestockLLGChart() {
                                    const livestockData = this.calculateLivestockLLGData();

                                    if (window.livestockLLGChart) {
                                        window.livestockLLGChart.clear();
                                        window.livestockLLGChart.data.labels = livestockData.labels;
                                        window.livestockLLGChart.data.datasets = livestockData.datasets;
                                        window.livestockLLGChart.options.animation = false;
                                        window.livestockLLGChart.update();
                                        requestAnimationFrame(() => {
                                            window.livestockLLGChart.resize();
                                            window.livestockLLGChart.render();
                                        });
                                    }
                                }

                                // Helper methods for colors
                                getCropColor(cropType) {
                                    // Get color codes from PHP data
                                    const colors = <?= json_encode(array_combine(
                                                        array_column($crops, 'crop_name'),
                                                        array_column($crops, 'crop_color_code')
                                                    )) ?>;
                                    return colors[cropType] || this.getRandomColor();
                                }

                                getLivestockColor(livestockType) {
                                    // Get color codes from PHP data
                                    const colors = <?= json_encode(array_combine(
                                                        array_column($livestock, 'livestock_name'),
                                                        array_column($livestock, 'livestock_color_code')
                                                    )) ?>;
                                    return colors[livestockType] || this.getRandomColor();
                                }

                                getRandomColor() {
                                    const letters = '0123456789ABCDEF';
                                    let color = '#';
                                    for (let i = 0; i < 6; i++) {
                                        color += letters[Math.floor(Math.random() * 16)];
                                    }
                                    return color;
                                }

                                getAgeRange(age) {
                                    if (isNaN(age)) return 'Unknown';
                                    if (age >= 80) return '80+';
                                    const baseAge = Math.floor(age / 5) * 5;
                                    return `${baseAge}-${baseAge + 4}`;
                                }
                            }

                            // Initialize the filterable table after DataTable is ready
                            $(document).ready(function() {
                                // Create initial age chart
                                const calculateInitialAgeDistribution = () => {
                                    const rows = Array.from(document.querySelectorAll('#farmersTable tbody tr'));
                                    const maleData = {};
                                    const femaleData = {};

                                    // Initialize age ranges
                                    for (let age = 15; age <= 80; age += 5) {
                                        const rangeKey = age === 80 ? '80+' : `${age}-${age + 4}`;
                                        maleData[rangeKey] = 0;
                                        femaleData[rangeKey] = 0;
                                    }

                                    rows.forEach(row => {
                                        const cells = Array.from(row.cells);
                                        const age = parseInt(cells[3].textContent);
                                        const gender = cells[2].textContent;

                                        if (!isNaN(age)) {
                                            let rangeKey;
                                            if (age >= 80) {
                                                rangeKey = '80+';
                                            } else {
                                                const baseAge = Math.floor(age / 5) * 5;
                                                rangeKey = `${baseAge}-${baseAge + 4}`;
                                            }

                                            if (gender === 'Male') {
                                                maleData[rangeKey]++;
                                            } else if (gender === 'Female') {
                                                femaleData[rangeKey]++;
                                            }
                                        }
                                    });

                                    return {
                                        labels: Object.keys(maleData),
                                        male: Object.values(maleData),
                                        female: Object.values(femaleData)
                                    };
                                };

                                // Create age chart immediately with initial data
                                const initialData = calculateInitialAgeDistribution();
                                window.ageChart = new Chart(document.getElementById('ageChart'), {
                                    type: 'line',
                                    data: {
                                        labels: initialData.labels,
                                        datasets: [{
                                            label: 'Male',
                                            data: initialData.male,
                                            borderColor: 'rgba(54, 162, 235, 1)',
                                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                            tension: 0.4,
                                            fill: true
                                        }, {
                                            label: 'Female',
                                            data: initialData.female,
                                            borderColor: 'rgba(255, 99, 132, 1)',
                                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                            tension: 0.4,
                                            fill: true
                                        }]
                                    },
                                    options: {
                                        responsive: true,
                                        scales: {
                                            y: {
                                                beginAtZero: true,
                                                ticks: {
                                                    precision: 0
                                                }
                                            }
                                        },
                                        plugins: {
                                            title: {
                                                display: true,
                                                text: 'Age Distribution by Gender'
                                            }
                                        }
                                    }
                                });

                                // Initialize DataTable
                                const dataTable = $('#farmersTable').DataTable({
                                    "responsive": false,
                                    "lengthChange": true,
                                    "autoWidth": false,
                                    "pageLength": 10,
                                    "lengthMenu": [
                                        [10, 50, 100, 300, 500, -1],
                                        [10, 50, 100, 300, 500, 'All']
                                    ],
                                    "buttons": ["colvis", "copy", "excel"],
                                    "initComplete": function() {
                                        window.filterableTable = new FilterableTable('farmersTable', []);

                                        // Update summary cards with initial data
                                        window.filterableTable.updateSummaryCards();

                                        // Create Education Chart
                                        const educationData = window.filterableTable.calculateEducationDistribution();
                                        window.eduChart = new Chart(document.getElementById('eduChart'), {
                                            type: 'bar',
                                            data: {
                                                labels: educationData.labels,
                                                datasets: [{
                                                    label: 'Male',
                                                    data: educationData.male,
                                                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                                    borderColor: 'rgba(54, 162, 235, 1)',
                                                    borderWidth: 1
                                                }, {
                                                    label: 'Female',
                                                    data: educationData.female,
                                                    backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                                    borderColor: 'rgba(255, 99, 132, 1)',
                                                    borderWidth: 1
                                                }]
                                            },
                                            options: {
                                                responsive: true,
                                                scales: {
                                                    x: {
                                                        grid: {
                                                            display: false
                                                        }
                                                    },
                                                    y: {
                                                        beginAtZero: true,
                                                        grid: {
                                                            color: 'rgba(0, 0, 0, 0.1)'
                                                        },
                                                        ticks: {
                                                            precision: 0
                                                        }
                                                    }
                                                },
                                                plugins: {
                                                    title: {
                                                        display: true,
                                                        text: 'Education Distribution by Gender'
                                                    },
                                                    legend: {
                                                        position: 'bottom',
                                                        labels: {
                                                            usePointStyle: true
                                                        }
                                                    }
                                                }
                                            }
                                        });

                                        // Create Crop District Chart
                                        const cropData = window.filterableTable.calculateCropDistrictData();
                                        window.cropDistrictChart = new Chart(document.getElementById('cropDistrictChart'), {
                                            type: 'bar',
                                            data: cropData,
                                            options: {
                                                responsive: true,
                                                maintainAspectRatio: false,
                                                plugins: {
                                                    title: {
                                                        display: true,
                                                        text: 'Farmers by Crop Type and District'
                                                    },
                                                    tooltip: {
                                                        mode: 'index',
                                                        intersect: false,
                                                    },
                                                    legend: {
                                                        position: 'bottom',
                                                        labels: {
                                                            usePointStyle: true,
                                                            boxWidth: 8,
                                                            boxHeight: 8,
                                                            padding: 10
                                                        }
                                                    }
                                                },
                                                scales: {
                                                    x: {
                                                        stacked: true,
                                                        grid: {
                                                            display: false
                                                        },
                                                        ticks: {
                                                            maxRotation: 45,
                                                            minRotation: 45
                                                        }
                                                    },
                                                    y: {
                                                        stacked: true,
                                                        beginAtZero: true,
                                                        grid: {
                                                            color: 'rgba(0, 0, 0, 0.1)'
                                                        },
                                                        ticks: {
                                                            precision: 0
                                                        }
                                                    }
                                                },
                                                interaction: {
                                                    intersect: false,
                                                    mode: 'index'
                                                }
                                            }
                                        });

                                        // Create Livestock District Chart
                                        const livestockData = window.filterableTable.calculateLivestockDistrictData();
                                        window.livestockDistrictChart = new Chart(document.getElementById('livestockDistrictChart'), {
                                            type: 'bar',
                                            data: livestockData,
                                            options: {
                                                responsive: true,
                                                plugins: {
                                                    title: {
                                                        display: true,
                                                        text: 'Farmers by Livestock Type and District'
                                                    },
                                                    tooltip: {
                                                        mode: 'index',
                                                        intersect: false,
                                                    },
                                                    legend: {
                                                        position: 'bottom',
                                                        labels: {
                                                            usePointStyle: true,
                                                        }
                                                    }
                                                },
                                                scales: {
                                                    x: {
                                                        stacked: true,
                                                        grid: {
                                                            display: false
                                                        }
                                                    },
                                                    y: {
                                                        stacked: true,
                                                        beginAtZero: true,
                                                        grid: {
                                                            color: 'rgba(0, 0, 0, 0.1)'
                                                        },
                                                        ticks: {
                                                            precision: 0
                                                        }
                                                    }
                                                },
                                                interaction: {
                                                    intersect: false,
                                                    mode: 'index'
                                                }
                                            }
                                        });

                                        // Create Crop LLG Chart
                                        const cropLLGData = window.filterableTable.calculateCropLLGData();
                                        window.cropLLGChart = new Chart(document.getElementById('cropLLGChart'), {
                                            type: 'bar',
                                            data: cropLLGData,
                                            options: {
                                                responsive: true,
                                                plugins: {
                                                    title: {
                                                        display: true,
                                                        text: 'Farmers by Crop Type and LLG'
                                                    },
                                                    tooltip: {
                                                        mode: 'index',
                                                        intersect: false,
                                                    },
                                                    legend: {
                                                        position: 'bottom',
                                                        labels: {
                                                            usePointStyle: true,
                                                        }
                                                    }
                                                },
                                                scales: {
                                                    x: {
                                                        stacked: true,
                                                        grid: {
                                                            display: false
                                                        }
                                                    },
                                                    y: {
                                                        stacked: true,
                                                        beginAtZero: true,
                                                        grid: {
                                                            color: 'rgba(0, 0, 0, 0.1)'
                                                        },
                                                        ticks: {
                                                            precision: 0
                                                        }
                                                    }
                                                },
                                                interaction: {
                                                    intersect: false,
                                                    mode: 'index'
                                                }
                                            }
                                        });

                                        // Create Livestock LLG Chart
                                        const livestockLLGData = window.filterableTable.calculateLivestockLLGData();
                                        window.livestockLLGChart = new Chart(document.getElementById('livestockLLGChart'), {
                                            type: 'bar',
                                            data: livestockLLGData,
                                            options: {
                                                responsive: true,
                                                plugins: {
                                                    title: {
                                                        display: true,
                                                        text: 'Farmers by Livestock Type and LLG'
                                                    },
                                                    tooltip: {
                                                        mode: 'index',
                                                        intersect: false,
                                                    },
                                                    legend: {
                                                        position: 'bottom',
                                                        labels: {
                                                            usePointStyle: true,
                                                        }
                                                    }
                                                },
                                                scales: {
                                                    x: {
                                                        stacked: true,
                                                        grid: {
                                                            display: false
                                                        }
                                                    },
                                                    y: {
                                                        stacked: true,
                                                        beginAtZero: true,
                                                        grid: {
                                                            color: 'rgba(0, 0, 0, 0.1)'
                                                        },
                                                        ticks: {
                                                            precision: 0
                                                        }
                                                    }
                                                },
                                                interaction: {
                                                    intersect: false,
                                                    mode: 'index'
                                                }
                                            }
                                        });
                                    },
                                    "drawCallback": function() {
                                        if (window.filterableTable) {
                                            window.filterableTable.updateAgeChart();
                                            window.filterableTable.updateEducationChart();
                                            window.filterableTable.updateCropDistrictChart();
                                            window.filterableTable.updateLivestockDistrictChart();
                                            window.filterableTable.updateCropLLGChart();
                                            window.filterableTable.updateLivestockLLGChart();
                                            window.filterableTable.updateSummaryCards();
                                        }
                                    }
                                }).buttons().container().appendTo('#farmersTable_wrapper .col-md-6:eq(0)');
                            });
                        </script>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row">
            <!-- Crop Type Distribution by District -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Farmers by Crop Type and District</h3>
                    </div>
                    <div class="card-body">
                        <div style="position: relative; height: 400px; width: 100%;">
                            <canvas id="cropDistrictChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Livestock Type Distribution by District -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Farmers by Livestock Type and District</h3>
                    </div>
                    <div class="card-body">
                        <div style="position: relative; height: 400px; width: 100%;">
                            <canvas id="livestockDistrictChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row for LLG -->
        <div class="row">
            <!-- Crop Type Distribution by LLG -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Farmers by Crop Type and LLG</h3>
                    </div>
                    <div class="card-body">
                        <div style="position: relative; height: 400px; width: 100%;">
                            <canvas id="cropLLGChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Livestock Type Distribution by LLG -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Farmers by Livestock Type and LLG</h3>
                    </div>
                    <div class="card-body">
                        <div style="position: relative; height: 400px; width: 100%;">
                            <canvas id="livestockLLGChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Age Distribution Row -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Age Distribution by Gender</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="ageChart" style="min-height: 100px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Education Distribution Row -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Education Distribution by Gender</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="eduChart" style="min-height: 100px;"></canvas>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>

<!-- Charts Initialization -->
<script>
    $(function() {
        // Create initial age chart
        const calculateInitialAgeDistribution = () => {
            const rows = Array.from(document.querySelectorAll('#farmersTable tbody tr'));
            const maleData = {};
            const femaleData = {};

            // Initialize age ranges
            for (let age = 15; age <= 80; age += 5) {
                const rangeKey = age === 80 ? '80+' : `${age}-${age + 4}`;
                maleData[rangeKey] = 0;
                femaleData[rangeKey] = 0;
            }

            rows.forEach(row => {
                const cells = Array.from(row.cells);
                const age = parseInt(cells[3].textContent);
                const gender = cells[2].textContent;

                if (!isNaN(age)) {
                    let rangeKey;
                    if (age >= 80) {
                        rangeKey = '80+';
                    } else {
                        const baseAge = Math.floor(age / 5) * 5;
                        rangeKey = `${baseAge}-${baseAge + 4}`;
                    }

                    if (gender === 'Male') {
                        maleData[rangeKey]++;
                    } else if (gender === 'Female') {
                        femaleData[rangeKey]++;
                    }
                }
            });

            return {
                labels: Object.keys(maleData),
                male: Object.values(maleData),
                female: Object.values(femaleData)
            };
        };

        // Create age chart immediately with initial data
        const initialData = calculateInitialAgeDistribution();
        window.ageChart = new Chart(document.getElementById('ageChart'), {
            type: 'line',
            data: {
                labels: initialData.labels,
                datasets: [{
                    label: 'Male',
                    data: initialData.male,
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Female',
                    data: initialData.female,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Age Distribution by Gender'
                    }
                }
            }
        });
    });
</script>
<?= $this->endSection() ?>