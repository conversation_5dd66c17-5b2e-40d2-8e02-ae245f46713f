<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\DistrictModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel as FarmBlockModel;
use App\Models\CropsFarmCropsDataModel as FarmCropsDataModel;
use App\Models\LlgModel;
use App\Models\ProvinceModel;
use App\Models\CropsModel;
use App\Models\UsersModel;
use App\Models\CropsFarmFertilizerDataModel as FarmFertilizerDataModel;
use App\Models\CropsFarmPesticidesDataModel as FarmPesticidesDataModel;
use App\Models\CropsFarmHarvestDataModel as FarmHarvestDataModel;
use App\Models\WardModel;
use App\Models\CropsFarmMarketingDataModel as FarmMarketingDataModel;
use App\Models\CropBuyersModel;
use App\Models\PesticidesModel;
use App\Models\FertilizersModel;
use App\Models\CropsFarmDiseaseDataModel as FarmDiseaseDataModel;
use App\Models\InfectionsModel;
use App\Models\LivestockFarmBlockModel;

class Staff_Farms extends BaseController
{
    protected $usersModel;
    protected $farmersModel;
    protected $farmBlockModel;
    protected $livestockFarmBlockModel; 
    protected $farmCropsDataModel;
    protected $provincesModel;
    protected $districtsModel;
    protected $llgsModel;
    protected $wardsModel;
    protected $cropsModel;
    protected $farmFertilizerDataModel;
    protected $farmPesticidesDataModel;
    protected $farmHarvestDataModel;
    protected $farmMarketingDataModel;
    protected $cropBuyersModel;
    protected $pesticidesModel;
    protected $fertilizersModel;
    protected $farmDiseaseDataModel;
    protected $infectionsModel;

    public function __construct()
    {
        helper(['url', 'form', 'info', 'weather']);
        
        // Check if user is logged in and has staff role
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        $this->usersModel = new UsersModel();
        $this->farmersModel = new FarmerInformationModel();
        $this->farmBlockModel = new FarmBlockModel();
        $this->livestockFarmBlockModel = new LivestockFarmBlockModel();
        $this->farmCropsDataModel = new FarmCropsDataModel();
        $this->provincesModel = new ProvinceModel();
        $this->districtsModel = new DistrictModel();
        $this->llgsModel = new LlgModel();
        $this->wardsModel = new WardModel();
        $this->cropsModel = new CropsModel();
        $this->farmFertilizerDataModel = new FarmFertilizerDataModel();
        $this->farmPesticidesDataModel = new FarmPesticidesDataModel();
        $this->farmHarvestDataModel = new FarmHarvestDataModel();
        $this->farmMarketingDataModel = new FarmMarketingDataModel();
        $this->cropBuyersModel = new CropBuyersModel();
        $this->pesticidesModel = new PesticidesModel();
        $this->fertilizersModel = new FertilizersModel();
        $this->farmDiseaseDataModel = new FarmDiseaseDataModel();
        $this->infectionsModel = new InfectionsModel();
    }

    /**
     * Verify district access
     */
    protected function verifyDistrictAccess($districtId) 
    {
        return $districtId == session()->get('district_id');
    }

    /**
     * Sanitize and validate input
     */
    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    // Farm Block Management
    public function view()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';
        
        $llgs = $this->llgsModel->where('district_id', session()->get('district_id'))->findAll();

        $data = [
            'title' => 'Farm Blocks',
            'page_header' => 'Farm Blocks',
            'farmers' => $this->farmersModel->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $this->farmBlockModel->select("
                crops_farm_blocks.*,
                CONCAT(farmer_information.given_name, ' ', farmer_information.surname) AS farmer_name,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ")
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'llgs' => $llgs,
            'crops' => $this->cropsModel->findAll(),
        ];

        return view('staff/farms/farm_blocks', $data);
    }

    public function farm_blocks($farmer_id = null)
    {
        $data = [
            'title' => 'Farm Blocks',
            'page_header' => 'Farm Blocks',
            'farmer' => $farmer_id ? $this->farmersModel->find($farmer_id) : null,
            'farmers' => $this->farmersModel->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $farmer_id ? $this->farmBlockModel->where('farmer_id', $farmer_id)
                ->where('district_id', session()->get('district_id'))
                ->orderBy('id', 'asc')
                ->findAll() : null,
            'districts' => $this->districtsModel->where('province_id', session()->get('orgprovince_id'))
                ->findAll(),
        ];

        return view('staff/farms/farm_blocks', $data);
    }

    public function add_farm_block()
    {
        try {
            $farmer_id = $this->request->getPost('farmer_id');
            $latest_block_code = $this->farmBlockModel->where('farmer_id', $farmer_id)->orderBy('id', 'DESC')->first();
            $farmer_code = $this->farmersModel->find($farmer_id)['farmer_code'];

            if ($latest_block_code) {
                $current_number = (int)substr($latest_block_code['block_code'], strpos($latest_block_code['block_code'], '-') + 1);
                $next_block_code = $farmer_code . '-' . sprintf('%03d', $current_number + 1);
            } else {
                $next_block_code = $farmer_code . '-001';
            }

            $data = [
                'farmer_id' => $farmer_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'block_code' => $next_block_code,
                'org_id' => session()->get('org_id'),
                'country_id' => session()->get('orgcountry_id'),
                'province_id' => session()->get('orgprovince_id'),
                'district_id' => session()->get('district_id'), // From session, not POST
                'llg_id' => $this->request->getPost('llg_id'),
                'ward_id' => $this->request->getPost('ward_id'),
                'village' => $this->request->getPost('village'),
                'block_site' => $this->request->getPost('block_site'),
                'lon' => $this->request->getPost('lon'),
                'lat' => $this->request->getPost('lat'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            $this->farmBlockModel->save($data);
            return $this->response->setJSON(['status' => 'success', 'message' => 'Farm block added successfully!']);
        } catch (\Exception $e) {
            log_message('error', '[Add Farm Block] ' . $e->getMessage());
            return $this->response->setJSON(['status' => 'error', 'message' => 'An error occurred while adding the farm block']);
        }
    }

    public function update_farm_block()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            $existing_block = $this->farmBlockModel->where('id', $block_id)->where('district_id', session()->get('district_id'))->first();

            if (!$existing_block) {
                throw new \Exception('Farm block not found or access denied');
            }

            $farmer_id = $this->request->getPost('farmer_id');
            $farmer_code = $this->farmersModel->find($farmer_id)['farmer_code'];

            // Regenerate block code if farmer changed
            if (substr($existing_block['block_code'], 0, strpos($existing_block['block_code'], '-')) !== $farmer_code) {
                $latest_block_code = $this->farmBlockModel->where('farmer_id', $farmer_id)->orderBy('id', 'DESC')->first();
                if ($latest_block_code) {
                    $current_number = (int)substr($latest_block_code['block_code'], strpos($latest_block_code['block_code'], '-') + 1);
                    $next_block_code = $farmer_code . '-' . sprintf('%03d', $current_number + 1);
                } else {
                    $next_block_code = $farmer_code . '-001';
                }
            } else {
                $next_block_code = $existing_block['block_code'];
            }

            $data = [
                'block_code' => $next_block_code,
                'farmer_id' => $farmer_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'llg_id' => $this->request->getPost('llg_id'),
                'ward_id' => $this->request->getPost('ward_id'),
                'village' => $this->request->getPost('village'),
                'block_site' => $this->request->getPost('block_site'),
                'lon' => $this->request->getPost('lon'),
                'lat' => $this->request->getPost('lat'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmBlockModel->update($block_id, $data);
            return $this->response->setJSON(['status' => 'success', 'message' => 'Farm block updated successfully!']);
        } catch (\Exception $e) {
            log_message('error', '[Update Farm Block] ' . $e->getMessage());
            return $this->response->setJSON(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    public function delete_farm_block($id)
    {
        try {
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmBlockModel->update($id, $data);

            return redirect()->back()->with('success', 'Farm block deleted successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Delete Farm Block] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while deleting the farm block');
        }
    }

    public function get_llgs()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $districtId = $this->request->getPost('district_id');
        if (!$districtId) {
            return $this->response->setJSON(['success' => false, 'message' => 'District ID is required']);
        }

        try {
            $llgs = $this->llgsModel->where('district_id', $districtId)->findAll();
            return $this->response->setJSON(['success' => true, 'llgs' => $llgs]);
        } catch (\Exception $e) {
            log_message('error', 'Error in get_llgs: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Database error occurred']);
        }
    }

    public function get_wards()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $llg_id = $this->request->getPost('llg_id');
        if (!$llg_id) {
            return $this->response->setJSON(['success' => false, 'message' => 'LLG ID is required']);
        }

        try {
            $wards = $this->wardsModel->where('llg_id', $llg_id)->findAll();
            return $this->response->setJSON(['success' => true, 'wards' => $wards]);
        } catch (\Exception $e) {
            log_message('error', 'Error in get_wards: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Database error occurred']);
        }
    }

    public function view_crop_blocks()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Farm Blocks List',
            'page_header' => 'Farm Blocks List',
            'farmers' => $this->farmersModel->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $this->farmBlockModel->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'crops' => $this->cropsModel->findAll(),
        ];

        return view('staff/farms/view_crop_blocks', $data);
    }

    public function view_crops_data($block_id)
    {
        $block = $this->farmBlockModel->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$block || ($block['crop_id'] == 0)) {
            return redirect()->back()->with('error', 'Block not found or Crop not selected');
        }

        $data = [
            'title' => 'Farm Block Data',
            'block' => $block,
            'crops_data' => $this->farmCropsDataModel->where('block_id', $block_id)->findAll(),
            'total_plants_added' => $this->farmCropsDataModel->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('number_of_plants', 'total_plants_added')
                ->first(),
            'total_plants_removed' => $this->farmCropsDataModel->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('number_of_plants', 'total_plants_removed')
                ->first(),
            'total_hectares_added' => $this->farmCropsDataModel->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('hectares', 'total_hectares_added')
                ->first(),
            'total_hectares_removed' => $this->farmCropsDataModel->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('hectares', 'total_hectares_removed')
                ->first(),
            'farmer' => $this->farmersModel->find($block['farmer_id']),
            'province' => $this->provincesModel->find($block['province_id']),
            'district' => $this->districtsModel->find($block['district_id']),
            'llg' => $this->llgsModel->find($block['llg_id']),
            'ward' => $this->wardsModel->find($block['ward_id']),
            'crop' => $this->cropsModel->find($block['crop_id']),
            'users' => $this->usersModel->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_crops_data', $data);
    }

    public function add_crops_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'block_id' => $block_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'action_type' => $this->request->getPost('action_type'),
                'action_reason' => $this->request->getPost('action_reason'),
                'action_date' => $this->request->getPost('action_date'),
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'hectares' => $this->request->getPost('hectares'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['action_type', 'action_date', 'number_of_plants', 'hectares'];
            $this->validateInput($data, $required);

            $this->farmCropsDataModel->save($data);
            
            //get weather data
            $weather_data = get_weather_data($block['id'], $block['lon'], $block['lat'], $data['action_date']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Crops data added successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_crops_data()
    {
        try {
            $id = $this->request->getPost('id');
            $crops_data = $this->farmCropsDataModel->find($id);
            
            if (!$crops_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $crops_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'action_type' => $this->request->getPost('action_type'),
                'action_reason' => $this->request->getPost('action_reason'),
                'action_date' => $this->request->getPost('action_date'),
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'hectares' => $this->request->getPost('hectares'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['action_type', 'action_date', 'number_of_plants', 'hectares'];
            $this->validateInput($data, $required);

            $this->farmCropsDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Crops data updated successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_block_data($id)
    {
        try {
            $crops_data = $this->farmCropsDataModel->find($id);
            
            if (!$crops_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $crops_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmCropsDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Block Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to delete record'
            ]);
        }
    }

    public function delete_crops_data($id)
    {
        try {
            $crops_data = $this->farmCropsDataModel->find($id);
            
            if (!$crops_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $crops_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmCropsDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Fertilizer Management
    public function fertilizer_data()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Fertilizer Data',
            'page_header' => 'Fertilizer Data',
            'farm_blocks' => $this->farmBlockModel->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'fertilizers' => $this->fertilizersModel->findAll()
        ];

        return view('staff/farms/fertilizer_data', $data);
    }

    public function view_fertilizer_data($block_id)
    {
        $block = $this->farmBlockModel->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$block) {
            return redirect()->back()->with('error', 'Block not found or access denied');
        }

        $data = [
            'title' => 'Block Fertilizer Data',
            'block' => $block,
            'fertilizer_data' => $this->farmFertilizerDataModel->where('block_id', $block_id)->findAll(),
            'farmer' => $this->farmersModel->find($block['farmer_id']),
            'province' => $this->provincesModel->find($block['province_id']),
            'district' => $this->districtsModel->find($block['district_id']),
            'llg' => $this->llgsModel->find($block['llg_id']),
            'ward' => $this->wardsModel->find($block['ward_id']),
            'crop' => $this->cropsModel->find($block['crop_id']),
            'fertilizers' => $this->fertilizersModel->findAll(),
            'users' => $this->usersModel->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_fertilizer_data', $data);
    }

    public function add_fertilizer_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'block_id' => $block_id,
                'fertilizer_id' => $this->request->getPost('fertilizer_id'),
                'crop_id' => $this->request->getPost('crop_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['fertilizer_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->farmFertilizerDataModel->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Fertilizer data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Fertilizer Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_fertilizer_data()
    {
        try {
            $id = $this->request->getPost('id');
            $fertilizer_data = $this->farmFertilizerDataModel->find($id);
            
            if (!$fertilizer_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $fertilizer_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'fertilizer_id' => $this->request->getPost('fertilizer_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['fertilizer_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->farmFertilizerDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Fertilizer data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Fertilizer Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_fertilizer_data($id)
    {
        try {
            $fertilizer_data = $this->farmFertilizerDataModel->find($id);
            
            if (!$fertilizer_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $fertilizer_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmFertilizerDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Fertilizer data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Fertilizer Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Pesticides Management
    public function pesticides_data()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Pesticides Data',
            'page_header' => 'Pesticides Data',
            'farm_blocks' => $this->farmBlockModel->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'pesticides' => $this->pesticidesModel->findAll()
        ];

        return view('staff/farms/pesticides_data', $data);
    }

    public function view_pesticides_data($block_id)
    {
        $block = $this->farmBlockModel->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$block) {
            return redirect()->back()->with('error', 'Block not found or access denied');
        }

        $data = [
            'title' => 'Block Pesticides Data',
            'block' => $block,
            'pesticides_data' => $this->farmPesticidesDataModel->where('block_id', $block_id)->findAll(),
            'farmer' => $this->farmersModel->find($block['farmer_id']),
            'province' => $this->provincesModel->find($block['province_id']),
            'district' => $this->districtsModel->find($block['district_id']),
            'llg' => $this->llgsModel->find($block['llg_id']),
            'ward' => $this->wardsModel->find($block['ward_id']),
            'crop' => $this->cropsModel->find($block['crop_id']),
            'pesticides' => $this->pesticidesModel->findAll(),
            'users' => $this->usersModel->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_pesticides_data', $data);
    }

    public function add_pesticides_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'block_id' => $block_id,
                'pesticide_id' => $this->request->getPost('pesticide_id'),
                'crop_id' => $this->request->getPost('crop_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['pesticide_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->farmPesticidesDataModel->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pesticides data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_pesticides_data()
    {
        try {
            $id = $this->request->getPost('id');
            $pesticides_data = $this->farmPesticidesDataModel->find($id);
            
            if (!$pesticides_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $pesticides_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'pesticide_id' => $this->request->getPost('pesticide_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['pesticide_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->farmPesticidesDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pesticides data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_pesticides_data($id)
    {
        try {
            $pesticides_data = $this->farmPesticidesDataModel->find($id);
            
            if (!$pesticides_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $pesticides_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmPesticidesDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pesticides data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Harvest Management
    public function harvest_data()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Harvest Data',
            'page_header' => 'Harvest Data',
            'farm_blocks' => $this->farmBlockModel->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName
        ];

        return view('staff/farms/harvest_data', $data);
    }

    public function view_harvest_data($block_id)
    {
        $block = $this->farmBlockModel->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$block) {
            return redirect()->back()->with('error', 'Block not found or access denied');
        }

        $crop = $this->cropsModel->find($block['crop_id']);
        if (!$crop) {
            return redirect()->back()->with('error', 'Crop information not found');
        }

        $data = [
            'title' => 'Block Harvest Data',
            'block' => $block,
            'harvest_data' => $this->farmHarvestDataModel->where('block_id', $block_id)
                ->where('status', 'active')
                ->findAll(),
            'farmer' => $this->farmersModel->find($block['farmer_id']),
            'province' => $this->provincesModel->find($block['province_id']),
            'district' => $this->districtsModel->find($block['district_id']),
            'llg' => $this->llgsModel->find($block['llg_id']),
            'ward' => $this->wardsModel->find($block['ward_id']),
            'crop' => $crop,
            'users' => $this->usersModel->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_harvest_data', $data);
    }

    public function add_harvest_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'block_id' => $block_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'item' => $this->request->getPost('item'),
                'description' => $this->request->getPost('description'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'unit' => $this->request->getPost('unit'),
                'quantity' => $this->request->getPost('quantity'),
                'harvest_date' => $this->request->getPost('harvest_date'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['item', 'unit_of_measure', 'unit', 'quantity', 'harvest_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->farmHarvestDataModel->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Harvest data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_harvest_data()
    {
        try {
            $id = $this->request->getPost('id');
            $harvest_data = $this->farmHarvestDataModel->find($id);
            
            if (!$harvest_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $harvest_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'item' => $this->request->getPost('item'),
                'description' => $this->request->getPost('description'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'unit' => $this->request->getPost('unit'),
                'quantity' => $this->request->getPost('quantity'),
                'harvest_date' => $this->request->getPost('harvest_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['item', 'unit_of_measure', 'unit', 'quantity', 'harvest_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->farmHarvestDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Harvest data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_harvest_data($id)
    {
        try {
            $harvest_data = $this->farmHarvestDataModel->find($id);
            
            if (!$harvest_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $harvest_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmHarvestDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Harvest data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Marketing Management
    public function marketing_data()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Marketing Data',
            'page_header' => 'Marketing Data',
            'farmers' => $this->farmersModel->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'district_name' => $districtName
        ];

        return view('staff/farms/marketing_data', $data);
    }

    public function view_market_data($farmer_id)
    {
        $farmer = $this->farmersModel->where('id', $farmer_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$farmer) {
            return redirect()->back()->with('error', 'Farmer not found or access denied');
        }

        $data = [
            'title' => 'Farmer Marketing Data',
            'farmer' => $farmer,
            'marketing_data' => $this->farmMarketingDataModel->where('farmer_id', $farmer_id)
                ->where('status !=', 'deleted')
                ->findAll(),
            'crops' => $this->cropsModel->findAll(),
            'buyers' => $this->cropBuyersModel->where('status', 'active')->findAll(),
            'users' => $this->usersModel->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_market_data', $data);
    }

    public function add_market_data()
    {
        try {
            $farmer_id = $this->request->getPost('farmer_id');
            
            // Verify farmer belongs to user's district
            $farmer = $this->farmersModel->where('id', $farmer_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$farmer) {
                throw new \Exception('Farmer not found or access denied');
            }

            $data = [
                'farmer_id' => $farmer_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'item' => $this->request->getPost('item'),
                'market_stage' => $this->request->getPost('market_stage'),
                'item_type' => $this->request->getPost('item_type'),
                'description' => $this->request->getPost('description'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'market_price_per_unit' => $this->request->getPost('market_price_per_unit'),
                'market_date' => $this->request->getPost('market_date'),
                'buyer_id' => $this->request->getPost('buyer_id'),
                'total_freight_cost' => $this->request->getPost('total_freight_cost'),
                'selling_location' => $this->request->getPost('selling_location'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = [
                'crop_id',
                'item',
                'market_stage',
                'item_type',
                'unit',
                'unit_of_measure',
                'quantity',
                'market_price_per_unit',
                'market_date',
                'buyer_id'
            ];
            $this->validateInput($data, $required);

            // Validate numeric fields
            $numeric_fields = ['unit', 'quantity', 'market_price_per_unit'];
            foreach ($numeric_fields as $field) {
                if (!is_numeric($data[$field]) || $data[$field] <= 0) {
                    throw new \Exception("The {$field} must be a positive number.");
                }
            }

            // Validate total_freight_cost if provided
            if (!empty($data['total_freight_cost']) && (!is_numeric($data['total_freight_cost']) || $data['total_freight_cost'] < 0)) {
                throw new \Exception("Total freight cost must be a non-negative number.");
            }

            $this->farmMarketingDataModel->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Marketing data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Marketing Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_market_data()
    {
        try {
            $id = $this->request->getPost('id');
            $marketing_data = $this->farmMarketingDataModel->find($id);
            
            if (!$marketing_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify farmer belongs to user's district
            $farmer = $this->farmersModel->where('id', $marketing_data['farmer_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$farmer) {
                throw new \Exception('Access denied');
            }

            $data = [
                'crop_id' => $this->request->getPost('crop_id'),
                'item' => $this->request->getPost('item'),
                'market_stage' => $this->request->getPost('market_stage'),
                'item_type' => $this->request->getPost('item_type'),
                'description' => $this->request->getPost('description'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'market_price_per_unit' => $this->request->getPost('market_price_per_unit'),
                'market_date' => $this->request->getPost('market_date'),
                'buyer_id' => $this->request->getPost('buyer_id'),
                'total_freight_cost' => $this->request->getPost('total_freight_cost'),
                'selling_location' => $this->request->getPost('selling_location'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = [
                'crop_id',
                'item',
                'market_stage',
                'item_type',
                'unit',
                'unit_of_measure',
                'quantity',
                'market_price_per_unit',
                'market_date',
                'buyer_id'
            ];
            $this->validateInput($data, $required);

            // Validate numeric fields
            $numeric_fields = ['unit', 'quantity', 'market_price_per_unit'];
            foreach ($numeric_fields as $field) {
                if (!is_numeric($data[$field]) || $data[$field] <= 0) {
                    throw new \Exception("The {$field} must be a positive number.");
                }
            }

            // Validate total_freight_cost if provided
            if (!empty($data['total_freight_cost']) && (!is_numeric($data['total_freight_cost']) || $data['total_freight_cost'] < 0)) {
                throw new \Exception("Total freight cost must be a non-negative number.");
            }

            $this->farmMarketingDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Marketing data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Marketing Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_market_data($id)
    {
        try {
            $marketing_data = $this->farmMarketingDataModel->find($id);
            
            if (!$marketing_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify farmer belongs to user's district
            $farmer = $this->farmersModel->where('id', $marketing_data['farmer_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$farmer) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmMarketingDataModel->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Marketing data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Marketing Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function edit_market_data($id)
    {
        $marketing_data = $this->farmMarketingDataModel->find($id);
        if (!$marketing_data) {
            return redirect()->back()->with('error', 'Marketing data not found');
        }

        $farmer = $this->farmersModel->where('id', $marketing_data['farmer_id'])
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$farmer) {
            return redirect()->back()->with('error', 'Access denied');
        }

        $data = [
            'title' => 'Edit Marketing Data',
            'page_header' => 'Edit Marketing Data',
            'marketing_data' => $marketing_data,
            'farmer' => $farmer,
            'crops' => $this->cropsModel->findAll(),
            'buyers' => $this->cropBuyersModel->where('status', 'active')->findAll(),
        ];

        return view('staff/farms/edit_market_data', $data);
    }

    // Disease Management
    public function diseases_data()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Diseases Data',
            'page_header' => 'Diseases Data',
            'farm_blocks' => $this->farmBlockModel->select("
                crops_farm_blocks.*,
                CONCAT(farmer_information.given_name, ' ', farmer_information.surname) AS farmer_name,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ")
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName
        ];

        return view('staff/farms/diseases_data', $data);
    }

    public function view_diseases_data($block_id)
    {
        $block = $this->farmBlockModel->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$block) {
            return redirect()->back()->with('error', 'Block not found or access denied');
        }

        $data = [
            'title' => 'Block Diseases Data',
            'block' => $block,
            'diseases_data' => $this->farmDiseaseDataModel->where('block_id', $block_id)
                ->where('status', 'active')
                ->findAll(),
            'farmer' => $this->farmersModel->find($block['farmer_id']),
            'province' => $this->provincesModel->find($block['province_id']),
            'district' => $this->districtsModel->find($block['district_id']),
            'llg' => $this->llgsModel->find($block['llg_id']),
            'ward' => $this->wardsModel->find($block['ward_id']),
            'crop' => $this->cropsModel->find($block['crop_id']),
            'infections' => $this->infectionsModel->findAll(),
            'users' => $this->usersModel->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_diseases_data', $data);
    }

    public function add_diseases_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'block_id' => $block_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'disease_type' => $this->request->getPost('disease_type'),
                'disease_name' => $this->request->getPost('disease_name'),
                'description' => $this->request->getPost('description'),
                'action_reason' => 'Disease Infection',
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'action_date' => $this->request->getPost('action_date'),
                'hectares' => $this->request->getPost('hectares'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = [
                'disease_type',
                'disease_name',
                'action_date',
                'number_of_plants',
                'hectares'
            ];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['number_of_plants']) || $data['number_of_plants'] <= 0) {
                throw new \Exception("Number of plants must be a positive number.");
            }
            if (!is_numeric($data['hectares']) || $data['hectares'] <= 0) {
                throw new \Exception("Hectares must be a positive number.");
            }

            $this->farmDiseaseDataModel->insert($data);

            // Also record in crops data as removal
            $this->farmCropsDataModel->insert([
                'block_id' => $data['block_id'],
                'crop_id' => $data['crop_id'],
                'action_type' => 'remove',
                'action_reason' => 'Disease Infection',
                'number_of_plants' => $data['number_of_plants'],
                'breed' => $data['breed'],
                'action_date' => $data['action_date'],
                'hectares' => $data['hectares'],
                'remarks' => $data['remarks'],
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ]);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Disease data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_diseases_data()
    {
        try {
            $id = $this->request->getPost('id');
            $disease_data = $this->farmDiseaseDataModel->find($id);
            
            if (!$disease_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $disease_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'disease_type' => $this->request->getPost('disease_type'),
                'disease_name' => $this->request->getPost('disease_name'),
                'description' => $this->request->getPost('description'),
                'action_reason' => 'Disease Infection',
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'action_date' => $this->request->getPost('action_date'),
                'hectares' => $this->request->getPost('hectares'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = [
                'disease_type',
                'disease_name',
                'action_date',
                'number_of_plants',
                'hectares'
            ];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['number_of_plants']) || $data['number_of_plants'] <= 0) {
                throw new \Exception("Number of plants must be a positive number.");
            }
            if (!is_numeric($data['hectares']) || $data['hectares'] <= 0) {
                throw new \Exception("Hectares must be a positive number.");
            }

            $this->farmDiseaseDataModel->update($id, $data);

            // Update corresponding crops data entry
            $crops_data = $this->farmCropsDataModel->where([
                'block_id' => $disease_data['block_id'],
                'action_type' => 'remove',
                'action_reason' => 'Disease Infection',
                'action_date' => $disease_data['action_date']
            ])->first();

            if ($crops_data) {
                $this->farmCropsDataModel->update($crops_data['id'], [
                    'number_of_plants' => $data['number_of_plants'],
                    'breed' => $data['breed'],
                    'action_date' => $data['action_date'],
                    'hectares' => $data['hectares'],
                    'remarks' => $data['remarks'],
                    'updated_by' => session()->get('emp_id'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Disease data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_diseases_data($id)
    {
        try {
            $disease_data = $this->farmDiseaseDataModel->find($id);
            
            if (!$disease_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $disease_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmDiseaseDataModel->update($id, $data);

            // Also update corresponding crops data entry
            $crops_data = $this->farmCropsDataModel->where([
                'block_id' => $disease_data['block_id'],
                'action_type' => 'remove',
                'action_reason' => 'Disease Infection',
                'action_date' => $disease_data['action_date']
            ])->first();

            if ($crops_data) {
                $this->farmCropsDataModel->update($crops_data['id'], $data);
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Disease data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Maps
    public function maps()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Field Maps',
            'page_header' => 'Field Maps',
            'farm_blocks' => $this->farmBlockModel->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'crops' => $this->cropsModel->findAll()
        ];

        return view('staff/farms/maps', $data);
    }
}
