<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">Farmers Report</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Farmers Report</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">Summary Statistics</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-6">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Farmers</span>
                                        <span class="info-box-number"><?= $summary['total_farmers'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-6">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-map-marker-alt"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Farm Blocks</span>
                                        <span class="info-box-number"><?= $summary['total_blocks'] ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-6">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-dollar-sign"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Revenue</span>
                                        <span class="info-box-number">K<?= number_format($summary['total_revenue'], 2) ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-6">
                                <div class="info-box bg-danger">
                                    <span class="info-box-icon"><i class="fas fa-user-check"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Active Farmers</span>
                                        <span class="info-box-number"><?= $summary['active_farmers'] ?? 0 ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farmers List Card -->
        <div class="row">
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list mr-1"></i>
                            Farmers List
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-tool" data-card-widget="maximize">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="farmers-table" class="table table-bordered table-striped table-hover" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Farmer Code</th>
                                        <th>Name</th>
                                        <th>Location</th>
                                        <th>Blocks</th>
                                        <th>Revenue</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($farmers as $farmer): ?>
                                        <tr>
                                            <td class="text-monospace"><?= esc($farmer['farmer_code']) ?></td>
                                            <td><?= esc($farmer['given_name']) ?> <?= esc($farmer['surname']) ?></td>
                                            <td>
                                                <small>
                                                    <?= esc($farmer['village'] ?? '') ?>
                                                    <?= $farmer['ward_name'] ? ', ' . esc($farmer['ward_name']) : '' ?>
                                                    <?= $farmer['llg_name'] ? ', ' . esc($farmer['llg_name']) : '' ?>
                                                    <?= $farmer['district_name'] ? ', ' . esc($farmer['district_name']) : '' ?>
                                                </small>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge badge-pill badge-info">
                                                    <?= $farmer['total_blocks'] ?>
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <span class="text-success">
                                                    K<?= number_format($farmer['total_revenue'], 2) ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge badge-pill badge-<?= $farmer['status'] == 'active' ? 'success' : 'danger' ?>">
                                                    <?= ucfirst($farmer['status']) ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <a href="<?= base_url('reports/farmer-profile/' . $farmer['id']) ?>" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(document).ready(function() {
    $('#farmers-table').DataTable({
        "responsive": true,
        "scrollX": true,
        "scrollCollapse": true,
        "autoWidth": false,
        "pageLength": 25,
        "fixedHeader": true,
        "dom": "<'row'<'col-sm-12 col-md-6'B><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "buttons": [
            {
                extend: 'excel',
                className: 'btn-sm btn-success',
                text: '<i class="fas fa-file-excel mr-1"></i> Excel'
            },
            {
                extend: 'pdf',
                className: 'btn-sm btn-danger',
                text: '<i class="fas fa-file-pdf mr-1"></i> PDF'
            },
            {
                extend: 'print',
                className: 'btn-sm btn-info',
                text: '<i class="fas fa-print mr-1"></i> Print'
            }
        ]
    }).buttons().container().appendTo('#farmers-table_wrapper .col-md-6:eq(0)');
});
</script>

<style>
.info-box {
    min-height: 100px;
    padding: 15px;
    border-radius: 0.25rem;
}
.info-box-icon {
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 2rem;
}
.info-box-content {
    padding: 5px 10px;
    margin-left: 70px;
}
.info-box-number {
    font-size: 1.5rem;
    font-weight: bold;
}
.table td {
    padding: 0.5rem;
}
.badge-pill {
    padding-right: 1em;
    padding-left: 1em;
}
.modal-body .nav-tabs {
    border-bottom: 2px solid #dee2e6;
}
.modal-body .nav-tabs .nav-link.active {
    border-bottom: 2px solid #28a745;
    border-color: transparent transparent #28a745 transparent;
}
.modal-body .nav-tabs .nav-link:hover {
    border-color: transparent transparent #28a745 transparent;
}
.text-monospace {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.dataTables_wrapper .dataTables_scroll {
    margin-bottom: 1rem;
}

.table {
    margin-bottom: 0;
    width: 100% !important;
}

.table th, .table td {
    white-space: nowrap;
    vertical-align: middle !important;
}

.table-responsive {
    min-height: 400px;
}

.dataTables_wrapper .row:first-child,
.dataTables_wrapper .row:last-child {
    margin: 0;
    padding: 1rem 0;
}

/* Fix for button group alignment */
.dt-buttons {
    padding-left: 1rem;
}

/* Ensure consistent padding in cells */
.table td, .table th {
    padding: 0.75rem !important;
}
</style>

<?= $this->endSection() ?>
