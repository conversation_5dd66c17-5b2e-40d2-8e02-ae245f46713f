<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">Livestock Management</h2>
            <p class="text-muted mb-0">Manage livestock types and animal husbandry information</p>
        </div>
        <div class="btn-group">
            <a href="<?= base_url('dakoii/data') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Data
            </a>
            <a href="<?= base_url('dakoii/data/livestock/create') ?>" class="btn btn-info">
                <i class="fas fa-plus"></i> Add Livestock
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Livestock Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-cow"></i> Livestock Types
            </h5>
        </div>
        <div class="card-body">
            <?php if (!empty($livestock)): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Icon</th>
                                <th>Name</th>
                                <th>Color</th>
                                <th>Remarks</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($livestock as $animal): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($animal['icon'])): ?>
                                            <img src="<?= base_url($animal['icon']) ?>" 
                                                 alt="<?= esc($animal['name']) ?>" 
                                                 class="img-thumbnail" 
                                                 style="width: 40px; height: 40px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-cow text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?= esc($animal['name']) ?></strong>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="color-preview me-2" 
                                                 style="width: 20px; height: 20px; background-color: <?= esc($animal['color_code'] ?: '#6f42c1') ?>; border-radius: 3px; border: 1px solid #ddd;"></div>
                                            <small class="text-muted"><?= esc($animal['color_code'] ?: '#6f42c1') ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?= !empty($animal['remarks']) ? esc($animal['remarks']) : '<span class="text-muted">No remarks</span>' ?>
                                    </td>
                                    <td>
                                        <?php if ($animal['status'] == 1): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('dakoii/data/livestock/edit/' . $animal['id']) ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit Livestock">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('dakoii/data/livestock/delete/' . $animal['id']) ?>" 
                                               class="btn btn-sm btn-outline-danger" 
                                               title="Delete Livestock"
                                               onclick="return confirm('Are you sure you want to delete this livestock?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-cow fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No livestock found</h5>
                    <p class="text-muted">Start by adding your first livestock type.</p>
                    <a href="<?= base_url('dakoii/data/livestock/create') ?>" class="btn btn-info">
                        <i class="fas fa-plus"></i> Add Livestock
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.table td {
    vertical-align: middle;
}

.color-preview {
    display: inline-block;
}

/* Ensure table header is always dark with white text */
.table thead th, .table thead td, .table-dark th, .table-dark td {
    background-color: #343a40 !important;
    color: #fff !important;
}
</style>

<?= $this->endSection() ?>
