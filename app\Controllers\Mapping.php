<?php

namespace App\Controllers;

use App\Models\CropsFarmBlockModel;
use App\Models\CropsModel;

class Mapping extends BaseController
{
    protected $farmBlockModel;
    protected $cropsModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        
        $this->farmBlockModel = new CropsFarmBlockModel();
        $this->cropsModel = new CropsModel();
    }

    public function index()
    {
        // Get crops with their icons
        $crops = $this->cropsModel
            ->select('id, crop_name as item, crop_icon as icons')
            ->findAll();

        $data = [
            'title' => 'GPS Mapping',
            'menu' => 'mapping',
            'page_header' => 'GPS Mapping',
            'page_desc' => 'Farm Blocks Distribution Map',
            'crops' => $crops
        ];

        // Get all farm blocks with coordinates and crop information
        $data['farm_blocks'] = $this->farmBlockModel
            ->select('block_code, block_site, lat, lon, farmer_id, crop_id')
            ->join('farmer_information', 'crops_farm_blocks.farmer_id = farmer_information.id', 'left')
            ->join('adx_crops', 'crops_farm_blocks.crop_id = adx_crops.id', 'left')
            ->select('farmer_information.given_name, farmer_information.surname, adx_crops.crop_name as crop_name, adx_crops.crop_icon as icons')
            ->where('lat IS NOT NULL')
            ->where('lon IS NOT NULL')
            ->findAll();

        return view('mapping/index', $data);
    }
}
