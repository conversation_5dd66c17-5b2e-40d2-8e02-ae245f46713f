<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Helpers\DummyDataHelper;

class FarmerController extends BaseController
{
    public function __construct()
    {
        helper(['form', 'url']);
    }

    /**
     * Display a listing of farmers
     * GET /staff/farmers
     */
    public function index()
    {
        // Get dummy farmers data
        $farmers = DummyDataHelper::getFarmers();

        $data = [
            'title' => 'Farmers Management',
            'page_header' => 'Farmers Management',
            'farmers' => $farmers,
            'total_farmers' => count($farmers),
            'stats' => DummyDataHelper::getFarmersReport()
        ];

        return view('staff/farmers/farmers_index', $data);
    }

    /**
     * Show the form for creating a new farmer
     * GET /staff/farmers/create
     */
    public function create()
    {
        $data = [
            'title' => 'Add New Farmer',
            'page_header' => 'Add New Farmer',
            'provinces' => DummyDataHelper::getProvinces(),
            'districts' => DummyDataHelper::getDistricts(),
            'education_levels' => [
                ['id' => 1, 'name' => 'No Formal Education'],
                ['id' => 2, 'name' => 'Primary School'],
                ['id' => 3, 'name' => 'Secondary School'],
                ['id' => 4, 'name' => 'High School'],
                ['id' => 5, 'name' => 'Technical/Vocational'],
                ['id' => 6, 'name' => 'University/College']
            ]
        ];

        return view('staff/farmers/farmers_create', $data);
    }

    /**
     * Store a newly created farmer
     * POST /staff/farmers
     */
    public function store()
    {
        // Simulate successful creation
        session()->setFlashdata('success', 'Farmer created successfully! (Demo Mode - No data saved)');
        return redirect()->to('staff/farmers');
    }

    /**
     * Display the specified farmer
     * GET /staff/farmers/{id}
     */
    public function show($id)
    {
        $farmers = DummyDataHelper::getFarmers();
        $farmer = null;
        
        foreach ($farmers as $f) {
            if ($f['id'] == $id) {
                $farmer = $f;
                break;
            }
        }
        
        if (!$farmer) {
            session()->setFlashdata('error', 'Farmer not found');
            return redirect()->to('staff/farmers');
        }

        $data = [
            'title' => 'Farmer Profile - ' . $farmer['full_name'],
            'page_header' => 'Farmer Profile',
            'farmer' => $farmer,
            'crop_blocks' => array_filter(DummyDataHelper::getCropFarmBlocks(), fn($b) => $b['farmer_id'] == $id),
            'livestock_blocks' => array_filter(DummyDataHelper::getLivestockFarmBlocks(), fn($b) => $b['farmer_id'] == $id)
        ];

        return view('staff/farmers/farmers_show', $data);
    }

    /**
     * Show the form for editing the specified farmer
     * GET /staff/farmers/{id}/edit
     */
    public function edit($id)
    {
        $farmers = DummyDataHelper::getFarmers();
        $farmer = null;
        
        foreach ($farmers as $f) {
            if ($f['id'] == $id) {
                $farmer = $f;
                break;
            }
        }
        
        if (!$farmer) {
            session()->setFlashdata('error', 'Farmer not found');
            return redirect()->to('staff/farmers');
        }

        $data = [
            'title' => 'Edit Farmer - ' . $farmer['full_name'],
            'page_header' => 'Edit Farmer',
            'farmer' => $farmer,
            'provinces' => DummyDataHelper::getProvinces(),
            'districts' => DummyDataHelper::getDistricts(),
            'education_levels' => [
                ['id' => 1, 'name' => 'No Formal Education'],
                ['id' => 2, 'name' => 'Primary School'],
                ['id' => 3, 'name' => 'Secondary School'],
                ['id' => 4, 'name' => 'High School'],
                ['id' => 5, 'name' => 'Technical/Vocational'],
                ['id' => 6, 'name' => 'University/College']
            ]
        ];

        return view('staff/farmers/farmers_edit', $data);
    }

    /**
     * Update the specified farmer
     * PUT /staff/farmers/{id}
     */
    public function update($id)
    {
        // Simulate successful update
        session()->setFlashdata('success', 'Farmer updated successfully! (Demo Mode - No data saved)');
        return redirect()->to('staff/farmers/' . $id);
    }

    /**
     * Remove the specified farmer
     * DELETE /staff/farmers/{id}
     */
    public function delete($id)
    {
        // Simulate successful deletion
        session()->setFlashdata('success', 'Farmer deleted successfully! (Demo Mode - No data actually deleted)');
        return redirect()->to('staff/farmers');
    }
}