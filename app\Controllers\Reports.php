<?php

namespace App\Controllers;

use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\FarmersChildrenModel;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\provinceModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\wardModel;
use App\Models\CropsModel;
use App\Models\CropBuyersModel;
use App\Models\CropsFarmCropsDataModel;
use App\Models\CropsFarmFertilizerDataModel;
use App\Models\CropsFarmHarvestDataModel;
use App\Models\CropsFarmPesticidesDataModel;

class Reports extends BaseController
{
    protected $farmerModel;
    protected $farmBlockModel;
    protected $farmersChildrenModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $cropsModel;
    protected $cropBuyersModel;
    protected $farmCropsDataModel;
    protected $harvestDataModel;
    protected $fertilizerDataModel;
    protected $pesticideDataModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        
        $this->farmerModel = new FarmerInformationModel();
        $this->farmBlockModel = new CropsFarmBlockModel();
        $this->farmersChildrenModel = new FarmersChildrenModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->wardModel = new wardModel();
        $this->cropsModel = new CropsModel();
        $this->cropBuyersModel = new CropBuyersModel();
        $this->farmCropsDataModel = new CropsFarmCropsDataModel();
        $this->harvestDataModel = new CropsFarmHarvestDataModel();
        $this->fertilizerDataModel = new CropsFarmFertilizerDataModel();
        $this->pesticideDataModel = new CropsFarmPesticidesDataModel();
    }

    public function farmersReport()
    {
        // Get all farmers with their details
        $farmers = $this->farmerModel->findAll();

        // Get farm blocks for each farmer
        foreach ($farmers as &$farmer) {
            // Get location information safely
            $province = null;
            $district = null;
            $llg = null;
            $ward = null;
            
            if (!empty($farmer['province_id'])) {
                $province = $this->provinceModel->find($farmer['province_id']);
            }
            if (!empty($farmer['district_id'])) {
                $district = $this->districtModel->find($farmer['district_id']);
            }
            if (!empty($farmer['llg_id'])) {
                $llg = $this->llgModel->find($farmer['llg_id']);
            }
            if (!empty($farmer['ward_id'])) {
                $ward = $this->wardModel->find($farmer['ward_id']);
            }
            
            // Add location names with safe checks
            $farmer['province_name'] = !empty($province) && isset($province['name']) ? $province['name'] : '';
            $farmer['district_name'] = !empty($district) && isset($district['name']) ? $district['name'] : '';
            $farmer['llg_name'] = !empty($llg) && isset($llg['name']) ? $llg['name'] : '';
            $farmer['ward_name'] = !empty($ward) && isset($ward['name']) ? $ward['name'] : '';
            
            // Get farm blocks
            $farmer['farm_blocks'] = $this->farmBlockModel->where('farmer_id', $farmer['id'])->findAll();
            
            // Get children data
            $farmer['children'] = $this->farmersChildrenModel->where('farmer_id', $farmer['id'])->findAll();
            
            // Calculate total blocks
            $farmer['total_blocks'] = count($farmer['farm_blocks']);
        }

        $data = [
            'title' => 'Farmers Report',
            'menu' => 'farmers-report',
            'farmers' => $farmers
        ];

        return view('reports/farmers_report', $data);
    }

    public function farmerProfile($id)
    {
        // Get farmer details
        $farmer = $this->farmerModel->find($id);
        if (!$farmer) {
            return redirect()->back()->with('error', 'Farmer not found');
        }

        // Get location information safely
        $province = null;
        $district = null;
        $llg = null;
        $ward = null;
        
        if (!empty($farmer['province_id'])) {
            $province = $this->provinceModel->find($farmer['province_id']);
        }
        if (!empty($farmer['district_id'])) {
            $district = $this->districtModel->find($farmer['district_id']);
        }
        if (!empty($farmer['llg_id'])) {
            $llg = $this->llgModel->find($farmer['llg_id']);
        }
        if (!empty($farmer['ward_id'])) {
            $ward = $this->wardModel->find($farmer['ward_id']);
        }
        
        // Add location names with safe checks
        $farmer['province_name'] = !empty($province) && isset($province['name']) ? $province['name'] : '';
        $farmer['district_name'] = !empty($district) && isset($district['name']) ? $district['name'] : '';
        $farmer['llg_name'] = !empty($llg) && isset($llg['name']) ? $llg['name'] : '';
        $farmer['ward_name'] = !empty($ward) && isset($ward['name']) ? $ward['name'] : '';
        
        // Get farm blocks
        $farmer['farm_blocks'] = $this->farmBlockModel->where('farmer_id', $farmer['id'])->findAll();
        
        // Get children data
        $farmer['children'] = $this->farmersChildrenModel->where('farmer_id', $farmer['id'])->findAll();
        
        // Calculate total blocks
        $farmer['total_blocks'] = count($farmer['farm_blocks']);

        $data = [
            'title' => 'Farmer Profile',
            'menu' => 'farmers-report',
            'farmer' => $farmer
        ];

        return view('reports/farmer_profile', $data);
    }

    public function cropBuyers()
    {
        // Get all buyers with their transactions
        $buyers = $this->cropBuyersModel->findAll();
        foreach ($buyers as &$buyer) {
            // Get location name based on operation span
            $buyer['location_name'] = '';
            if ($buyer['operation_span'] === 'local' && !empty($buyer['location_id'])) {
                $province = $this->provinceModel->find($buyer['location_id']);
                $buyer['location_name'] = $province ? $province['name'] : '';
            } elseif ($buyer['operation_span'] === 'national' && !empty($buyer['location_id'])) {
                $buyer['location_name'] = 'Papua New Guinea';
            }
        }

        $data = [
            'title' => 'Crop Buyers Report',
            'menu' => 'crop-buyers',
            'buyers' => $buyers
        ];

        return view('reports/crop_buyers', $data);
    }

    public function farmBlocksReport()
    {
        $data = [
            'title' => 'Farm Blocks Report',
            'menu' => 'farm-blocks',
            'blocks' => $this->farmBlockModel->getFarmBlocksWithDetails()
        ];

        return view('reports/farm_blocks', $data);
    }
}
