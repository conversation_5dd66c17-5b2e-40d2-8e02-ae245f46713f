<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Routing extends BaseConfig
{
    /**
     * The default namespace for controllers that is used
     * when no namespace has been specified.
     * If executing namespace is defined, this value will be ignored.
     *
     * @var string
     */
    public string $defaultNamespace = 'App\\Controllers';

    /**
     * The default controller used when no controller has been
     * specified in the route.
     *
     * @var string
     */
    public string $defaultController = 'Home';

    /**
     * The default method called on the controller when no method
     * has been specified.
     *
     * @var string
     */
    public string $defaultMethod = 'index';

    /**
     * Whether to translate dashes in URI segments into underscores
     * when determining the method name.
     *
     * @var bool
     */
    public bool $translateURIDashes = false;

    /**
     * Sets the class/method that should be called if routing doesn't
     * find a match. It can be any callable, even a closure.
     *
     * @var string|callable
     */
    public $override404 = null;

    /**
     * Paths to the files that contain the Route Definitions.
     *
     * @var array<string>
     */
    public array $routeFiles = [APPPATH . 'Config/Routes.php'];

    /**
     * Map of URI segments and namespaces.
     * For example:
     *    [
     *        'blog' => 'App\Blog\Controllers'
     *    ]
     *
     * @var array<string, string>
     */
    public array $routePath = [];

    /**
     * Should we automatically route requests based on the URI structure?
     *
     * Note: This requires the controller and method to be spelled
     * exactly as defined in the file/class structure.
     *
     * @var bool
     */
    public bool $autoRoute = false;

    /**
     * If TRUE, routing will treat URI segments containing only integers as
     * controller parameters, even if it matches a directory structure.
     *
     * @var bool
     */
    public bool $prioritize = false;

    /**
     * For Defined Routes.
     * If TRUE, matched multiple URI segments will be passed as one parameter.
     *
     * Default: false
     */
    public bool $multipleSegmentsOneParam = false;

    /**
     * Flag to limit or not the routes with {locale} placeholder to App::$supportedLocales
     *
     * @var bool
     */
    public bool $useSupportedLocalesOnly = false;
}
