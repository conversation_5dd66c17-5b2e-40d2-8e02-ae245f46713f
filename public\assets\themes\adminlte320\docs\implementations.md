---
layout: page
title: Implementations
---

Thanks to many of AdminLTE users, there are multiple implementations of the template for easy integration with back-end frameworks. The following are some of them: 


##### AdminLTE v3.0
{: .text-bold .text-dark .mt-3}
- [Setup AdminLTE3 in to <PERSON><PERSON> in few minutes](https://youtu.be/jA7hr2gE9yc) by [<PERSON><PERSON><PERSON>](https://github.com/shailesh-ladumor)
- [<PERSON><PERSON>-AdminLTE _v3.x_](https://github.com/jeroennoten/<PERSON>vel-AdminLTE) by [<PERSON><PERSON><PERSON>n](https://github.com/jeroennoten)
- [django-adminlte3](https://github.com/d-demirci/django-adminlte3) by [d-demirci](https://github.com/d-demirci)
- [AdminLTE3.MVC](https://www.nuget.org/packages/AdminLTE3.MVC/) by [somaraj](https://github.com/somaraj)
- [admin-lte-dotnet](https://github.com/iyilm4z/admin-lte-dotnet) by [iyilm4z](https://github.com/iyilm4z)
- [WebPx.AdminLTE.AspNetCore](https://github.com/WebPx/WebPx.AdminLTE.AspNetCore) by [WebPx](https://github.com/WebPx)
- [AdminLTE-3-Angular](https://github.com/erdkse/adminlte-3-angular) by [erdkse](https://github.com/erdkse)
- [AdminLTE-3-React](https://github.com/erdkse/adminlte-3-react) by [erdkse](https://github.com/erdkse)
- [AdminLTE-3-Vue](https://github.com/erdkse/adminlte-3-vue) by [erdkse](https://github.com/erdkse)
- [ASP.NET Core MVC / Angular Startup Project](https://github.com/aspnetboilerplate/module-zero-core-template) by [ASP.NET Boilerplate](https://github.com/aspnetboilerplate)
- [AdminLTE-3-CakePHP](https://github.com/arodu/cakelte) by [arodu](https://github.com/arodu)
- [AdminLTE-3 for Lua Server Pages](https://github.com/RealTimeLogic/LSP-Examples/tree/master/Dashboard) by [Real Time Logic](https://github.com/RealTimeLogic/)
- [yii2-adminlte3](https://github.com/hail812/yii2-adminlte3) by [hail812](https://github.com/hail812)

##### AdminLTE v2.4
{: .text-bold .text-dark .mt-3}
- [Setup AdminLTE2 in to Laravel in few minutes](https://youtu.be/8Fa7Ji4lDyI) by [Shailesh Ladumor](https://github.com/shailesh-ladumor)
- [Laravel-AdminLTE _v1.x & v2.x_](https://github.com/jeroennoten/Laravel-AdminLTE) by [Jeroen Noten](https://github.com/jeroennoten)
- [adminlte-laravel](https://github.com/acacha/adminlte-laravel) by [Sergi Tur Badenas](https://github.com/acacha)
- [yii2-adminlte-asset](https://github.com/dmstr/yii2-adminlte-asset) by [Tobias Munk](https://github.com/schmunk42)

##### AdminLTE v2.3
{: .text-bold .text-dark .mt-3}
- [AdminThemeBundle (Symfony)](https://github.com/avanzu/AdminThemeBundle) by [Marc Bach](https://github.com/avanzu)
- [lte-rails](https://github.com/racketlogger/lte-rails) by [Carlos at RacketLogger](https://github.com/racketlogger)

##### AdminLTE v2.2
{: .text-bold .text-dark .mt-3}
- [AdminLTE-RTL](https://github.com/mmdsharifi/AdminLTE-RTL) by [Mohammad Sharifi](https://github.com/mmdsharifi)

##### AdminLTE v2.1
{: .text-bold .text-dark .mt-3}
- [adminlte2-rails](https://github.com/nicolas-besnard/adminlte2-rails) by [Nicolas Besnard](https://github.com/nicolas-besnard)



> ##### Note!
> These implementations are not supported by ColorlibHQ. However, they do provide a good example of how to integrate AdminLTE into different frameworks. For the latest release of AdminLTE, please visit our [repository](https://github.com/ColorlibHQ/AdminLTE/) or [website](https://adminlte.io).
{: .quote-danger}
