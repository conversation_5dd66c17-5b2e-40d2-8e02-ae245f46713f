<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="card-title">Crops</h3>
            <a href="<?= site_url('crops/getNew') ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add New Crop
            </a>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('success')) : ?>
                <div class="alert alert-success"><?= session()->getFlashdata('success') ?></div>
            <?php endif; ?>
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="cropsTable">
                    <thead>
                        <tr>
                            <th>Crop Name</th>
                            <th>Scientific Name</th>
                            <th>Growing Season</th>
                            <th>Growing Period</th>
                            <th>Temperature</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($crops as $crop) : ?>
                            <tr>
                                <td><?= esc($crop['crop_name']) ?></td>
                                <td><?= esc($crop['scientific_name']) ?></td>
                                <td><?= esc($crop['growing_season']) ?></td>
                                <td><?= esc($crop['average_growing_period']) ?> days</td>
                                <td><?= esc($crop['ideal_temperature']) ?></td>
                                <td>
                                    <span class="badge <?= $crop['status'] === 'active' ? 'bg-success' : 'bg-danger' ?>">
                                        <?= ucfirst($crop['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="<?= site_url('crops/edit/' . $crop['crop_id']) ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger delete-crop" data-id="<?= $crop['crop_id'] ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this crop?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    let cropId;
    
    $('#cropsTable').DataTable();

    $('.delete-crop').click(function() {
        cropId = $(this).data('id');
        $('#deleteModal').modal('show');
    });

    $('#confirmDelete').click(function() {
        $.ajax({
            url: '<?= site_url('crops/delete/') ?>' + cropId,
            type: 'DELETE',
            success: function(response) {
                $('#deleteModal').modal('hide');
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('An error occurred while deleting the crop.');
            }
        });
    });
});
</script>
<?= $this->endSection() ?> 