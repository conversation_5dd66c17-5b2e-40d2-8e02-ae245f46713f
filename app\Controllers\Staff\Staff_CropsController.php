<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmCropsDataModel;
use App\Models\CropsFarmFertilizerDataModel;
use App\Models\CropsFarmPesticidesDataModel;
use App\Models\CropsFarmHarvestDataModel;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\CropsFarmDiseaseDataModel;
use App\Models\CropBuyersModel;
use App\Models\PesticidesModel;
use App\Models\FertilizersModel;
use App\Models\InfectionsModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\CropsModel;
use App\Models\usersModel;
use App\Models\districtModel;
use App\Models\provinceModel;
use App\Models\llgModel;
use App\Models\wardModel;

class Staff_CropsController extends BaseController
{
    private $models = [];
    protected $helpers = ['url', 'form', 'info', 'weather'];

    public function __construct()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        foreach ($this->helpers as $helper) {
            helper($helper);
        }
    }

    protected function getModel($modelName)
    {
        if (!isset($this->models[$modelName])) {
            switch ($modelName) {
                case 'farmCropsData':
                    $this->models[$modelName] = new CropsFarmCropsDataModel();
                    break;
                case 'farmFertilizerData':
                    $this->models[$modelName] = new CropsFarmFertilizerDataModel();
                    break;
                case 'fertilizers':
                    $this->models[$modelName] = new FertilizersModel();
                    break;
                case 'farmPesticidesData':
                    $this->models[$modelName] = new CropsFarmPesticidesDataModel();
                    break;
                case 'pesticides':
                    $this->models[$modelName] = new PesticidesModel();
                    break;
                case 'farmHarvestData':
                    $this->models[$modelName] = new CropsFarmHarvestDataModel();
                    break;
                case 'farmMarketingData':
                    $this->models[$modelName] = new CropsFarmMarketingDataModel();
                    break;
                case 'cropBuyers':
                    $this->models[$modelName] = new CropBuyersModel();
                    break;
                case 'farmDiseaseData':
                    $this->models[$modelName] = new CropsFarmDiseaseDataModel();
                    break;
                case 'infections':
                    $this->models[$modelName] = new InfectionsModel();
                    break;
                case 'farmers':
                    $this->models[$modelName] = new FarmerInformationModel();
                    break;
                case 'farmBlock':
                    $this->models[$modelName] = new CropsFarmBlockModel();
                    break;
                case 'crops':
                    $this->models[$modelName] = new CropsModel();
                    break;
                case 'users':
                    $this->models[$modelName] = new usersModel();
                    break;
                case 'districts':
                    $this->models[$modelName] = new districtModel();
                    break;
                case 'provinces':
                    $this->models[$modelName] = new provinceModel();
                    break;
                case 'llgs':
                    $this->models[$modelName] = new llgModel();
                    break;
                case 'wards':
                    $this->models[$modelName] = new wardModel();
                    break;
            }
        }
        return $this->models[$modelName];
    }

    // Helper methods to get specific models
    protected function getFarmCropsDataModel() { return $this->getModel('farmCropsData'); }
    protected function getFarmFertilizerDataModel() { return $this->getModel('farmFertilizerData'); }
    protected function getFertilizersModel() { return $this->getModel('fertilizers'); }
    protected function getFarmPesticidesDataModel() { return $this->getModel('farmPesticidesData'); }
    protected function getPesticidesModel() { return $this->getModel('pesticides'); }
    protected function getFarmHarvestDataModel() { return $this->getModel('farmHarvestData'); }
    protected function getFarmMarketingDataModel() { return $this->getModel('farmMarketingData'); }
    protected function getCropBuyersModel() { return $this->getModel('cropBuyers'); }
    protected function getFarmDiseaseDataModel() { return $this->getModel('farmDiseaseData'); }
    protected function getInfectionsModel() { return $this->getModel('infections'); }
    protected function getFarmersModel() { return $this->getModel('farmers'); }
    protected function getFarmBlockModel() { return $this->getModel('farmBlock'); }
    protected function getCropsModel() { return $this->getModel('crops'); }
    protected function getUsersModel() { return $this->getModel('users'); }
    protected function getDistrictModel() { return $this->getModel('districts'); }
    protected function getProvinceModel() { return $this->getModel('provinces'); }
    protected function getLlgModel() { return $this->getModel('llgs'); }
    protected function getWardModel() { return $this->getModel('wards'); }

    protected function verifyDistrictAccess($districtId) 
    {
        return $districtId == session()->get('district_id');
    }

    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    public function view_crop_blocks()
    {
        $district = $this->getModel('districts')->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Farm Blocks List',
            'page_header' => 'Farm Blocks List',
            'farmers' => $this->getFarmersModel()->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $this->getFarmBlockModel()->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'crops' => $this->getCropsModel()->findAll(),
        ];

        return view('staff/farms/view_crop_blocks', $data);
    }

    public function view_crops_data($block_id)
    {
        $block = $this->getFarmBlockModel()->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$block || ($block['crop_id'] == 0)) {
            return redirect()->back()->with('error', 'Block not found or Crop not selected');
        }

        $data = [
            'title' => 'Farm Block Data',
            'block' => $block,
            'crops_data' => $this->getFarmCropsDataModel()->where('block_id', $block_id)->findAll(),
            'total_plants_added' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('number_of_plants', 'total_plants_added')
                ->first(),
            'total_plants_removed' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('number_of_plants', 'total_plants_removed')
                ->first(),
            'total_hectares_added' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('hectares', 'total_hectares_added')
                ->first(),
            'total_hectares_removed' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('hectares', 'total_hectares_removed')
                ->first(),
            'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
            'province' => $this->getModel('provinces')->find($block['province_id']),
            'district' => $this->getModel('districts')->find($block['district_id']),
            'llg' => $this->getModel('llgs')->find($block['llg_id']),
            'ward' => $this->getModel('wards')->find($block['ward_id']),
            'crop' => $this->getCropsModel()->find($block['crop_id']),
            'users' => $this->getUsersModel()->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_crops_data', $data);
    }

    public function add_crops_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'block_id' => $block_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'action_type' => $this->request->getPost('action_type'),
                'action_reason' => $this->request->getPost('action_reason'),
                'action_date' => $this->request->getPost('action_date'),
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'hectares' => $this->request->getPost('hectares'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['action_type', 'action_date', 'number_of_plants', 'hectares'];
            $this->validateInput($data, $required);

            $this->getFarmCropsDataModel()->save($data);
            
            //get weather data
            $weather_data = get_weather_data($block['id'], $block['lon'], $block['lat'], $data['action_date']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Crops data added successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_crops_data()
    {
        try {
            $id = $this->request->getPost('id');
            $crops_data = $this->getFarmCropsDataModel()->find($id);
            
            if (!$crops_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $crops_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'action_type' => $this->request->getPost('action_type'),
                'action_reason' => $this->request->getPost('action_reason'),
                'action_date' => $this->request->getPost('action_date'),
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'hectares' => $this->request->getPost('hectares'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['action_type', 'action_date', 'number_of_plants', 'hectares'];
            $this->validateInput($data, $required);

            $this->getFarmCropsDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Crops data updated successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_block_data($id)
    {
        try {
            $crops_data = $this->getFarmCropsDataModel()->find($id);
            
            if (!$crops_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $crops_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmCropsDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Block Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to delete record'
            ]);
        }
    }

    public function delete_crops_data($id)
    {
        try {
            $crops_data = $this->getFarmCropsDataModel()->find($id);
            
            if (!$crops_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $crops_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmCropsDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Fertilizer Management
    public function fertilizer_data()
    {
        $district = $this->getModel('districts')->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Fertilizer Data',
            'page_header' => 'Fertilizer Data',
            'farm_blocks' => $this->getFarmBlockModel()->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'fertilizers' => $this->getFertilizersModel()->findAll()
        ];

        return view('staff/farms/fertilizer_data', $data);
    }

    public function view_fertilizer_data($block_id)
    {
        $block = $this->getFarmBlockModel()->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$block) {
            return redirect()->back()->with('error', 'Block not found or access denied');
        }

        $data = [
            'title' => 'Block Fertilizer Data',
            'block' => $block,
            'fertilizer_data' => $this->getFarmFertilizerDataModel()->where('block_id', $block_id)->findAll(),
            'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
            'province' => $this->getModel('provinces')->find($block['province_id']),
            'district' => $this->getModel('districts')->find($block['district_id']),
            'llg' => $this->getModel('llgs')->find($block['llg_id']),
            'ward' => $this->getModel('wards')->find($block['ward_id']),
            'crop' => $this->getCropsModel()->find($block['crop_id']),
            'fertilizers' => $this->getFertilizersModel()->findAll(),
            'users' => $this->getUsersModel()->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_fertilizer_data', $data);
    }

    public function add_fertilizer_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'block_id' => $block_id,
                'fertilizer_id' => $this->request->getPost('fertilizer_id'),
                'crop_id' => $this->request->getPost('crop_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['fertilizer_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->getFarmFertilizerDataModel()->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Fertilizer data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Fertilizer Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_fertilizer_data()
    {
        try {
            $id = $this->request->getPost('id');
            $fertilizer_data = $this->getFarmFertilizerDataModel()->find($id);
            
            if (!$fertilizer_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $fertilizer_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'fertilizer_id' => $this->request->getPost('fertilizer_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['fertilizer_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->getFarmFertilizerDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Fertilizer data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Fertilizer Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_fertilizer_data($id)
    {
        try {
            $fertilizer_data = $this->getFarmFertilizerDataModel()->find($id);
            
            if (!$fertilizer_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $fertilizer_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmFertilizerDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Fertilizer data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Fertilizer Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Pesticides Management
    public function pesticides_data()
    {
        $district = $this->getModel('districts')->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Pesticides Data',
            'page_header' => 'Pesticides Data',
            'farm_blocks' => $this->getFarmBlockModel()->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'pesticides' => $this->getPesticidesModel()->findAll()
        ];

        return view('staff/farms/pesticides_data', $data);
    }

    public function view_pesticides_data($block_id)
    {
        $block = $this->getFarmBlockModel()->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();
            
        if (!$block) {
            return redirect()->back()->with('error', 'Block not found or access denied');
        }

        $data = [
            'title' => 'Block Pesticides Data',
            'block' => $block,
            'pesticides_data' => $this->getFarmPesticidesDataModel()->where('block_id', $block_id)->findAll(),
            'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
            'province' => $this->getModel('provinces')->find($block['province_id']),
            'district' => $this->getModel('districts')->find($block['district_id']),
            'llg' => $this->getModel('llgs')->find($block['llg_id']),
            'ward' => $this->getModel('wards')->find($block['ward_id']),
            'crop' => $this->getCropsModel()->find($block['crop_id']),
            'pesticides' => $this->getPesticidesModel()->findAll(),
            'users' => $this->getUsersModel()->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_pesticides_data', $data);
    }

    public function add_pesticides_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'block_id' => $block_id,
                'pesticide_id' => $this->request->getPost('pesticide_id'),
                'crop_id' => $this->request->getPost('crop_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['pesticide_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->getFarmPesticidesDataModel()->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pesticides data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_pesticides_data()
    {
        try {
            $id = $this->request->getPost('id');
            $pesticides_data = $this->getFarmPesticidesDataModel()->find($id);
            
            if (!$pesticides_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $pesticides_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'pesticide_id' => $this->request->getPost('pesticide_id'),
                'name' => $this->request->getPost('name'),
                'brand' => $this->request->getPost('brand'),
                'unit' => $this->request->getPost('unit'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'quantity' => $this->request->getPost('quantity'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['pesticide_id', 'name', 'brand', 'unit', 'unit_of_measure', 'quantity', 'action_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields
            if (!is_numeric($data['unit']) || $data['unit'] <= 0) {
                throw new \Exception("Unit value must be a positive number.");
            }
            if (!is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                throw new \Exception("Quantity must be a positive number.");
            }

            $this->getFarmPesticidesDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pesticides data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_pesticides_data($id)
    {
        try {
            $pesticides_data = $this->getFarmPesticidesDataModel()->find($id);
            
            if (!$pesticides_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $pesticides_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmPesticidesDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Pesticides data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Pesticides Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Harvest Management
    public function harvest_data()
    {
        $district = $this->getModel('districts')->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Harvest Data',
            'page_header' => 'Harvest Data',
            'farm_blocks' => $this->getFarmBlockModel()->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where([
                'crops_farm_blocks.district_id' => session()->get('district_id'),
                'crops_farm_blocks.status' => 'active'
            ])
            ->findAll(),
            'district_name' => $districtName
        ];

        return view('staff/farms/harvest_data', $data);
    }

    public function view_harvest_data($block_id)
    {
        try {
            // Fetch block with district access verification
            $block = $this->getFarmBlockModel()->where([
                'id' => $block_id,
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$block) {
                return redirect()->back()->with('error', 'Block not found or access denied');
            }

            // Fetch crop information
            $crop = $this->getCropsModel()->find($block['crop_id']);
            if (!$crop) {
                return redirect()->back()->with('error', 'Crop information not found');
            }

            // Prepare view data with optimized queries
            $data = [
                'title' => 'Block Harvest Data',
                'block' => $block,
                'harvest_data' => $this->getFarmHarvestDataModel()
                    ->where([
                        'block_id' => $block_id,
                        'status' => 'active'
                    ])
                    ->findAll(),
                'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
                'province' => $this->getModel('provinces')->find($block['province_id']),
                'district' => $this->getModel('districts')->find($block['district_id']),
                'llg' => $this->getModel('llgs')->find($block['llg_id']),
                'ward' => $this->getModel('wards')->find($block['ward_id']),
                'crop' => $crop,
                'users' => $this->getUsersModel()
                    ->where('org_id', session()->get('org_id'))
                    ->findAll()
            ];

            return view('staff/farms/view_harvest_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[View Harvest Data] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while loading harvest data');
        }
    }

    public function add_harvest_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where([
                'id' => $block_id,
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            // Prepare harvest data
            $data = [
                'block_id' => $block_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'item' => $this->request->getPost('item'),
                'description' => $this->request->getPost('description'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'unit' => $this->request->getPost('unit'),
                'quantity' => $this->request->getPost('quantity'),
                'harvest_date' => $this->request->getPost('harvest_date'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['item', 'unit_of_measure', 'unit', 'quantity', 'harvest_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields with proper error messages
            $this->validateNumericFields($data);

            // Save the data
            $this->getFarmHarvestDataModel()->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Harvest data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_harvest_data()
    {
        try {
            $id = $this->request->getPost('id');
            $harvest_data = $this->getFarmHarvestDataModel()->find($id);
            
            if (!$harvest_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where([
                'id' => $harvest_data['block_id'],
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            // Prepare update data
            $data = [
                'item' => $this->request->getPost('item'),
                'description' => $this->request->getPost('description'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'unit' => $this->request->getPost('unit'),
                'quantity' => $this->request->getPost('quantity'),
                'harvest_date' => $this->request->getPost('harvest_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['item', 'unit_of_measure', 'unit', 'quantity', 'harvest_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields with proper error messages
            $this->validateNumericFields($data);

            // Update the record
            $this->getFarmHarvestDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Harvest data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_harvest_data($id)
    {
        try {
            $harvest_data = $this->getFarmHarvestDataModel()->find($id);
            
            if (!$harvest_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district with optimized query
            $block = $this->getFarmBlockModel()->where([
                'id' => $harvest_data['block_id'],
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            // Soft delete the record
            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmHarvestDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Harvest data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Validate numeric fields for harvest data
     * @param array $data The data array containing fields to validate
     * @throws \Exception If validation fails
     */
    private function validateNumericFields(array $data): void
    {
        $numericFields = [
            'unit' => 'Unit value',
            'quantity' => 'Quantity'
        ];

        foreach ($numericFields as $field => $label) {
            if (!isset($data[$field])) continue;
            
            if (!is_numeric($data[$field])) {
                throw new \Exception("{$label} must be a number.");
            }
            
            if ($data[$field] <= 0) {
                throw new \Exception("{$label} must be a positive number.");
            }
        }
    }

    // Marketing Management
    /**
     * Display marketing data overview
     * Optimized with proper joins and selective field loading
     * @return string
     */
    public function marketing_data()
    {
        try {
            // Log the district ID for debugging
            log_message('debug', '[Marketing Data] District ID: ' . session()->get('district_id'));
            
            $district = $this->getModel('districts')->find(session()->get('district_id'));
            if (!$district) {
                log_message('error', '[Marketing Data] District not found for ID: ' . session()->get('district_id'));
                throw new \Exception('District not found');
            }
            
            $districtName = $district['name'] ?? 'No District Assigned';
            
            // Log the query parameters for debugging
            log_message('debug', '[Marketing Data] Query params: ' . json_encode([
                'crops_farm_blocks.status' => 'active',
                'district_id' => session()->get('district_id')
            ]));
            
            $farmers = $this->getFarmersModel()
                ->select('farmer_information.id, farmer_information.farmer_code, farmer_information.given_name, 
                         farmer_information.surname, farmer_information.status, farmer_information.gender, 
                         farmer_information.village, farmer_information.phone, farmer_information.email')
                ->where('farmer_information.status', 'active')
                ->where('farmer_information.district_id', session()->get('district_id'))
                ->findAll();
                
            log_message('debug', '[Marketing Data] Found ' . count($farmers) . ' farmers');

            $data = [
                'title' => 'Marketing Data',
                'page_header' => 'Marketing Data',
                'farmers' => $farmers,
                'district_name' => $districtName
            ];

            return view('staff/farms/marketing_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[Marketing Data] Error details: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', 'An error occurred while loading marketing data: ' . $e->getMessage());
        }
    }

    /**
     * View detailed marketing data for a specific farmer
     * @param int $farmer_id
     * @return string
     */
    public function view_market_data($farmer_id)
    {
        try {
            // Log the incoming request
            log_message('debug', '[View Market Data] Starting with farmer_id: ' . $farmer_id);
            
            // Verify farmer belongs to user's district with optimized query
            $farmer = $this->getFarmersModel()
                ->select('farmer_information.*, adx_district.name as district_name')
                ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
                ->where([
                    'farmer_information.id' => $farmer_id,
                    'farmer_information.district_id' => session()->get('district_id')
                ])
                ->first();
        
            log_message('debug', '[View Market Data] Farmer query result: ' . json_encode($farmer));
                
            if (!$farmer) {
                log_message('error', '[View Market Data] Farmer not found or access denied. Farmer ID: ' . $farmer_id . ', District ID: ' . session()->get('district_id'));
                throw new \Exception('Farmer not found or access denied');
            }

            // Get provinces data
            try {
                $provinces = $this->getModel('provinces')->findAll();
                log_message('debug', '[View Market Data] Provinces query successful. Found ' . count($provinces) . ' provinces');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching provinces: ' . $e->getMessage());
                throw new \Exception('Error retrieving provinces data');
            }

            // Get farm blocks data
            try {
                $farm_blocks = $this->getFarmBlockModel()
                    ->select('
                        crops_farm_blocks.*, 
                        farmer_information.given_name,
                        farmer_information.surname,
                        adx_crops.crop_name,
                        adx_ward.name as ward_name,
                        adx_llg.name as llg_name,
                        adx_district.name as district_name
                    ')
                    ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
                    ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
                    ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
                    ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
                    ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
                    ->where('crops_farm_blocks.farmer_id', $farmer_id)
                    ->where('crops_farm_blocks.status', 'active')
                    ->findAll();

                log_message('debug', '[View Market Data] Farm blocks query successful. Found ' . count($farm_blocks) . ' blocks');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching farm blocks: ' . $e->getMessage());
                throw new \Exception('Error retrieving farm blocks data: ' . $e->getMessage());
            }

            // Get marketing data with detailed error logging
            try {
                $marketing_data = $this->getFarmMarketingDataModel()
                    ->select('
                        crops_farm_marketing_data.*,
                        adx_crops.crop_name,
                        crop_buyers.name as buyer_name,
                        users.name as created_by_name
                    ')
                    ->join('adx_crops', 'adx_crops.id = crops_farm_marketing_data.crop_id', 'left')
                    ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id', 'left')
                    ->join('users', 'users.id = crops_farm_marketing_data.created_by', 'left')
                    ->where('crops_farm_marketing_data.farmer_id', $farmer_id)
                    ->where('crops_farm_marketing_data.status !=', 'deleted')
                    ->orderBy('crops_farm_marketing_data.market_date', 'DESC')
                    ->findAll();

                log_message('debug', '[View Market Data] Marketing data query successful. Found ' . count($marketing_data) . ' records');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching marketing data: ' . $e->getMessage());
                throw new \Exception('Error retrieving marketing data: ' . $e->getMessage());
            }

            // Get crops list with error handling
            try {
                $crops = $this->getCropsModel()
                    ->orderBy('crop_name', 'ASC')
                    ->findAll();
                log_message('debug', '[View Market Data] Crops query successful. Found ' . count($crops) . ' crops');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching crops: ' . $e->getMessage());
                throw new \Exception('Error retrieving crops data');
            }

            // Get buyers list with error handling
            try {
                $buyers = $this->getCropBuyersModel()
                    ->where('status', 'active')
                    ->findAll();
                log_message('debug', '[View Market Data] Buyers query successful. Found ' . count($buyers) . ' buyers');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching buyers: ' . $e->getMessage());
                throw new \Exception('Error retrieving buyers data');
            }

            // Get users list with error handling
            try {
                $users = $this->getUsersModel()
                    ->where('org_id', session()->get('org_id'))
                    ->findAll();
                log_message('debug', '[View Market Data] Users query successful. Found ' . count($users) . ' users');
            } catch (\Exception $e) {
                log_message('error', '[View Market Data] Database error fetching users: ' . $e->getMessage());
                throw new \Exception('Error retrieving users data');
            }

            $data = [
                'title' => 'Farmer Marketing Data',
                'page_header' => 'Farmer Marketing Data',
                'farmer' => $farmer,
                'provinces' => $provinces,
                'farm_blocks' => $farm_blocks,
                'marketing_data' => $marketing_data,
                'crops' => $crops,
                'buyers' => $buyers,
                'users' => $users
            ];

            log_message('debug', '[View Market Data] Successfully prepared view data');
            return view('staff/farms/view_market_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[View Market Data] ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
            return redirect()->back()->with('error', 'An error occurred while loading market data: ' . $e->getMessage());
        }
    }

    /**
     * Add new marketing data
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function add_market_data()
    {
        try {
            $farmer_id = $this->request->getPost('farmer_id');
            
            // Verify farmer belongs to user's district
            $farmer = $this->getFarmersModel()->where([
                'id' => $farmer_id,
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$farmer) {
                throw new \Exception('Farmer not found or access denied');
            }

            // Prepare market data with proper validation
            $data = $this->prepareMarketData($farmer_id);

            // Additional validation for market-specific fields
            $this->validateMarketData($data);

            // Save the data
            $this->getFarmMarketingDataModel()->save($data);

            // Calculate and cache total sales for reporting
            $this->updateFarmerMarketingStats($farmer_id);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Marketing data added successfully',
                'redirect' => base_url("staff/farms/view-market-data/{$farmer_id}")
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Market Data] ' . $e->getMessage());
            return $this->response->setStatusCode(400)->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update existing marketing data
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function update_market_data()
    {
        try {
            $id = $this->request->getPost('id');
            $marketing_data = $this->getFarmMarketingDataModel()->find($id);
            
            if (!$marketing_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify farmer belongs to user's district
            $farmer = $this->getFarmersModel()->where([
                'id' => $marketing_data['farmer_id'],
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$farmer) {
                throw new \Exception('Access denied');
            }

            // Prepare update data with proper validation
            $data = $this->prepareMarketData($marketing_data['farmer_id'], true);

            // Additional validation for market-specific fields
            $this->validateMarketData($data);

            // Update the record
            $this->getFarmMarketingDataModel()->update($id, $data);

            // Recalculate and update cached stats
            $this->updateFarmerMarketingStats($marketing_data['farmer_id']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Marketing data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Market Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Delete (soft delete) marketing data
     * @param int $id
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function delete_market_data($id)
    {
        try {
            $marketing_data = $this->getFarmMarketingDataModel()->find($id);
            
            if (!$marketing_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify farmer belongs to user's district
            $farmer = $this->getFarmersModel()->where([
                'id' => $marketing_data['farmer_id'],
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$farmer) {
                throw new \Exception('Access denied');
            }

            // Soft delete the record
            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmMarketingDataModel()->update($id, $data);

            // Update cached stats after deletion
            $this->updateFarmerMarketingStats($marketing_data['farmer_id']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Marketing data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Market Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Prepare and validate market data for insert/update
     * @param int $farmer_id
     * @param bool $isUpdate
     * @return array
     */
    private function prepareMarketData(int $farmer_id, bool $isUpdate = false): array
    {
        $data = [
            'farmer_id' => $farmer_id,
            'block_id' => $this->request->getPost('block_id') ?: null,
            'crop_id' => $this->request->getPost('crop_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'market_date' => $this->request->getPost('market_date'),
            'market_stage' => $this->request->getPost('market_stage'),
            'buyer_id' => $this->request->getPost('buyer_id'),
            'selling_location' => $this->request->getPost('selling_location'),
            'product' => $this->request->getPost('product'),
            'product_type' => $this->request->getPost('product_type'),
            'description' => $this->request->getPost('description'),
            'unit_of_measure' => $this->request->getPost('unit_of_measure'),
            'unit' => $this->request->getPost('unit'),
            'quantity' => $this->request->getPost('quantity'),
            'market_price_per_unit' => $this->request->getPost('market_price_per_unit'),
            'total_freight_cost' => $this->request->getPost('total_freight_cost') ?: null,
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active'
        ];

        if ($isUpdate) {
            $data['updated_by'] = session()->get('id');
            $data['updated_at'] = date('Y-m-d H:i:s');
        } else {
            $data['created_by'] = session()->get('id');
            $data['created_at'] = date('Y-m-d H:i:s');
        }

        return $data;
    }

    /**
     * Validate market data fields
     * @param array $data
     * @throws \Exception
     */
    private function validateMarketData(array $data): void
    {
        // Required fields validation
        $required = [
            'crop_id',
            'market_stage',
            'market_date',
            'buyer_id',
            'product',
            'product_type',
            'unit',
            'unit_of_measure',
            'quantity',
            'market_price_per_unit',
            'province_id',
            'district_id',
            'llg_id'
        ];
        $this->validateInput($data, $required);

        // Numeric fields validation
        $numericFields = [
            'unit' => 'Unit value',
            'quantity' => 'Quantity',
            'market_price_per_unit' => 'Market price per unit'
        ];

        foreach ($numericFields as $field => $label) {
            if (!isset($data[$field])) continue;
            
            if (!is_numeric($data[$field])) {
                throw new \Exception("{$label} must be a number.");
            }
            
            if ($data[$field] <= 0) {
                throw new \Exception("{$label} must be a positive number.");
            }
        }

        // Validate total_freight_cost if provided
        if (!empty($data['total_freight_cost'])) {
            if (!is_numeric($data['total_freight_cost'])) {
                throw new \Exception("Total freight cost must be a number.");
            }
            if ($data['total_freight_cost'] < 0) {
                throw new \Exception("Total freight cost cannot be negative.");
            }
        }

        // Validate market date
        if (strtotime($data['market_date']) > time()) {
            throw new \Exception("Market date cannot be in the future.");
        }
    }

    /**
     * Update cached marketing statistics for a farmer
     * @param int $farmer_id
     */
    private function updateFarmerMarketingStats(int $farmer_id): void
    {
        try {
            $stats = $this->getFarmMarketingDataModel()
                ->select('
                    COUNT(*) as total_transactions,
                    SUM(quantity * market_price_per_unit) as total_sales,
                    SUM(total_freight_cost) as total_freight_costs,
                    AVG(market_price_per_unit) as avg_price_per_unit
                ')
                ->where([
                    'farmer_id' => $farmer_id,
                    'status' => 'active'
                ])
                ->first();

            // Cache the results for faster retrieval
            cache()->save("farmer_marketing_stats_{$farmer_id}", $stats, 3600); // Cache for 1 hour
        } catch (\Exception $e) {
            log_message('error', '[Update Marketing Stats] Failed to update stats for farmer ' . $farmer_id . ': ' . $e->getMessage());
        }
    }

    // Disease Management
    /**
     * Display disease data overview with optimized queries and proper joins
     * @return string
     */
    public function disease_data()
    {
        try {
            // Check if user is logged in and has district access
            if (!session()->get('district_id')) {
                log_message('error', '[Disease Data] No district assigned to user');
                return redirect()->back()->with('error', 'No district assigned to your account');
            }

            $district = $this->getModel('districts')->find(session()->get('district_id'));
            if (!$district) {
                log_message('error', '[Disease Data] District not found: ' . session()->get('district_id'));
                return redirect()->back()->with('error', 'District not found');
            }

            $districtName = $district['name'] ?? 'No District Assigned';

            // Get farm blocks with proper error handling
            $farmBlocks = $this->getFarmBlockModel()->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where([
                'crops_farm_blocks.district_id' => session()->get('district_id'),
                'crops_farm_blocks.status' => 'active'
            ])
            ->findAll();

            if ($farmBlocks === null) {
                log_message('error', '[Disease Data] Error fetching farm blocks');
                throw new \Exception('Error fetching farm blocks data');
            }

            // Get infections list with error handling
            $infections = $this->getInfectionsModel()->findAll();
            if ($infections === null) {
                log_message('error', '[Disease Data] Error fetching infections list');
                throw new \Exception('Error fetching infections data');
            }

            $data = [
                'title' => 'Disease Data',
                'page_header' => 'Disease Data',
                'farm_blocks' => $farmBlocks,
                'district_name' => $districtName,
                'infections' => $infections
            ];

            // Log the view path for debugging
            log_message('debug', '[Disease Data] Attempting to load view: staff/farms/diseases_data');
            
            // Check if view file exists
            $viewPath = APPPATH . 'Views/staff/farms/diseases_data.php';
            if (!file_exists($viewPath)) {
                log_message('error', '[Disease Data] View file not found: ' . $viewPath);
                throw new \Exception('View file not found');
            }

            return view('staff/farms/diseases_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[Disease Data] ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', 'An error occurred while loading disease data: ' . $e->getMessage());
        }
    }

    /**
     * View detailed disease data for a specific block with optimized queries
     * @param int $block_id
     * @return string
     */
    public function view_diseases_data($block_id)
    {
        try {
            // Get block data with proper error handling
            $block = $this->getFarmBlockModel()
                ->where([
                    'id' => $block_id,
                    'district_id' => session()->get('district_id'),
                    'status' => 'active'
                ])
                ->first();

            if (!$block) {
                log_message('error', '[View Disease Data] Block not found or access denied: ' . $block_id);
                return redirect()->back()->with('error', 'Block not found or access denied');
            }

            // Get diseases data for this block
            $diseases_data = $this->getFarmDiseaseDataModel()
                ->where('block_id', $block_id)
                ->where('status', 'active')
                ->findAll();

            // Prepare view data
            $data = [
                'title' => 'Block Diseases Data',
                'page_header' => 'Block Diseases Data',
                'block' => $block,
                'diseases_data' => $diseases_data,
                'farmer' => $this->getModel('farmers')->find($block['farmer_id']),
                'province' => $this->getModel('provinces')->find($block['province_id']),
                'district' => $this->getModel('districts')->find($block['district_id']),
                'llg' => $this->getModel('llgs')->find($block['llg_id']),
                'ward' => $this->getModel('wards')->find($block['ward_id']),
                'crop' => $this->getModel('crops')->find($block['crop_id']),
                'infections' => $this->getInfectionsModel()->findAll(),
                'users' => $this->getUsersModel()
                    ->where('org_id', session()->get('org_id'))
                    ->findAll()
            ];

            return view('staff/farms/view_diseases_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[View Disease Data] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while loading disease data');
        }
    }

    /**
     * Add new disease data with comprehensive validation
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function add_disease_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where([
                'id' => $block_id,
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            // Prepare disease data
            $data = $this->prepareDiseaseData($block_id);

            // Validate disease data
            $this->validateDiseaseData($data);

            // Save the data
            $this->getFarmDiseaseDataModel()->save($data);

            // Update block disease status
            $this->updateBlockDiseaseStatus($block_id);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Disease data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update existing disease data with validation
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function update_disease_data()
    {
        try {
            $id = $this->request->getPost('id');
            $disease_data = $this->getFarmDiseaseDataModel()->find($id);
            
            if (!$disease_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where([
                'id' => $disease_data['block_id'],
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            // Prepare update data
            $data = $this->prepareDiseaseData($disease_data['block_id'], true);

            // Validate disease data
            $this->validateDiseaseData($data);

            // Update the record
            $this->getFarmDiseaseDataModel()->update($id, $data);

            // Update block disease status
            $this->updateBlockDiseaseStatus($disease_data['block_id']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Disease data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Delete (soft delete) disease data
     * @param int $id
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function delete_disease_data($id)
    {
        try {
            $disease_data = $this->getFarmDiseaseDataModel()->find($id);
            
            if (!$disease_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where([
                'id' => $disease_data['block_id'],
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            // Soft delete the record
            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmDiseaseDataModel()->update($id, $data);

            // Update block disease status
            $this->updateBlockDiseaseStatus($disease_data['block_id']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Disease data deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Prepare and validate disease data for insert/update
     * @param int $block_id
     * @param bool $isUpdate
     * @return array
     */
    private function prepareDiseaseData(int $block_id, bool $isUpdate = false): array
    {
        $data = [
            'block_id' => $block_id,
            'crop_id' => $this->request->getPost('crop_id'),
            'disease_type' => $this->request->getPost('disease_type'),
            'disease_name' => $this->request->getPost('disease_name'),
            'description' => $this->request->getPost('description'),
            'number_of_plants' => $this->request->getPost('number_of_plants'),
            'breed' => $this->request->getPost('breed'),
            'hectares' => $this->request->getPost('hectares'),
            'action_date' => $this->request->getPost('action_date'),
            'remarks' => $this->request->getPost('remarks')
        ];

        if ($isUpdate) {
            $data['updated_by'] = session()->get('emp_id');
            $data['updated_at'] = date('Y-m-d H:i:s');
        } else {
            $data['created_by'] = session()->get('emp_id');
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['status'] = 'active';
        }

        return $data;
    }

    /**
     * Validate disease data fields
     * @param array $data
     * @throws \Exception
     */
    private function validateDiseaseData(array $data): void
    {
        // Required fields validation
        $required = [
            'disease_type',
            'disease_name',
            'number_of_plants',
            'breed',
            'hectares',
            'action_date'
        ];
        $this->validateInput($data, $required);

        // Numeric fields validation
        $numericFields = [
            'number_of_plants' => 'Number of plants',
            'hectares' => 'Hectares'
        ];

        foreach ($numericFields as $field => $label) {
            if (!isset($data[$field])) continue;
            
            if (!is_numeric($data[$field])) {
                throw new \Exception("{$label} must be a number.");
            }
            
            if ($data[$field] <= 0) {
                throw new \Exception("{$label} must be a positive number.");
            }
        }

        // Validate date
        if (strtotime($data['action_date']) > time()) {
            throw new \Exception("Action date cannot be in the future.");
        }
    }

    /**
     * Update block's disease status based on active disease records
     * @param int $block_id
     */
    private function updateBlockDiseaseStatus(int $block_id): void
    {
        try {
            // Get active disease records for the block
            $activeDiseases = $this->getFarmDiseaseDataModel()
                ->where([
                    'block_id' => $block_id,
                    'status' => 'active'
                ])
                ->findAll();

            // Calculate overall disease status
            $hasActiveDiseases = !empty($activeDiseases);
            $hasCriticalDiseases = false;
            $totalAffectedArea = 0;
            $totalAffectedPlants = 0;

            foreach ($activeDiseases as $disease) {
                if (strtolower($disease['severity_level']) === 'critical') {
                    $hasCriticalDiseases = true;
                }
                $totalAffectedArea += floatval($disease['affected_area']);
                $totalAffectedPlants += intval($disease['affected_plants']);
            }

            // Update block status
            $blockStatus = [
                'has_active_diseases' => $hasActiveDiseases ? 1 : 0,
                'has_critical_diseases' => $hasCriticalDiseases ? 1 : 0,
                'total_affected_area' => $totalAffectedArea,
                'total_affected_plants' => $totalAffectedPlants,
                'last_disease_check' => date('Y-m-d H:i:s'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmBlockModel()->update($block_id, $blockStatus);

            // Cache the results for faster retrieval
            cache()->save("block_disease_status_{$block_id}", $blockStatus, 3600); // Cache for 1 hour
        } catch (\Exception $e) {
            log_message('error', '[Update Block Disease Status] Failed to update status for block ' . $block_id . ': ' . $e->getMessage());
        }
    }

    // ... include all other crop-related methods from the original file ...
    // (fertilizer, pesticides, harvest, marketing, and disease management methods)

    /**
     * Edit market data view
     * @param int $id
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function edit_market_data($id)
    {
        try {
            log_message('debug', '[Edit Market Data] Starting with ID: ' . $id);
            
            // Get the marketing data record
            $marketing_data = $this->getFarmMarketingDataModel()
                ->select('
                    crops_farm_marketing_data.*,
                    adx_crops.crop_name,
                    crop_buyers.name as buyer_name,
                    users.name as created_by_name
                ')
                ->join('adx_crops', 'adx_crops.id = crops_farm_marketing_data.crop_id', 'left')
                ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id', 'left')
                ->join('users', 'users.id = crops_farm_marketing_data.created_by', 'left')
                ->where('crops_farm_marketing_data.id', $id)
                ->first();

            if (!$marketing_data) {
                log_message('error', '[Edit Market Data] Record not found for ID: ' . $id);
                throw new \Exception('Marketing data record not found');
            }

            // Verify farmer belongs to user's district
            $farmer = $this->getFarmersModel()->where([
                'id' => $marketing_data['farmer_id'],
                'district_id' => session()->get('district_id')
            ])->first();

            if (!$farmer) {
                log_message('error', '[Edit Market Data] Access denied for farmer ID: ' . $marketing_data['farmer_id']);
                throw new \Exception('Access denied');
            }

            // Get required data for dropdowns
            $data = [
                'title' => 'Edit Marketing Data',
                'page_header' => 'Edit Marketing Data',
                'marketing_data' => $marketing_data,
                'farmer' => $farmer,
                'provinces' => $this->getModel('provinces')->findAll(),
                'crops' => $this->getCropsModel()->findAll(),
                'buyers' => $this->getCropBuyersModel()->where('status', 'active')->findAll(),
                'farm_blocks' => $this->getFarmBlockModel()
                    ->where('farmer_id', $marketing_data['farmer_id'])
                    ->where('status', 'active')
                    ->findAll()
            ];

            log_message('debug', '[Edit Market Data] Successfully prepared view data');
            return view('staff/farms/edit_market_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[Edit Market Data] ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return redirect()->back()->with('error', 'An error occurred while loading market data: ' . $e->getMessage());
        }
    }
} 