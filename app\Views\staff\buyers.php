<?= $this->extend('staff/templates/staff_template') ?>
<?= $this->section('content') ?>
<div class="row mb-3">
    <div class="col-md-12">
        <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addBuyerModal">
            <i class="fas fa-plus-circle"></i> Add Buyer
        </button>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped" id="buyersTable">
                <thead>
                    <tr>
                        <th>Crop</th>
                        <th>Buyer Code</th>
                        <th>Name</th>
                        <th>Contact</th>
                        <th>Operation Span</th>
                        <th>Location</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($buyers as $buyer): ?>
                        <tr>
                            <td><?php foreach ($crops as $crop): ?>
                                <?php if($crop['id'] == $buyer['crop_id']): ?>
                                    <?= esc($crop['crop_name']) ?>
                                <?php endif; ?>
                            <?php endforeach; ?></td>
                            <td><?= esc($buyer['buyer_code']) ?></td>
                            <td><?= esc($buyer['name']) ?></td>
                            <td>
                                <?= esc($buyer['contact_number']) ?>
                                <?php if($buyer['email']): ?>
                                    <br>
                                    <?= esc($buyer['email']) ?>
                                <?php endif; ?>
                            </td>
                            <td><?= ucfirst(esc($buyer['operation_span'])) ?></td>
                            <td><?= esc($buyer['address']) ?></td>
                            <td>
                                <span class="badge bg-<?= $buyer['status'] === 'active' ? 'success' : 'danger' ?>">
                                    <?= ucfirst($buyer['status']) ?>
                                </span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary edit-btn"
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editBuyerModal"
                                        data-id="<?= $buyer['id'] ?>"
                                        data-buyer_code="<?= $buyer['buyer_code'] ?>"
                                        data-crop_id="<?= $buyer['crop_id'] ?>"
                                        data-name="<?= $buyer['name'] ?>"
                                        data-address="<?= $buyer['address'] ?>"
                                        data-contact_number="<?= $buyer['contact_number'] ?>"
                                        data-email="<?= $buyer['email'] ?>"
                                        data-operation_span="<?= $buyer['operation_span'] ?>"
                                        data-location_id="<?= $buyer['location_id'] ?>"
                                        data-description="<?= $buyer['description'] ?>">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Buyer Modal -->
<div class="modal fade" id="addBuyerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add Buyer</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/add-buyer', ['id' => 'addBuyerForm']) ?>
            <div class="modal-body">
                <div class="row">
                
                <div class="col-md-12">
                        <div class="mb-3">
                            <label for="crop_id" class="form-label">Crop *</label>
                            <select class="form-select" id="crop_id" name="crop_id" required>
                                <option value="">Select Crop</option>
                                <?php foreach ($crops as $crop): ?>
                                    <option value="<?= $crop['id'] ?>"><?= esc($crop['crop_name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Buyer Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_number" class="form-label">Contact Number *</label>
                            <input type="text" class="form-control" id="contact_number" name="contact_number" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="operation_span" class="form-label">Operation Span *</label>
                            <select class="form-select" id="operation_span" name="operation_span" required>
                                <option value="">Select Operation Span</option>
                                <option value="local">Local (Province Wide)</option>
                                <option value="national">National (Country Wide)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="address" class="form-label">Address *</label>
                    <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Save Buyer</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Buyer Modal -->
<div class="modal fade" id="editBuyerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Buyer</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/update-buyer', ['id' => 'editBuyerForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_id">
                
                <div class="row">
                
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="edit_crop_id" class="form-label">Crop *</label>
                            <select class="form-select" id="edit_crop_id" name="crop_id" required>
                                <option value="">Select Crop</option>
                                <?php foreach ($crops as $crop): ?>
                                    <option value="<?= $crop['id'] ?>"><?= esc($crop['crop_name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Buyer Name *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_contact_number" class="form-label">Contact Number *</label>
                            <input type="text" class="form-control" id="edit_contact_number" name="contact_number" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="edit_email" name="email">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_operation_span" class="form-label">Operation Span *</label>
                            <select class="form-select" id="edit_operation_span" name="operation_span" required>
                                <option value="">Select Operation Span</option>
                                <option value="local">Local (Province Wide)</option>
                                <option value="national">National (Country Wide)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="edit_address" class="form-label">Address *</label>
                    <textarea class="form-control" id="edit_address" name="address" rows="2" required></textarea>
                </div>

                <div class="mb-3">
                    <label for="edit_description" class="form-label">Description</label>
                    <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Update Buyer</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#buyersTable').DataTable({
        responsive: true,
        order: [[0, 'desc']],
        columnDefs: [
            { 
                targets: -1, // Last column (Actions)
                orderable: false 
            }
        ]
    });

    // Handle edit button click
    $('.edit-btn').click(function() {
        const data = $(this).data();
        
        // Populate form fields
        $('#edit_id').val(data.id);
        $('#edit_crop_id').val(data.crop_id);
        $('#edit_name').val(data.name);
        $('#edit_contact_number').val(data.contact_number);
        $('#edit_email').val(data.email);
        $('#edit_operation_span').val(data.operation_span);
        $('#edit_address').val(data.address);
        $('#edit_description').val(data.description);
    });

    // Form submission handlers
    $('#addBuyerForm, #editBuyerForm').submit(function(e) {
        e.preventDefault();
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.html();

        $.ajax({
            url: $form.attr('action'),
            type: 'POST',
            data: $form.serialize(),
            dataType: 'json',
            beforeSend: function() {
                $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Processing...').prop('disabled', true);
            },
            success: function(response) {
                if (response.status === 'success') {
                    toastr.success(response.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while processing your request');
            },
            complete: function() {
                $submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
<?= $this->endSection() ?>