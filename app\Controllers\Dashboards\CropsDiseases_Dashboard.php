<?php

namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\CropsFarmDiseaseDataModel;
use App\Models\InfectionsModel;
use App\Models\provinceModel;

class CropsDiseases_Dashboard extends BaseController
{
    protected $cropsFarmDiseaseDataModel;
    protected $infectionsModel;
    protected $provinceModel;

    public function __construct()
    {
        $this->cropsFarmDiseaseDataModel = new CropsFarmDiseaseDataModel();
        $this->infectionsModel = new InfectionsModel();
        $this->provinceModel = new provinceModel();
        helper(['url', 'form', 'info']);
    }

    public function index()
    {
        $data['title'] = 'Crops Diseases Dashboard';
        $data['menu'] = 'crops-diseases';

        // Build query using CodeIgniter's automatic model with grouping by block and disease
        $data['diseases'] = $this->cropsFarmDiseaseDataModel
            ->select('
                crops_farm_blocks.block_code,
                crops_farm_blocks.block_site,
                crops_farm_blocks.lat,
                crops_farm_blocks.lon,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_province.name as province_name,
                adx_district.name as district_name,
                adx_llg.name as llg_name,
                adx_ward.name as ward_name,
                crops_farm_disease_data.disease_name,
                SUM(crops_farm_disease_data.number_of_plants) as total_plants_affected,
                MAX(crops_farm_disease_data.action_date) as latest_action_date
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_disease_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
            ->join('adx_province', 'adx_province.id = crops_farm_blocks.province_id')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
            ->where('crops_farm_disease_data.status', 'active')
            ->where('crops_farm_blocks.status', 'active')
            ->groupBy('crops_farm_blocks.block_code, crops_farm_blocks.block_site, crops_farm_blocks.lat, crops_farm_blocks.lon, 
                      farmer_information.given_name, farmer_information.surname, adx_crops.crop_name, 
                      adx_province.name, adx_district.name, adx_llg.name, adx_ward.name, crops_farm_disease_data.disease_name')
            ->findAll();

        // Get infections data
        $data['infections'] = $this->infectionsModel->findAll();

        return view('dashboard_reports/dashboard_crops_diseases', $data);
    }
}
