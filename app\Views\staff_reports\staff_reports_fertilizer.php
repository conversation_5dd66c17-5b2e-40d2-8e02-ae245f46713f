<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Fertilizer Report</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/reports') ?>">Reports</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Fertilizer</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Fertilizer Distribution</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="fertilizerDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Applications by Crop</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="cropChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Quantity by Crop</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="quantityChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Distribution by LLG</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="llgDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- LLG Distribution Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribution by LLG</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>LLG</th>
                            <?php
                            // Get unique fertilizer names
                            $unique_fertilizers = array_unique(array_map(function($item) {
                                return $item['fertilizer_name'];
                            }, $fertilizer_data));
                            sort($unique_fertilizers);

                            foreach ($unique_fertilizers as $fertilizer_name): ?>
                                <th class="text-center" colspan="2"><?= esc($fertilizer_name) ?></th>
                            <?php endforeach; ?>
                            <th class="text-center" colspan="2">Total</th>
                        </tr>
                        <tr>
                            <th></th>
                            <?php foreach ($unique_fertilizers as $fertilizer_name): ?>
                                <th class="text-center">Apps</th>
                                <th class="text-center">Quantity</th>
                            <?php endforeach; ?>
                            <th class="text-center">Apps</th>
                            <th class="text-center">Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Get unique LLGs and sort them
                        $unique_llgs = array_unique(array_map(function($item) {
                            return $item['llg_name'];
                        }, $fertilizer_data));
                        sort($unique_llgs);

                        // Initialize totals
                        $fertilizer_totals = array_fill_keys($unique_fertilizers, ['apps' => 0, 'quantity' => 0]);
                        $grand_total = ['apps' => 0, 'quantity' => 0];

                        // Group data by LLG and Fertilizer
                        $llg_data = [];
                        foreach ($fertilizer_data as $application) {
                            $llg = $application['llg_name'];
                            $fertilizer_name = $application['fertilizer_name'];

                            if (!isset($llg_data[$llg])) {
                                $llg_data[$llg] = array_fill_keys($unique_fertilizers, ['apps' => 0, 'quantity' => 0]);
                            }

                            $llg_data[$llg][$fertilizer_name]['apps']++;
                            $llg_data[$llg][$fertilizer_name]['quantity'] += (float)$application['quantity'];
                        }

                        // Display data
                        foreach ($unique_llgs as $llg):
                            $llg_total = ['apps' => 0, 'quantity' => 0];
                        ?>
                            <tr>
                                <td><?= esc($llg) ?></td>
                                <?php foreach ($unique_fertilizers as $fertilizer_name):
                                    $apps = $llg_data[$llg][$fertilizer_name]['apps'] ?? 0;
                                    $quantity = $llg_data[$llg][$fertilizer_name]['quantity'] ?? 0;

                                    // Update totals
                                    $fertilizer_totals[$fertilizer_name]['apps'] += $apps;
                                    $fertilizer_totals[$fertilizer_name]['quantity'] += $quantity;
                                    $llg_total['apps'] += $apps;
                                    $llg_total['quantity'] += $quantity;
                                ?>
                                    <td class="text-end"><?= $apps ? number_format($apps) : '-' ?></td>
                                    <td class="text-end"><?= $quantity ? number_format($quantity, 2) : '-' ?></td>
                                <?php endforeach; ?>
                                <td class="text-end fw-bold"><?= number_format($llg_total['apps']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($llg_total['quantity'], 2) ?></td>
                            </tr>
                        <?php
                            $grand_total['apps'] += $llg_total['apps'];
                            $grand_total['quantity'] += $llg_total['quantity'];
                        endforeach;
                        ?>
                        <tr class="table-light">
                            <td class="fw-bold">Total</td>
                            <?php foreach ($unique_fertilizers as $fertilizer_name): ?>
                                <td class="text-end fw-bold"><?= number_format($fertilizer_totals[$fertilizer_name]['apps']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($fertilizer_totals[$fertilizer_name]['quantity'], 2) ?></td>
                            <?php endforeach; ?>
                            <td class="text-end fw-bold"><?= number_format($grand_total['apps']) ?></td>
                            <td class="text-end fw-bold"><?= number_format($grand_total['quantity'], 2) ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Table Card -->
    <div class="card">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-flask me-2"></i>Fertilizer Application Records</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Block Code</th>
                            <th>Farmer Name</th>
                            <th>Crop</th>
                            <th>Fertilizer</th>
                            <th>Action Date</th>
                            <th>Quantity</th>
                            <th>Location</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Group data by block and keep only the latest application
                        $distinct_applications = [];
                        foreach ($fertilizer_data as $application) {
                            // Skip if block_id is not set
                            if (!isset($application['block_id'])) {
                                continue;
                            }

                            $block_id = $application['block_id'];
                            $action_date = strtotime($application['action_date']);

                            // If block not seen before or this application is more recent
                            if (!isset($distinct_applications[$block_id]) ||
                                strtotime($distinct_applications[$block_id]['action_date']) < $action_date) {
                                $distinct_applications[$block_id] = $application;
                            }
                        }

                        // Sort by latest action date
                        uasort($distinct_applications, function($a, $b) {
                            return strtotime($b['action_date']) - strtotime($a['action_date']);
                        });

                        $loop_count = 1;
                        foreach ($distinct_applications as $application): ?>
                            <tr>
                                <td><?= $loop_count++ ?></td>
                                <td><?= esc($application['block_code']) ?></td>
                                <td><?= esc($application['given_name']) . ' ' . esc($application['surname']) ?></td>
                                <td><?= esc($application['crop_name']) ?></td>
                                <td><?= esc($application['fertilizer_name']) ?></td>
                                <td>
                                    <?php
                                    $action_date = strtotime($application['action_date']);
                                    $is_recent = (time() - $action_date) < (7 * 24 * 60 * 60); // Within last 7 days
                                    ?>
                                    <?= date('d M Y', $action_date) ?>
                                    <?php if ($is_recent): ?>
                                        <span class="badge bg-success ms-1" title="Recent application">New</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end"><?= number_format($application['quantity'], 2) ?></td>
                                <td><?= esc($application['llg_name']) ?></td>
                                <td>
                                    <span class="badge bg-<?= $application['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($application['status']) ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // Chart data
    const fertilizerData = <?= json_encode($fertilizer_data) ?>;

    // Chart configurations
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 15
                }
            }
        }
    };

    // Check if we have any data
    if (fertilizerData.length === 0) {
        console.log('No fertilizer data available');
        $('.container-fluid').prepend('<div class="alert alert-info">No fertilizer data available for the current filters.</div>');
    }

    // Process data for charts
    const fertilizerStats = fertilizerData.reduce((acc, application) => {
        const fertName = application.fertilizer_name || 'Unknown';
        if (!acc[fertName]) {
            acc[fertName] = {
                count: 0,
                quantity: 0
            };
        }
        acc[fertName].count++;
        acc[fertName].quantity += parseFloat(application.quantity || 0);
        return acc;
    }, {});

    const cropStats = fertilizerData.reduce((acc, application) => {
        const cropName = application.crop_name || 'Unknown';
        if (!acc[cropName]) {
            acc[cropName] = {
                count: 0,
                quantity: 0,
                color: application.crop_color_code || '#' + Math.floor(Math.random()*16777215).toString(16)
            };
        }
        acc[cropName].count++;
        acc[cropName].quantity += parseFloat(application.quantity || 0);
        return acc;
    }, {});

    // Process data for LLG distribution
    const llgStats = fertilizerData.reduce((acc, application) => {
        const llgName = application.llg_name || 'Unknown';
        const fertName = application.fertilizer_name || 'Unknown';

        if (!acc[llgName]) {
            acc[llgName] = {
                fertilizers: {}
            };
        }
        if (!acc[llgName].fertilizers[fertName]) {
            acc[llgName].fertilizers[fertName] = {
                count: 0,
                quantity: 0
            };
        }
        acc[llgName].fertilizers[fertName].count++;
        acc[llgName].fertilizers[fertName].quantity += parseFloat(application.quantity || 0);
        return acc;
    }, {});

    // Only create charts if we have data
    if (fertilizerData.length > 0) {
        try {
            // Fertilizer Distribution Chart
            new Chart(document.getElementById('fertilizerDistributionChart'), {
                type: 'pie',
                data: {
                    labels: Object.keys(fertilizerStats),
                    datasets: [{
                        data: Object.values(fertilizerStats).map(stat => stat.count),
                        backgroundColor: Object.keys(fertilizerStats).map((_, index) =>
                            `hsl(${(index * 360) / Object.keys(fertilizerStats).length}, 70%, 50%)`)
                    }]
                },
                options: chartOptions
            });

            // Applications by Crop Chart
            new Chart(document.getElementById('cropChart'), {
                type: 'pie',
                data: {
                    labels: Object.keys(cropStats),
                    datasets: [{
                        data: Object.values(cropStats).map(stat => stat.count),
                        backgroundColor: Object.values(cropStats).map(stat => stat.color)
                    }]
                },
                options: chartOptions
            });

            // Quantity by Crop Chart
            new Chart(document.getElementById('quantityChart'), {
                type: 'bar',
                data: {
                    labels: Object.keys(cropStats),
                    datasets: [{
                        label: 'Quantity',
                        data: Object.values(cropStats).map(stat => stat.quantity),
                        backgroundColor: Object.values(cropStats).map(stat => stat.color)
                    }]
                },
                options: {
                    ...chartOptions,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2);
                                }
                            }
                        }
                    }
                }
            });

            // LLG Distribution Chart
            const llgLabels = Object.keys(llgStats);
            const fertilizerTypes = [...new Set(fertilizerData.map(d => d.fertilizer_name || 'Unknown'))];
            const llgDatasets = fertilizerTypes.map((fertilizerName, index) => ({
                label: fertilizerName,
                data: llgLabels.map(llg => llgStats[llg].fertilizers[fertilizerName]?.quantity || 0),
                backgroundColor: `hsl(${(index * 360) / fertilizerTypes.length}, 70%, 50%)`
            }));

            new Chart(document.getElementById('llgDistributionChart'), {
                type: 'bar',
                data: {
                    labels: llgLabels,
                    datasets: llgDatasets
                },
                options: {
                    ...chartOptions,
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2);
                                }
                            }
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Error creating charts:', error);
            $('.container-fluid').prepend('<div class="alert alert-warning">Error creating charts. Please check the console for details.</div>');
        }
    } else {
        // Display message for each chart container
        $('.chart-container').each(function() {
            $(this).html('<div class="text-center text-muted p-4">No data available</div>');
        });
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
