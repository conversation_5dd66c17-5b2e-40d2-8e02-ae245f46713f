<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= $page_header ?? 'Climate Focus Details' ?></h5>
                <div>
                    <a href="<?= base_url('staff/tools/climate-data/' . $climate_focus['id'] . '/edit') ?>" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i> Edit
                    </a>
                    <a href="<?= base_url('staff/tools/climate-data') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">ID</th>
                                <td><?= $climate_focus['id'] ?></td>
                            </tr>
                            <tr>
                                <th>Location</th>
                                <td><?= esc($climate_focus['location']) ?></td>
                            </tr>
                            <tr>
                                <th>GPS Coordinates</th>
                                <td><?= esc($climate_focus['gps']) ?></td>
                            </tr>
                            <tr>
                                <th>District</th>
                                <td><?= esc($climate_focus['district_name']) ?></td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    <?php if ($climate_focus['status'] == 1): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Created At</th>
                                <td><?= date('d M Y H:i', strtotime($climate_focus['created_at'])) ?></td>
                            </tr>
                            <tr>
                                <th>Created By</th>
                                <td><?= $climate_focus['created_by'] ?></td>
                            </tr>
                            <?php if ($climate_focus['updated_at'] && $climate_focus['updated_at'] != $climate_focus['created_at']): ?>
                            <tr>
                                <th>Updated At</th>
                                <td><?= date('d M Y H:i', strtotime($climate_focus['updated_at'])) ?></td>
                            </tr>
                            <tr>
                                <th>Updated By</th>
                                <td><?= $climate_focus['updated_by'] ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <th>Remarks</th>
                                <td><?= nl2br(esc($climate_focus['remarks'] ?? 'No remarks')) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Location Map</h5>
                            </div>
                            <div class="card-body">
                                <div id="map" style="height: 400px; width: 100%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Climate Data (Last 6 Months)</h5>
                                <div class="spinner-border text-primary d-none" id="weatherSpinner" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info mb-4" id="weatherInfo">
                                    <i class="fas fa-info-circle me-2"></i> Loading climate data for this location...
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">Temperature (°C)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="temperatureChart"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0">Precipitation (mm)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="precipitationChart"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">Wind Speed (km/h)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="windSpeedChart"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0">Cloud Cover (%)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="cloudCoverChart"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-secondary text-white">
                                                <h6 class="mb-0">Humidity (%)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="humidityChart"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header bg-dark text-white">
                                        <h6 class="mb-0">Monthly Climate Summary</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="weatherTable">
                                                <thead>
                                                    <tr>
                                                        <th>Month</th>
                                                        <th>Temperature (°C)</th>
                                                        <th>Precipitation (mm)</th>
                                                        <th>Wind Speed (km/h)</th>
                                                        <th>Cloud Cover (%)</th>
                                                        <th>Humidity (%)</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Will be populated by JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- Climate Elements Summary Card -->
                                        <div class="card mt-4">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">Climate Elements Summary (Last 6 Months)</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered" id="summaryTable">
                                                        <thead>
                                                            <tr>
                                                                <th>Climate Element</th>
                                                                <th>Average</th>
                                                                <th>High</th>
                                                                <th>Low</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <!-- Will be populated by JavaScript -->
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <!-- Extreme Weather Events Table -->
                                                <div class="card mt-4">
                                                    <div class="card-header bg-danger text-white">
                                                        <h6 class="mb-0">Extreme Weather Events</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="table-responsive">
                                                            <table class="table table-striped table-bordered" id="extremeEventsTable">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Climate Element</th>
                                                                        <th>Highest Value</th>
                                                                        <th>Date Occurred</th>
                                                                        <th>Lowest Value</th>
                                                                        <th>Date Occurred</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <!-- Will be populated by JavaScript -->
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Leaflet CSS and JS for OpenStreetMap -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<!-- ApexCharts for candlestick charts -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

<!-- Chart.js for other charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Load Chart.js annotation plugin -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.0/dist/chartjs-plugin-annotation.min.js"></script>

<script>
    // Initialize the map using Leaflet and OpenStreetMap
    function initMap() {
        // Parse GPS coordinates
        const gpsString = "<?= $climate_focus['gps'] ?>".trim();
        const parts = gpsString.split(',');
        let lat = -6.314993; // Default latitude (PNG)
        let lng = 143.95555; // Default longitude (PNG)

        // Process the coordinate
        if (parts.length >= 2) {
            const parsedLat = parseFloat(parts[0].trim());
            const parsedLng = parseFloat(parts[1].trim());
            if (!isNaN(parsedLat) && !isNaN(parsedLng)) {
                lat = parsedLat;
                lng = parsedLng;
            }
        }

        // Create map centered on the coordinate
        const map = L.map('map').setView([lat, lng], 12);

        // Add OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add marker for the coordinate
        L.marker([lat, lng])
            .addTo(map)
            .bindPopup("<?= esc($climate_focus['location']) ?>")
            .openPopup();

        // Fetch climate data after map is initialized
        fetchClimateData(lat, lng);
    }

    // Weather code descriptions
    const weatherCodeDescriptions = {
        0: "Clear sky",
        1: "Mainly clear",
        2: "Partly cloudy",
        3: "Overcast",
        45: "Fog",
        48: "Depositing rime fog",
        51: "Light drizzle",
        53: "Moderate drizzle",
        55: "Dense drizzle",
        56: "Light freezing drizzle",
        57: "Dense freezing drizzle",
        61: "Slight rain",
        63: "Moderate rain",
        65: "Heavy rain",
        66: "Light freezing rain",
        67: "Heavy freezing rain",
        71: "Slight snow fall",
        73: "Moderate snow fall",
        75: "Heavy snow fall",
        77: "Snow grains",
        80: "Slight rain showers",
        81: "Moderate rain showers",
        82: "Violent rain showers",
        85: "Slight snow showers",
        86: "Heavy snow showers",
        95: "Thunderstorm",
        96: "Thunderstorm with slight hail",
        99: "Thunderstorm with heavy hail"
    };

    // Chart instances
    let temperatureChart, precipitationChart, windSpeedChart, cloudCoverChart, humidityChart;

    // Process daily data into weekly data for candlestick charts
    function processWeeklyData(dailyData) {
        // Group data by week
        const weeks = [];
        let currentWeek = {
            startDate: '',
            endDate: '',
            temperature: { open: 0, high: -100, low: 100, close: 0 },
            precipitation: { total: 0, max: 0, days: 0, avg: 0 },
            wind: { open: 0, high: 0, low: 9999, close: 0 },
            cloud: { open: 0, high: 0, low: 100, close: 0 },
            humidity: { open: 0, high: 0, low: 100, close: 0 }
        };

        let dayCounter = 0;

        // Process each day
        for (let i = 0; i < dailyData.time.length; i++) {
            const date = new Date(dailyData.time[i]);
            const temp = (dailyData.temperature_2m_max[i] + dailyData.temperature_2m_min[i]) / 2;
            const precip = dailyData.precipitation_sum[i] || 0;
            const wind = dailyData.windspeed_10m_max[i] || 0;
            const cloud = dailyData.cloudcover_mean[i] || 0;
            const humidity = dailyData.relativehumidity_2m_mean[i] || 0;

            // First day of a week
            if (dayCounter === 0) {
                currentWeek.startDate = dailyData.time[i];
                currentWeek.temperature.open = temp;
                currentWeek.wind.open = wind;
                currentWeek.cloud.open = cloud;
                currentWeek.humidity.open = humidity;
            }

            // Update high/low values
            currentWeek.temperature.high = Math.max(currentWeek.temperature.high, dailyData.temperature_2m_max[i] || -100);
            currentWeek.temperature.low = Math.min(currentWeek.temperature.low, dailyData.temperature_2m_min[i] || 100);
            currentWeek.precipitation.total += precip;
            currentWeek.precipitation.max = Math.max(currentWeek.precipitation.max, precip);
            currentWeek.precipitation.days += precip > 0 ? 1 : 0;
            currentWeek.wind.high = Math.max(currentWeek.wind.high, wind);
            currentWeek.wind.low = Math.min(currentWeek.wind.low, wind);
            currentWeek.cloud.high = Math.max(currentWeek.cloud.high, cloud);
            currentWeek.cloud.low = Math.min(currentWeek.cloud.low, cloud);
            currentWeek.humidity.high = Math.max(currentWeek.humidity.high, humidity);
            currentWeek.humidity.low = Math.min(currentWeek.humidity.low, humidity);

            // Last day of this segment or end of data
            if (dayCounter === 6 || i === dailyData.time.length - 1) {
                currentWeek.endDate = dailyData.time[i];
                currentWeek.temperature.close = temp;
                currentWeek.wind.close = wind;
                currentWeek.cloud.close = cloud;
                currentWeek.humidity.close = humidity;

                // Calculate precipitation average for days with rain
                currentWeek.precipitation.avg = currentWeek.precipitation.days > 0 ?
                    currentWeek.precipitation.total / currentWeek.precipitation.days : 0;

                // Add week to array
                weeks.push(JSON.parse(JSON.stringify(currentWeek)));

                // Reset for next week
                currentWeek = {
                    startDate: '',
                    endDate: '',
                    temperature: { open: 0, high: -100, low: 100, close: 0 },
                    precipitation: { total: 0, max: 0, days: 0, avg: 0 },
                    wind: { open: 0, high: 0, low: 9999, close: 0 },
                    cloud: { open: 0, high: 0, low: 100, close: 0 },
                    humidity: { open: 0, high: 0, low: 100, close: 0 }
                };
                dayCounter = 0;
            } else {
                dayCounter++;
            }
        }

        return weeks;
    }

    // Create temperature candlestick chart
    function createTemperatureChart(dailyData) {
        // First destroy existing chart if it exists
        if (temperatureChart) {
            if (typeof temperatureChart.destroy === 'function') {
                temperatureChart.destroy();
            } else {
                document.getElementById('temperatureChart').innerHTML = '';
            }
        }

        // Process data into weekly segments for the candlestick chart
        const weeklyData = processWeeklyData(dailyData);

        // Prepare data for ApexCharts
        const candleData = weeklyData.map(week => ({
            x: `${new Date(week.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(week.endDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
            y: [
                parseFloat(week.temperature.open.toFixed(1)),
                parseFloat(week.temperature.high.toFixed(1)),
                parseFloat(week.temperature.low.toFixed(1)),
                parseFloat(week.temperature.close.toFixed(1))
            ]
        }));

        // Find highest and lowest points for annotations
        let maxTemp = -100;
        let minTemp = 100;
        let maxTempIndex = 0;
        let minTempIndex = 0;

        for (let i = 0; i < weeklyData.length; i++) {
            if (weeklyData[i].temperature.high > maxTemp) {
                maxTemp = weeklyData[i].temperature.high;
                maxTempIndex = i;
            }
            if (weeklyData[i].temperature.low < minTemp) {
                minTemp = weeklyData[i].temperature.low;
                minTempIndex = i;
            }
        }

        // ApexCharts configuration
        const options = {
            series: [{
                name: 'Temperature',
                data: candleData
            }],
            chart: {
                type: 'candlestick',
                height: 400,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                }
            },
            // Title removed to avoid overlap with card header
            tooltip: {
                enabled: true,
                custom: function({ seriesIndex, dataPointIndex, w }) {
                    const data = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
                    return `
                        <div class="apexcharts-tooltip-box">
                            <div class="apexcharts-tooltip-title">${data.x}</div>
                            <div>
                                <span class="apexcharts-tooltip-text">Open: ${data.y[0]}°C</span><br>
                                <span class="apexcharts-tooltip-text" style="color: #ff0000;">High: ${data.y[1]}°C</span><br>
                                <span class="apexcharts-tooltip-text" style="color: #0000ff;">Low: ${data.y[2]}°C</span><br>
                                <span class="apexcharts-tooltip-text">Close: ${data.y[3]}°C</span>
                            </div>
                        </div>
                    `;
                }
            },
            xaxis: {
                labels: {
                    rotate: 0,
                    style: {
                        fontSize: '12px'
                    }
                },
                title: {
                    text: 'Weekly Periods'
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return val.toFixed(1) + '°C';
                    }
                },
                title: {
                    text: 'Temperature (°C)'
                }
            },
            plotOptions: {
                candlestick: {
                    colors: {
                        upward: '#26a69a',  // green - temperature increase
                        downward: '#ef5350' // red - temperature decrease
                    },
                    wick: {
                        useFillColor: true
                    }
                }
            },
            annotations: {
                points: [
                    {
                        x: candleData[maxTempIndex].x,
                        y: maxTemp,
                        marker: {
                            size: 6,
                            fillColor: '#ff0000',
                            strokeColor: '#ffffff',
                            strokeWidth: 2
                        },
                        label: {
                            text: `Max: ${maxTemp.toFixed(1)}°C`,
                            offsetY: -15,
                            style: {
                                background: '#ff0000',
                                color: '#ffffff'
                            }
                        }
                    },
                    {
                        x: candleData[minTempIndex].x,
                        y: minTemp,
                        marker: {
                            size: 6,
                            fillColor: '#0000ff',
                            strokeColor: '#ffffff',
                            strokeWidth: 2
                        },
                        label: {
                            text: `Min: ${minTemp.toFixed(1)}°C`,
                            offsetY: 15,
                            style: {
                                background: '#0000ff',
                                color: '#ffffff'
                            }
                        }
                    }
                ]
            }
        };

        temperatureChart = new ApexCharts(document.getElementById('temperatureChart'), options);
        temperatureChart.render();
    }

    // Create precipitation chart with candlestick-like visualization
    function createPrecipitationChart(dailyData) {
        // First destroy existing chart if it exists
        if (precipitationChart) {
            if (typeof precipitationChart.destroy === 'function') {
                precipitationChart.destroy();
            } else {
                document.getElementById('precipitationChart').innerHTML = '';
            }
        }

        // Process data into weekly segments
        const weeklyData = processWeeklyData(dailyData);

        // Prepare data for ApexCharts
        const candleData = weeklyData.map(week => ({
            x: `${new Date(week.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(week.endDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
            y: [
                0,  // always start from zero for rainfall
                parseFloat(week.precipitation.max.toFixed(1)),
                0,  // minimum always zero for rainfall
                parseFloat(week.precipitation.total.toFixed(1))  // close is total weekly rainfall
            ]
        }));

        // Find highest precipitation for annotation
        let maxPrecip = 0;
        let maxPrecipIndex = 0;

        for (let i = 0; i < weeklyData.length; i++) {
            if (weeklyData[i].precipitation.max > maxPrecip) {
                maxPrecip = weeklyData[i].precipitation.max;
                maxPrecipIndex = i;
            }
        }

        // ApexCharts configuration
        const options = {
            series: [{
                name: 'Precipitation',
                data: candleData
            }],
            chart: {
                type: 'candlestick',
                height: 400,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                }
            },
            // Title removed to avoid overlap with card header
            tooltip: {
                enabled: true,
                custom: function({ seriesIndex, dataPointIndex, w }) {
                    const data = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
                    const weekData = weeklyData[dataPointIndex];
                    return `
                        <div class="apexcharts-tooltip-box">
                            <div class="apexcharts-tooltip-title">${data.x}</div>
                            <div>
                                <span class="apexcharts-tooltip-text" style="color: #ff0000;">Max Daily: ${data.y[1]}mm</span><br>
                                <span class="apexcharts-tooltip-text">Total: ${data.y[3]}mm</span><br>
                                <span class="apexcharts-tooltip-text">Rainy Days: ${weekData.precipitation.days}</span><br>
                                <span class="apexcharts-tooltip-text">Avg per Rainy Day: ${weekData.precipitation.avg.toFixed(1)}mm</span>
                            </div>
                        </div>
                    `;
                }
            },
            xaxis: {
                labels: {
                    rotate: 0,
                    style: {
                        fontSize: '12px'
                    }
                },
                title: {
                    text: 'Weekly Periods'
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return val.toFixed(1) + 'mm';
                    }
                },
                title: {
                    text: 'Precipitation (mm)'
                }
            },
            plotOptions: {
                candlestick: {
                    colors: {
                        upward: '#1a73e8',  // blue for precipitation
                        downward: '#42a5f5'  // lighter blue for days with less precipitation
                    },
                    wick: {
                        useFillColor: true
                    }
                }
            },
            annotations: {
                points: [
                    {
                        x: candleData[maxPrecipIndex].x,
                        y: maxPrecip,
                        marker: {
                            size: 6,
                            fillColor: '#ff0000',
                            strokeColor: '#ffffff',
                            strokeWidth: 2
                        },
                        label: {
                            text: `Max: ${maxPrecip.toFixed(1)}mm`,
                            offsetY: -15,
                            style: {
                                background: '#ff0000',
                                color: '#ffffff'
                            }
                        }
                    }
                ]
            }
        };

        precipitationChart = new ApexCharts(document.getElementById('precipitationChart'), options);
        precipitationChart.render();
    }

    // Fetch climate data from Open-Meteo API
    async function fetchClimateData(lat, lng) {
        try {
            // Show loading state
            document.getElementById('weatherSpinner').classList.remove('d-none');
            document.getElementById('weatherInfo').classList.remove('d-none');
            document.getElementById('weatherInfo').innerHTML = '<i class="fas fa-info-circle me-2"></i> Loading climate data for this location...';

            // Calculate dates for historical data - with validation to prevent future dates
            const today = new Date();

            // Always use the current date for the end date
            const realCurrentDate = new Date();

            // Log the current date being used
            console.log(`Using current date: ${realCurrentDate.toISOString().split('T')[0]} for climate data fetch`);

            // Set end date to current date (or fixed date if system date is wrong)
            const endDate = realCurrentDate.toISOString().split('T')[0];

            // Calculate start date as exactly 6 months before end date
            const sixMonthsAgo = new Date(realCurrentDate);
            sixMonthsAgo.setMonth(realCurrentDate.getMonth() - 6);
            const startDate = sixMonthsAgo.toISOString().split('T')[0];

            // Log parameters for debugging
            console.log('Climate data request parameters:', { lat, lng, startDate, endDate });

            // Try different API endpoints in sequence
            let response = null;
            let data = null;
            let url = null;

            // First try: Combined hourly and daily data
            try {
                url = `https://archive-api.open-meteo.com/v1/archive?latitude=${encodeURIComponent(lat)}&longitude=${encodeURIComponent(lng)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}&hourly=temperature_2m,relative_humidity_2m,precipitation,windspeed_10m,cloudcover,weather_code&daily=temperature_2m_max,temperature_2m_min,precipitation_sum,windspeed_10m_max,cloudcover_mean,relativehumidity_2m_mean&timezone=auto`;
                console.log('Climate data API URL (first attempt):', url);

                response = await fetch(url, { timeout: 10000 });
                if (response.ok) {
                    data = await response.json();
                }
            } catch (firstError) {
                console.warn('First API attempt failed:', firstError);
            }

            // Second try: Only hourly data (fallback)
            if (!data) {
                try {
                    url = `https://archive-api.open-meteo.com/v1/archive?latitude=${encodeURIComponent(lat)}&longitude=${encodeURIComponent(lng)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}&hourly=temperature_2m,relative_humidity_2m,precipitation,windspeed_10m,cloudcover,weather_code&timezone=auto`;
                    console.log('Climate data API URL (second attempt):', url);

                    response = await fetch(url, { timeout: 10000 });
                    if (response.ok) {
                        data = await response.json();
                    }
                } catch (secondError) {
                    console.warn('Second API attempt failed:', secondError);
                }
            }

            // Third try: Only daily data (original approach)
            if (!data) {
                try {
                    url = `https://archive-api.open-meteo.com/v1/archive?latitude=${encodeURIComponent(lat)}&longitude=${encodeURIComponent(lng)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}&daily=temperature_2m_max,temperature_2m_min,precipitation_sum,windspeed_10m_max,cloudcover_mean,relativehumidity_2m_mean,weather_code&timezone=auto`;
                    console.log('Climate data API URL (third attempt):', url);

                    response = await fetch(url, { timeout: 10000 });
                    if (response.ok) {
                        data = await response.json();
                    }
                } catch (thirdError) {
                    console.warn('Third API attempt failed:', thirdError);
                }
            }

            // Fourth try: Alternative endpoint (forecast API with past days)
            if (!data) {
                try {
                    const pastDays = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));
                    url = `https://api.open-meteo.com/v1/forecast?latitude=${encodeURIComponent(lat)}&longitude=${encodeURIComponent(lng)}&past_days=${Math.min(pastDays, 92)}&daily=temperature_2m_max,temperature_2m_min,precipitation_sum,windspeed_10m_max,weathercode&timezone=auto`;
                    console.log('Climate data API URL (fourth attempt):', url);

                    response = await fetch(url, { timeout: 10000 });
                    if (response.ok) {
                        data = await response.json();
                        // Rename weathercode to weather_code for consistency
                        if (data.daily && data.daily.weathercode) {
                            data.daily.weather_code = data.daily.weathercode;
                            delete data.daily.weathercode;
                        }
                    }
                } catch (fourthError) {
                    console.warn('Fourth API attempt failed:', fourthError);
                }
            }

            // If all attempts failed, throw error
            if (!data) {
                throw new Error("All API endpoints failed to respond. Please try again later.");
            }

            console.log('Climate data API response:', data);

            // FIXED: Ensure this variable exists before processing data
            try {
                if (data.hourly && data.hourly.time && data.daily && data.daily.time) {
                    // Combined hourly and daily data
                    document.getElementById('weatherInfo').classList.add('d-none');
                    const processedData = processHourlyToDaily(data.hourly, data.daily);
                    updateCharts(processedData);
                    populateWeatherTable(processedData);
                } else if (data.hourly && data.hourly.time) {
                    // Only hourly data
                    document.getElementById('weatherInfo').classList.add('d-none');
                    const processedData = processOnlyHourlyData(data.hourly);
                    updateCharts(processedData);
                    populateWeatherTable(processedData);
                } else if (data.daily && data.daily.time) {
                    // Only daily data
                    document.getElementById('weatherInfo').classList.add('d-none');
                    updateCharts(data.daily);
                    populateWeatherTable(data.daily);
                } else {
                    // Handle case where data structure is not as expected
                    let errorMsg = "Failed to load climate data: Invalid response format.";
                    if (data && data.error) {
                        errorMsg = `Failed to load climate data: ${data.reason || data.error}`;
                    }
                    console.error('Invalid API response format:', data);
                    showChartError(errorMsg);
                }
            } catch (processingError) {
                console.error('Error processing climate data:', processingError);
                showChartError(`Error processing climate data: ${processingError.message}`);
            }
        } catch (error) {
            console.error('Error fetching climate data:', error);
            showChartError(`Error fetching climate data: ${error.message}`);
        } finally {
            // Hide loading spinner
            document.getElementById('weatherSpinner').classList.add('d-none');
        }
    }

    // Process hourly data to daily format
    function processHourlyToDaily(hourlyData, dailyData) {
        // We'll use the daily data that's already provided by the API
        // and supplement with any data we need to calculate from hourly
        return {
            time: dailyData.time,
            temperature_2m_max: dailyData.temperature_2m_max,
            temperature_2m_min: dailyData.temperature_2m_min,
            precipitation_sum: dailyData.precipitation_sum,
            windspeed_10m_max: dailyData.windspeed_10m_max,
            cloudcover_mean: dailyData.cloudcover_mean,
            relativehumidity_2m_mean: dailyData.relativehumidity_2m_mean,
            // Extract most common weather code for each day
            weather_code: extractDailyWeatherCodes(hourlyData.time, hourlyData.weather_code)
        };
    }

    // Extract most common weather code for each day from hourly data
    function extractDailyWeatherCodes(hourlyTimes, weatherCodes) {
        const dailyWeatherCodes = [];
        const dailyWeatherCodeMap = {};

        // Group weather codes by day
        for (let i = 0; i < hourlyTimes.length; i++) {
            const date = hourlyTimes[i].split('T')[0];
            if (!dailyWeatherCodeMap[date]) {
                dailyWeatherCodeMap[date] = {};
            }

            const code = weatherCodes[i];
            if (code !== null && code !== undefined) {
                if (!dailyWeatherCodeMap[date][code]) {
                    dailyWeatherCodeMap[date][code] = 0;
                }
                dailyWeatherCodeMap[date][code]++;
            }
        }

        // Find most common weather code for each day
        for (const date in dailyWeatherCodeMap) {
            let maxCount = 0;
            let mostCommonCode = 0;

            for (const code in dailyWeatherCodeMap[date]) {
                if (dailyWeatherCodeMap[date][code] > maxCount) {
                    maxCount = dailyWeatherCodeMap[date][code];
                    mostCommonCode = parseInt(code);
                }
            }

            dailyWeatherCodes.push(mostCommonCode);
        }

        return dailyWeatherCodes;
    }

    // Update all charts with new data
    function updateCharts(dailyData) {
        // Destroy existing charts if they exist
        if (temperatureChart) {
            if (typeof temperatureChart.destroy === 'function') {
                temperatureChart.destroy();
            } else {
                document.getElementById('temperatureChart').innerHTML = '';
            }
        }
        if (precipitationChart) {
            if (typeof precipitationChart.destroy === 'function') {
                precipitationChart.destroy();
            } else {
                document.getElementById('precipitationChart').innerHTML = '';
            }
        }
        if (windSpeedChart) {
            if (typeof windSpeedChart.destroy === 'function') {
                windSpeedChart.destroy();
            } else {
                document.getElementById('windSpeedChart').innerHTML = '';
            }
        }
        if (cloudCoverChart) {
            if (typeof cloudCoverChart.destroy === 'function') {
                cloudCoverChart.destroy();
            } else {
                document.getElementById('cloudCoverChart').innerHTML = '';
            }
        }
        if (humidityChart) {
            if (typeof humidityChart.destroy === 'function') {
                humidityChart.destroy();
            } else {
                document.getElementById('humidityChart').innerHTML = '';
            }
        }

        // Create new charts
        createTemperatureChart(dailyData);
        createPrecipitationChart(dailyData);
        createWindSpeedChart(dailyData);
        createCloudCoverChart(dailyData);
        createHumidityChart(dailyData);
    }

    // Show error message if charts cannot be loaded
    function showChartError(message) {
        document.getElementById('weatherInfo').innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i> ${message}`;
        document.getElementById('weatherInfo').classList.remove('alert-info');
        document.getElementById('weatherInfo').classList.add('alert-danger');
        document.getElementById('weatherInfo').classList.remove('d-none');

        // Clear any existing charts
        const chartContainers = document.querySelectorAll('.card-body div[id$="Chart"]');
        chartContainers.forEach(container => {
            container.innerHTML = `<div class="text-center text-muted p-5">No data available</div>`;
        });
    }

    // Create wind speed chart with candlestick visualization
    function createWindSpeedChart(dailyData) {
        // First destroy existing chart if it exists
        if (windSpeedChart) {
            if (typeof windSpeedChart.destroy === 'function') {
                windSpeedChart.destroy();
            } else {
                document.getElementById('windSpeedChart').innerHTML = '';
            }
        }

        // Process data into weekly segments
        const weeklyData = processWeeklyData(dailyData);

        // Prepare data for ApexCharts
        const candleData = weeklyData.map(week => ({
            x: `${new Date(week.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(week.endDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
            y: [
                parseFloat(week.wind.open.toFixed(1)),
                parseFloat(week.wind.high.toFixed(1)),
                parseFloat(week.wind.low === 9999 ? 0 : week.wind.low.toFixed(1)),
                parseFloat(week.wind.close.toFixed(1))
            ]
        }));

        // Find highest wind speed for annotation
        let maxWind = 0;
        let maxWindIndex = 0;

        for (let i = 0; i < weeklyData.length; i++) {
            if (weeklyData[i].wind.high > maxWind) {
                maxWind = weeklyData[i].wind.high;
                maxWindIndex = i;
            }
        }

        // ApexCharts configuration
        const options = {
            series: [{
                name: 'Wind Speed',
                data: candleData
            }],
            chart: {
                type: 'candlestick',
                height: 400,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                }
            },
            // Title removed to avoid overlap with card header
            tooltip: {
                enabled: true,
                custom: function({ seriesIndex, dataPointIndex, w }) {
                    const data = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
                    return `
                        <div class="apexcharts-tooltip-box">
                            <div class="apexcharts-tooltip-title">${data.x}</div>
                            <div>
                                <span class="apexcharts-tooltip-text">Start: ${data.y[0]}km/h</span><br>
                                <span class="apexcharts-tooltip-text" style="color: #ff0000;">Max: ${data.y[1]}km/h</span><br>
                                <span class="apexcharts-tooltip-text" style="color: #0000ff;">Min: ${data.y[2]}km/h</span><br>
                                <span class="apexcharts-tooltip-text">End: ${data.y[3]}km/h</span>
                            </div>
                        </div>
                    `;
                }
            },
            xaxis: {
                labels: {
                    rotate: 0,
                    style: {
                        fontSize: '12px'
                    }
                },
                title: {
                    text: 'Weekly Periods'
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return val.toFixed(1) + 'km/h';
                    }
                },
                title: {
                    text: 'Wind Speed (km/h)'
                }
            },
            plotOptions: {
                candlestick: {
                    colors: {
                        upward: '#2E7D32',  // dark green
                        downward: '#558B2F'  // lighter green
                    },
                    wick: {
                        useFillColor: true
                    }
                }
            },
            annotations: {
                points: [
                    {
                        x: candleData[maxWindIndex].x,
                        y: maxWind,
                        marker: {
                            size: 6,
                            fillColor: '#ff0000',
                            strokeColor: '#ffffff',
                            strokeWidth: 2
                        },
                        label: {
                            text: `Max: ${maxWind.toFixed(1)}km/h`,
                            offsetY: -15,
                            style: {
                                background: '#ff0000',
                                color: '#ffffff'
                            }
                        }
                    }
                ]
            }
        };

        windSpeedChart = new ApexCharts(document.getElementById('windSpeedChart'), options);
        windSpeedChart.render();
    }

    // Create cloud cover chart with candlestick visualization
    function createCloudCoverChart(dailyData) {
        // First destroy existing chart if it exists
        if (cloudCoverChart) {
            if (typeof cloudCoverChart.destroy === 'function') {
                cloudCoverChart.destroy();
            } else {
                document.getElementById('cloudCoverChart').innerHTML = '';
            }
        }

        // Process data into weekly segments
        const weeklyData = processWeeklyData(dailyData);

        // Prepare data for ApexCharts
        const candleData = weeklyData.map(week => ({
            x: `${new Date(week.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(week.endDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
            y: [
                parseFloat(week.cloud.open.toFixed(0)),
                parseFloat(week.cloud.high.toFixed(0)),
                parseFloat(week.cloud.low.toFixed(0)),
                parseFloat(week.cloud.close.toFixed(0))
            ]
        }));

        // Find highest and lowest cloud cover for annotations
        let maxCloud = 0;
        let minCloud = 100;
        let maxCloudIndex = 0;
        let minCloudIndex = 0;

        for (let i = 0; i < weeklyData.length; i++) {
            if (weeklyData[i].cloud.high > maxCloud) {
                maxCloud = weeklyData[i].cloud.high;
                maxCloudIndex = i;
            }
            if (weeklyData[i].cloud.low < minCloud) {
                minCloud = weeklyData[i].cloud.low;
                minCloudIndex = i;
            }
        }

        // ApexCharts configuration
        const options = {
            series: [{
                name: 'Cloud Cover',
                data: candleData
            }],
            chart: {
                type: 'candlestick',
                height: 400,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                }
            },
            // Title removed to avoid overlap with card header
            tooltip: {
                enabled: true,
                custom: function({ seriesIndex, dataPointIndex, w }) {
                    const data = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
                    return `
                        <div class="apexcharts-tooltip-box">
                            <div class="apexcharts-tooltip-title">${data.x}</div>
                            <div>
                                <span class="apexcharts-tooltip-text">Start: ${data.y[0]}%</span><br>
                                <span class="apexcharts-tooltip-text" style="color: #ff0000;">Max: ${data.y[1]}%</span><br>
                                <span class="apexcharts-tooltip-text" style="color: #0000ff;">Min: ${data.y[2]}%</span><br>
                                <span class="apexcharts-tooltip-text">End: ${data.y[3]}%</span>
                            </div>
                        </div>
                    `;
                }
            },
            xaxis: {
                labels: {
                    rotate: 0,
                    style: {
                        fontSize: '12px'
                    }
                },
                title: {
                    text: 'Weekly Periods'
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return val.toFixed(0) + '%';
                    }
                },
                title: {
                    text: 'Cloud Cover (%)'
                },
                min: 0,
                max: 100
            },
            plotOptions: {
                candlestick: {
                    colors: {
                        upward: '#FFA000',  // amber
                        downward: '#FFB300'  // lighter amber
                    },
                    wick: {
                        useFillColor: true
                    }
                }
            },
            annotations: {
                points: [
                    {
                        x: candleData[maxCloudIndex].x,
                        y: maxCloud,
                        marker: {
                            size: 6,
                            fillColor: '#ff0000',
                            strokeColor: '#ffffff',
                            strokeWidth: 2
                        },
                        label: {
                            text: `Max: ${maxCloud.toFixed(0)}%`,
                            offsetY: -15,
                            style: {
                                background: '#ff0000',
                                color: '#ffffff'
                            }
                        }
                    },
                    {
                        x: candleData[minCloudIndex].x,
                        y: minCloud,
                        marker: {
                            size: 6,
                            fillColor: '#0000ff',
                            strokeColor: '#ffffff',
                            strokeWidth: 2
                        },
                        label: {
                            text: `Min: ${minCloud.toFixed(0)}%`,
                            offsetY: 15,
                            style: {
                                background: '#0000ff',
                                color: '#ffffff'
                            }
                        }
                    }
                ]
            }
        };

        cloudCoverChart = new ApexCharts(document.getElementById('cloudCoverChart'), options);
        cloudCoverChart.render();
    }

    // Create humidity chart with candlestick visualization
    function createHumidityChart(dailyData) {
        // First destroy existing chart if it exists
        if (humidityChart) {
            if (typeof humidityChart.destroy === 'function') {
                humidityChart.destroy();
            } else {
                document.getElementById('humidityChart').innerHTML = '';
            }
        }

        // Process data into weekly segments
        const weeklyData = processWeeklyData(dailyData);

        // Prepare data for ApexCharts
        const candleData = weeklyData.map(week => ({
            x: `${new Date(week.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${new Date(week.endDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
            y: [
                parseFloat(week.humidity.open.toFixed(0)),
                parseFloat(week.humidity.high.toFixed(0)),
                parseFloat(week.humidity.low.toFixed(0)),
                parseFloat(week.humidity.close.toFixed(0))
            ]
        }));

        // Find highest and lowest humidity for annotations
        let maxHumidity = 0;
        let minHumidity = 100;
        let maxHumidityIndex = 0;
        let minHumidityIndex = 0;

        for (let i = 0; i < weeklyData.length; i++) {
            if (weeklyData[i].humidity.high > maxHumidity) {
                maxHumidity = weeklyData[i].humidity.high;
                maxHumidityIndex = i;
            }
            if (weeklyData[i].humidity.low < minHumidity) {
                minHumidity = weeklyData[i].humidity.low;
                minHumidityIndex = i;
            }
        }

        // ApexCharts configuration
        const options = {
            series: [{
                name: 'Humidity',
                data: candleData
            }],
            chart: {
                type: 'candlestick',
                height: 400,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: false,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    }
                }
            },
            // Title removed to avoid overlap with card header
            tooltip: {
                enabled: true,
                custom: function({ seriesIndex, dataPointIndex, w }) {
                    const data = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
                    return `
                        <div class="apexcharts-tooltip-box">
                            <div class="apexcharts-tooltip-title">${data.x}</div>
                            <div>
                                <span class="apexcharts-tooltip-text">Start: ${data.y[0]}%</span><br>
                                <span class="apexcharts-tooltip-text" style="color: #ff0000;">Max: ${data.y[1]}%</span><br>
                                <span class="apexcharts-tooltip-text" style="color: #0000ff;">Min: ${data.y[2]}%</span><br>
                                <span class="apexcharts-tooltip-text">End: ${data.y[3]}%</span>
                            </div>
                        </div>
                    `;
                }
            },
            xaxis: {
                labels: {
                    rotate: 0,
                    style: {
                        fontSize: '12px'
                    }
                },
                title: {
                    text: 'Weekly Periods'
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return val.toFixed(0) + '%';
                    }
                },
                title: {
                    text: 'Humidity (%)'
                },
                min: 0,
                max: 100
            },
            plotOptions: {
                candlestick: {
                    colors: {
                        upward: '#5E35B1',  // deep purple
                        downward: '#7E57C2'  // lighter purple
                    },
                    wick: {
                        useFillColor: true
                    }
                }
            },
            annotations: {
                points: [
                    {
                        x: candleData[maxHumidityIndex].x,
                        y: maxHumidity,
                        marker: {
                            size: 6,
                            fillColor: '#ff0000',
                            strokeColor: '#ffffff',
                            strokeWidth: 2
                        },
                        label: {
                            text: `Max: ${maxHumidity.toFixed(0)}%`,
                            offsetY: -15,
                            style: {
                                background: '#ff0000',
                                color: '#ffffff'
                            }
                        }
                    },
                    {
                        x: candleData[minHumidityIndex].x,
                        y: minHumidity,
                        marker: {
                            size: 6,
                            fillColor: '#0000ff',
                            strokeColor: '#ffffff',
                            strokeWidth: 2
                        },
                        label: {
                            text: `Min: ${minHumidity.toFixed(0)}%`,
                            offsetY: 15,
                            style: {
                                background: '#0000ff',
                                color: '#ffffff'
                            }
                        }
                    }
                ]
            }
        };

        humidityChart = new ApexCharts(document.getElementById('humidityChart'), options);
        humidityChart.render();
    }

    // Process only hourly data to daily format
    function processOnlyHourlyData(hourlyData) {
        const dailyData = {
            time: [],
            temperature_2m_max: [],
            temperature_2m_min: [],
            precipitation_sum: [],
            windspeed_10m_max: [],
            cloudcover_mean: [],
            relativehumidity_2m_mean: [],
            weather_code: []
        };

        // Group hourly data by day
        const dailyMap = {};

        for (let i = 0; i < hourlyData.time.length; i++) {
            const date = hourlyData.time[i].split('T')[0];
            if (!dailyMap[date]) {
                dailyMap[date] = {
                    temps: [],
                    precip: 0,
                    windspeed: [],
                    cloudcover: [],
                    humidity: [],
                    weatherCodes: {}
                };
            }

            // Temperature
            if (hourlyData.temperature_2m && hourlyData.temperature_2m[i] !== null) {
                dailyMap[date].temps.push(hourlyData.temperature_2m[i]);
            }

            // Precipitation (sum)
            if (hourlyData.precipitation && hourlyData.precipitation[i] !== null) {
                dailyMap[date].precip += hourlyData.precipitation[i];
            }

            // Wind speed
            if (hourlyData.windspeed_10m && hourlyData.windspeed_10m[i] !== null) {
                dailyMap[date].windspeed.push(hourlyData.windspeed_10m[i]);
            }

            // Cloud cover
            if (hourlyData.cloudcover && hourlyData.cloudcover[i] !== null) {
                dailyMap[date].cloudcover.push(hourlyData.cloudcover[i]);
            }

            // Humidity
            if (hourlyData.relative_humidity_2m && hourlyData.relative_humidity_2m[i] !== null) {
                dailyMap[date].humidity.push(hourlyData.relative_humidity_2m[i]);
            }

            // Weather code
            if (hourlyData.weather_code && hourlyData.weather_code[i] !== null) {
                const code = hourlyData.weather_code[i];
                if (!dailyMap[date].weatherCodes[code]) {
                    dailyMap[date].weatherCodes[code] = 0;
                }
                dailyMap[date].weatherCodes[code]++;
            }
        }

        // Process daily data
        Object.keys(dailyMap).sort().forEach(date => {
            const dayData = dailyMap[date];

            dailyData.time.push(date);

            // Temperature max/min
            if (dayData.temps.length > 0) {
                dailyData.temperature_2m_max.push(Math.max(...dayData.temps));
                dailyData.temperature_2m_min.push(Math.min(...dayData.temps));
            } else {
                dailyData.temperature_2m_max.push(null);
                dailyData.temperature_2m_min.push(null);
            }

            // Precipitation sum
            dailyData.precipitation_sum.push(dayData.precip);

            // Wind speed max
            if (dayData.windspeed.length > 0) {
                dailyData.windspeed_10m_max.push(Math.max(...dayData.windspeed));
            } else {
                dailyData.windspeed_10m_max.push(null);
            }

            // Cloud cover mean
            if (dayData.cloudcover.length > 0) {
                const sum = dayData.cloudcover.reduce((a, b) => a + b, 0);
                dailyData.cloudcover_mean.push(sum / dayData.cloudcover.length);
            } else {
                dailyData.cloudcover_mean.push(null);
            }

            // Humidity mean
            if (dayData.humidity.length > 0) {
                const sum = dayData.humidity.reduce((a, b) => a + b, 0);
                dailyData.relativehumidity_2m_mean.push(sum / dayData.humidity.length);
            } else {
                dailyData.relativehumidity_2m_mean.push(null);
            }

            // Most common weather code
            let maxCount = 0;
            let mostCommonCode = 0;
            for (const code in dayData.weatherCodes) {
                if (dayData.weatherCodes[code] > maxCount) {
                    maxCount = dayData.weatherCodes[code];
                    mostCommonCode = parseInt(code);
                }
            }
            dailyData.weather_code.push(mostCommonCode);
        });

        return dailyData;
    }

    // Populate the weather data table
    function populateWeatherTable(dailyData) {
        const tableBody = document.querySelector('#weatherTable tbody');
        tableBody.innerHTML = ''; // Clear previous data

        // Group data by month
        const monthlyData = {};

        for (let i = 0; i < dailyData.time.length; i++) {
            const date = new Date(dailyData.time[i]);
            // Create a month key that includes the year and month in a format that can be sorted
            // Format: YYYY-MM Month Name Year (e.g., "2024-05 May 2024")
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // JavaScript months are 0-based
            const monthName = date.toLocaleDateString('en-US', { month: 'long' });
            const monthKey = `${year}-${month.toString().padStart(2, '0')} ${monthName} ${year}`;

            if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = {
                    temps: [],
                    precip: [],
                    wind: [],
                    cloud: [],
                    humidity: [],
                    highTemp: -100,
                    lowTemp: 100,
                    highPrecip: 0,
                    highWind: 0,
                    lowCloud: 100,
                    highCloud: 0,
                    lowHumidity: 100,
                    highHumidity: 0,
                    // Store the display name without the sorting prefix
                    displayName: `${monthName} ${year}`
                };
            }

            // Temperature
            if (dailyData.temperature_2m_max[i] !== null && dailyData.temperature_2m_min[i] !== null) {
                const avgTemp = (dailyData.temperature_2m_max[i] + dailyData.temperature_2m_min[i]) / 2;
                monthlyData[monthKey].temps.push(avgTemp);
                monthlyData[monthKey].highTemp = Math.max(monthlyData[monthKey].highTemp, dailyData.temperature_2m_max[i]);
                monthlyData[monthKey].lowTemp = Math.min(monthlyData[monthKey].lowTemp, dailyData.temperature_2m_min[i]);
            }

            // Precipitation
            if (dailyData.precipitation_sum[i] !== null) {
                monthlyData[monthKey].precip.push(dailyData.precipitation_sum[i]);
                monthlyData[monthKey].highPrecip = Math.max(monthlyData[monthKey].highPrecip, dailyData.precipitation_sum[i]);
            }

            // Wind speed
            if (dailyData.windspeed_10m_max[i] !== null) {
                monthlyData[monthKey].wind.push(dailyData.windspeed_10m_max[i]);
                monthlyData[monthKey].highWind = Math.max(monthlyData[monthKey].highWind, dailyData.windspeed_10m_max[i]);
            }

            // Cloud cover
            if (dailyData.cloudcover_mean[i] !== null) {
                monthlyData[monthKey].cloud.push(dailyData.cloudcover_mean[i]);
                monthlyData[monthKey].lowCloud = Math.min(monthlyData[monthKey].lowCloud, dailyData.cloudcover_mean[i]);
                monthlyData[monthKey].highCloud = Math.max(monthlyData[monthKey].highCloud, dailyData.cloudcover_mean[i]);
            }

            // Humidity
            if (dailyData.relativehumidity_2m_mean[i] !== null) {
                monthlyData[monthKey].humidity.push(dailyData.relativehumidity_2m_mean[i]);
                monthlyData[monthKey].lowHumidity = Math.min(monthlyData[monthKey].lowHumidity, dailyData.relativehumidity_2m_mean[i]);
                monthlyData[monthKey].highHumidity = Math.max(monthlyData[monthKey].highHumidity, dailyData.relativehumidity_2m_mean[i]);
            }
        }

        // Calculate averages and populate table
        // Sort months in reverse chronological order (newest to oldest)
        Object.keys(monthlyData).sort((a, b) => {
            // The keys are already in a sortable format (YYYY-MM Month Year)
            // Just compare them in reverse order for newest first
            return b.localeCompare(a);
        }).forEach(month => {
            const data = monthlyData[month];

            // Calculate averages
            const avgTemp = data.temps.length > 0 ? data.temps.reduce((a, b) => a + b, 0) / data.temps.length : 0;
            const totalPrecip = data.precip.length > 0 ? data.precip.reduce((a, b) => a + b, 0) : 0;
            const avgWindSpeed = data.wind.length > 0 ? data.wind.reduce((a, b) => a + b, 0) / data.wind.length : 0;
            const avgCloudCover = data.cloud.length > 0 ? data.cloud.reduce((a, b) => a + b, 0) / data.cloud.length : 0;
            const avgHumidity = data.humidity.length > 0 ? data.humidity.reduce((a, b) => a + b, 0) / data.humidity.length : 0;

            // Create table row
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${data.displayName}</td>
                <td>${avgTemp.toFixed(1)}°C<br><small>(High: ${data.highTemp.toFixed(1)}°C, Low: ${data.lowTemp.toFixed(1)}°C)</small></td>
                <td>${totalPrecip.toFixed(1)} mm<br><small>(High: ${data.highPrecip.toFixed(1)} mm)</small></td>
                <td>${avgWindSpeed.toFixed(1)} km/h<br><small>(High: ${data.highWind.toFixed(1)} km/h)</small></td>
                <td>${avgCloudCover.toFixed(0)}%<br><small>(Range: ${data.lowCloud.toFixed(0)}-${data.highCloud.toFixed(0)}%)</small></td>
                <td>${avgHumidity.toFixed(0)}%<br><small>(Range: ${data.lowHumidity.toFixed(0)}-${data.highHumidity.toFixed(0)}%)</small></td>
            `;
            tableBody.appendChild(row);
        });

        // Populate summary table
        populateSummaryTable(dailyData);

        // Populate extreme events table
        populateExtremeEventsTable(dailyData);
    }

    // Populate the climate elements summary table
    function populateSummaryTable(dailyData) {
        const tableBody = document.querySelector('#summaryTable tbody');
        if (!tableBody) return; // Table might not exist

        tableBody.innerHTML = ''; // Clear previous data

        // Calculate overall statistics
        const stats = {
            temperature: { values: [], high: -100, low: 100, highDate: '', lowDate: '' },
            precipitation: { values: [], high: 0, low: 0, highDate: '', lowDate: '' },
            windspeed: { values: [], high: 0, low: 9999, highDate: '', lowDate: '' },
            cloudcover: { values: [], high: 0, low: 100, highDate: '', lowDate: '' },
            humidity: { values: [], high: 0, low: 100, highDate: '', lowDate: '' }
        };

        // Process data
        for (let i = 0; i < dailyData.time.length; i++) {
            const date = dailyData.time[i];

            // Temperature (average of max and min)
            if (dailyData.temperature_2m_max[i] !== null && dailyData.temperature_2m_min[i] !== null) {
                const avgTemp = (dailyData.temperature_2m_max[i] + dailyData.temperature_2m_min[i]) / 2;
                stats.temperature.values.push(avgTemp);

                // Track max temperature
                if (dailyData.temperature_2m_max[i] > stats.temperature.high) {
                    stats.temperature.high = dailyData.temperature_2m_max[i];
                    stats.temperature.highDate = date;
                }

                // Track min temperature
                if (dailyData.temperature_2m_min[i] < stats.temperature.low) {
                    stats.temperature.low = dailyData.temperature_2m_min[i];
                    stats.temperature.lowDate = date;
                }
            }

            // Precipitation
            if (dailyData.precipitation_sum[i] !== null) {
                stats.precipitation.values.push(dailyData.precipitation_sum[i]);

                // Track max precipitation
                if (dailyData.precipitation_sum[i] > stats.precipitation.high) {
                    stats.precipitation.high = dailyData.precipitation_sum[i];
                    stats.precipitation.highDate = date;
                }
            }

            // Wind speed
            if (dailyData.windspeed_10m_max[i] !== null) {
                stats.windspeed.values.push(dailyData.windspeed_10m_max[i]);

                // Track max wind speed
                if (dailyData.windspeed_10m_max[i] > stats.windspeed.high) {
                    stats.windspeed.high = dailyData.windspeed_10m_max[i];
                    stats.windspeed.highDate = date;
                }

                // Track min wind speed
                if (dailyData.windspeed_10m_max[i] < stats.windspeed.low) {
                    stats.windspeed.low = dailyData.windspeed_10m_max[i];
                    stats.windspeed.lowDate = date;
                }
            }

            // Cloud cover
            if (dailyData.cloudcover_mean[i] !== null) {
                stats.cloudcover.values.push(dailyData.cloudcover_mean[i]);

                // Track max cloud cover
                if (dailyData.cloudcover_mean[i] > stats.cloudcover.high) {
                    stats.cloudcover.high = dailyData.cloudcover_mean[i];
                    stats.cloudcover.highDate = date;
                }

                // Track min cloud cover
                if (dailyData.cloudcover_mean[i] < stats.cloudcover.low) {
                    stats.cloudcover.low = dailyData.cloudcover_mean[i];
                    stats.cloudcover.lowDate = date;
                }
            }

            // Humidity
            if (dailyData.relativehumidity_2m_mean[i] !== null) {
                stats.humidity.values.push(dailyData.relativehumidity_2m_mean[i]);

                // Track max humidity
                if (dailyData.relativehumidity_2m_mean[i] > stats.humidity.high) {
                    stats.humidity.high = dailyData.relativehumidity_2m_mean[i];
                    stats.humidity.highDate = date;
                }

                // Track min humidity
                if (dailyData.relativehumidity_2m_mean[i] < stats.humidity.low) {
                    stats.humidity.low = dailyData.relativehumidity_2m_mean[i];
                    stats.humidity.lowDate = date;
                }
            }
        }

        // Calculate averages
        const avgTemp = stats.temperature.values.length > 0 ?
            stats.temperature.values.reduce((a, b) => a + b, 0) / stats.temperature.values.length : 0;

        const avgPrecip = stats.precipitation.values.length > 0 ?
            stats.precipitation.values.reduce((a, b) => a + b, 0) / stats.precipitation.values.length : 0;

        const avgWind = stats.windspeed.values.length > 0 ?
            stats.windspeed.values.reduce((a, b) => a + b, 0) / stats.windspeed.values.length : 0;

        const avgCloud = stats.cloudcover.values.length > 0 ?
            stats.cloudcover.values.reduce((a, b) => a + b, 0) / stats.cloudcover.values.length : 0;

        const avgHumidity = stats.humidity.values.length > 0 ?
            stats.humidity.values.reduce((a, b) => a + b, 0) / stats.humidity.values.length : 0;

        // Create table rows
        const rows = [
            {
                element: 'Temperature',
                avg: `${avgTemp.toFixed(1)}°C`,
                high: `${stats.temperature.high.toFixed(1)}°C`,
                low: `${stats.temperature.low.toFixed(1)}°C`
            },
            {
                element: 'Precipitation',
                avg: `${avgPrecip.toFixed(1)} mm/day`,
                high: `${stats.precipitation.high.toFixed(1)} mm`,
                low: '0.0 mm'
            },
            {
                element: 'Wind Speed',
                avg: `${avgWind.toFixed(1)} km/h`,
                high: `${stats.windspeed.high.toFixed(1)} km/h`,
                low: `${stats.windspeed.low.toFixed(1)} km/h`
            },
            {
                element: 'Cloud Cover',
                avg: `${avgCloud.toFixed(0)}%`,
                high: `${stats.cloudcover.high.toFixed(0)}%`,
                low: `${stats.cloudcover.low.toFixed(0)}%`
            },
            {
                element: 'Humidity',
                avg: `${avgHumidity.toFixed(0)}%`,
                high: `${stats.humidity.high.toFixed(0)}%`,
                low: `${stats.humidity.low.toFixed(0)}%`
            }
        ];

        // Add rows to table
        rows.forEach(row => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${row.element}</td>
                <td>${row.avg}</td>
                <td>${row.high}</td>
                <td>${row.low}</td>
            `;
            tableBody.appendChild(tr);
        });
    }

    // Populate the extreme weather events table
    function populateExtremeEventsTable(dailyData) {
        const tableBody = document.querySelector('#extremeEventsTable tbody');
        if (!tableBody) return; // Table might not exist

        tableBody.innerHTML = ''; // Clear previous data

        // Calculate extreme events
        const extremes = {
            temperature: {
                highest: -100, highestDate: '',
                lowest: 100, lowestDate: ''
            },
            precipitation: {
                highest: 0, highestDate: '',
                lowest: 0, lowestDate: ''
            },
            windspeed: {
                highest: 0, highestDate: '',
                lowest: 9999, lowestDate: ''
            },
            cloudcover: {
                highest: 0, highestDate: '',
                lowest: 100, lowestDate: ''
            },
            humidity: {
                highest: 0, highestDate: '',
                lowest: 100, lowestDate: ''
            }
        };

        // Process data
        for (let i = 0; i < dailyData.time.length; i++) {
            const date = new Date(dailyData.time[i]).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });

            // Temperature extremes
            if (dailyData.temperature_2m_max[i] !== null && dailyData.temperature_2m_max[i] > extremes.temperature.highest) {
                extremes.temperature.highest = dailyData.temperature_2m_max[i];
                extremes.temperature.highestDate = date;
            }

            if (dailyData.temperature_2m_min[i] !== null && dailyData.temperature_2m_min[i] < extremes.temperature.lowest) {
                extremes.temperature.lowest = dailyData.temperature_2m_min[i];
                extremes.temperature.lowestDate = date;
            }

            // Precipitation extremes
            if (dailyData.precipitation_sum[i] !== null && dailyData.precipitation_sum[i] > extremes.precipitation.highest) {
                extremes.precipitation.highest = dailyData.precipitation_sum[i];
                extremes.precipitation.highestDate = date;
            }

            // Wind speed extremes
            if (dailyData.windspeed_10m_max[i] !== null && dailyData.windspeed_10m_max[i] > extremes.windspeed.highest) {
                extremes.windspeed.highest = dailyData.windspeed_10m_max[i];
                extremes.windspeed.highestDate = date;
            }

            if (dailyData.windspeed_10m_max[i] !== null && dailyData.windspeed_10m_max[i] < extremes.windspeed.lowest) {
                extremes.windspeed.lowest = dailyData.windspeed_10m_max[i];
                extremes.windspeed.lowestDate = date;
            }

            // Cloud cover extremes
            if (dailyData.cloudcover_mean[i] !== null && dailyData.cloudcover_mean[i] > extremes.cloudcover.highest) {
                extremes.cloudcover.highest = dailyData.cloudcover_mean[i];
                extremes.cloudcover.highestDate = date;
            }

            if (dailyData.cloudcover_mean[i] !== null && dailyData.cloudcover_mean[i] < extremes.cloudcover.lowest) {
                extremes.cloudcover.lowest = dailyData.cloudcover_mean[i];
                extremes.cloudcover.lowestDate = date;
            }

            // Humidity extremes
            if (dailyData.relativehumidity_2m_mean[i] !== null && dailyData.relativehumidity_2m_mean[i] > extremes.humidity.highest) {
                extremes.humidity.highest = dailyData.relativehumidity_2m_mean[i];
                extremes.humidity.highestDate = date;
            }

            if (dailyData.relativehumidity_2m_mean[i] !== null && dailyData.relativehumidity_2m_mean[i] < extremes.humidity.lowest) {
                extremes.humidity.lowest = dailyData.relativehumidity_2m_mean[i];
                extremes.humidity.lowestDate = date;
            }
        }

        // Create table rows
        const rows = [
            {
                element: 'Temperature',
                highest: `${extremes.temperature.highest.toFixed(1)}°C`,
                highestDate: extremes.temperature.highestDate,
                lowest: `${extremes.temperature.lowest.toFixed(1)}°C`,
                lowestDate: extremes.temperature.lowestDate
            },
            {
                element: 'Precipitation',
                highest: `${extremes.precipitation.highest.toFixed(1)} mm`,
                highestDate: extremes.precipitation.highestDate,
                lowest: '0.0 mm',
                lowestDate: 'Multiple days'
            },
            {
                element: 'Wind Speed',
                highest: `${extremes.windspeed.highest.toFixed(1)} km/h`,
                highestDate: extremes.windspeed.highestDate,
                lowest: `${extremes.windspeed.lowest.toFixed(1)} km/h`,
                lowestDate: extremes.windspeed.lowestDate
            },
            {
                element: 'Cloud Cover',
                highest: `${extremes.cloudcover.highest.toFixed(0)}%`,
                highestDate: extremes.cloudcover.highestDate,
                lowest: `${extremes.cloudcover.lowest.toFixed(0)}%`,
                lowestDate: extremes.cloudcover.lowestDate
            },
            {
                element: 'Humidity',
                highest: `${extremes.humidity.highest.toFixed(0)}%`,
                highestDate: extremes.humidity.highestDate,
                lowest: `${extremes.humidity.lowest.toFixed(0)}%`,
                lowestDate: extremes.humidity.lowestDate
            }
        ];

        // Add rows to table
        rows.forEach(row => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${row.element}</td>
                <td>${row.highest}</td>
                <td>${row.highestDate}</td>
                <td>${row.lowest}</td>
                <td>${row.lowestDate}</td>
            `;
            tableBody.appendChild(tr);
        });
    }

    // Initialize map when page loads
    document.addEventListener('DOMContentLoaded', initMap);
</script>
<?= $this->endSection() ?>
