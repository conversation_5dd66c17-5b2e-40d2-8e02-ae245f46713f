<?php

if (!function_exists('get_weather_data')) {
    /**
     * Fetch and process weather data for a specific location and date
     * 
     * @param int $block_id Farm block ID
     * @param float $longitude Location longitude
     * @param float $latitude Location latitude
     * @param string $date Date in Y-m-d format
     * @return array|null Processed weather data or null on failure
     */
    function get_weather_data($block_id, $longitude, $latitude, $date)
    {
        try {
            // Validate inputs
            if (!is_numeric($block_id) || $block_id <= 0) {
                log_message('error', 'Invalid block_id provided: ' . $block_id);
                return null;
            }

            if (!is_numeric($longitude) || !is_numeric($latitude)) {
                log_message('error', 'Invalid coordinates provided: lat=' . $latitude . ', lon=' . $longitude);
                return null;
            }

            if (!strtotime($date)) {
                log_message('error', 'Invalid date provided: ' . $date);
                return null;
            }

            // Construct API URL
            $url = "https://archive-api.open-meteo.com/v1/archive?" . http_build_query([
                'latitude' => $latitude,
                'longitude' => $longitude,
                'start_date' => $date,
                'end_date' => $date,
                'hourly' => 'temperature_2m,precipitation,relative_humidity_2m,windspeed_10m,shortwave_radiation,pressure_msl,cloudcover',
                'timezone' => 'auto'
            ]);

            // Fetch data from API
            $response = @file_get_contents($url);
            if ($response === false) {
                log_message('error', 'Failed to fetch weather data from API');
                return null;
            }

            $data = json_decode($response, true);
            if (!isset($data['hourly'])) {
                log_message('error', 'Invalid API response structure');
                return null;
            }

            // Calculate daily averages
            $hourly = $data['hourly'];
            $weather_data = [
                'block_id' => $block_id,
                'temperature' => calculate_average($hourly['temperature_2m']),
                'total_precipitation' => array_sum($hourly['precipitation']), // Total daily precipitation
                'humidity' => calculate_average($hourly['relative_humidity_2m']),
                'wind_speed' => calculate_average($hourly['windspeed_10m']),
                'solar_radiation' => calculate_average($hourly['shortwave_radiation']),
                'pressure' => calculate_average($hourly['pressure_msl']),
                'cloud_cover' => calculate_average($hourly['cloudcover']),
                'recorded_date' => $date,
                'remarks' => 'Weather data fetched from Open-Meteo API',
                'created_by' => session()->get('emp_id') ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            return $weather_data;

        } catch (\Exception $e) {
            log_message('error', 'Weather data fetch error: ' . $e->getMessage());
            return null;
        }
    }
}

if (!function_exists('calculate_average')) {
    /**
     * Calculate average of an array of numbers
     * 
     * @param array $values Array of numeric values
     * @return float Average value
     */
    function calculate_average($values)
    {
        if (empty($values)) {
            return 0;
        }
        return array_sum($values) / count($values);
    }
}

if (!function_exists('save_weather_data')) {
    /**
     * Save weather data to database
     * 
     * @param int $block_id Farm block ID
     * @param float $longitude Block longitude
     * @param float $latitude Block latitude
     * @param string $date Date to fetch weather for
     * @return bool Success status
     */
    function save_weather_data($block_id, $longitude, $latitude, $date)
    {
        try {
            // Get weather data
            $weather_data = get_weather_data($block_id, $longitude, $latitude, $date);
            if (!$weather_data) {
                return false;
            }

            // Get database instance
            $db = \Config\Database::connect();
            $builder = $db->table('farm_weather_data');

            // Check if weather data already exists for this block and date
            $existing = $builder->where([
                'block_id' => $block_id,
                'recorded_date' => $date
            ])->get()->getRow();

            if ($existing) {
                // Update existing record
                $weather_data['updated_by'] = session()->get('emp_id');
                $weather_data['updated_at'] = date('Y-m-d H:i:s');
                return $builder->where('id', $existing->id)->update($weather_data);
            } else {
                // Insert new record
                return $builder->insert($weather_data);
            }

        } catch (\Exception $e) {
            log_message('error', 'Weather data save error: ' . $e->getMessage());
            return false;
        }
    }
} 