<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\LocationsModel;
use App\Models\CropsModel;
use App\Models\GroupingsModel;
use App\Models\DatasetsModel;

class Staff extends BaseController
{
    protected $session;
    protected $locationsModel;
    protected $cropsModel;
    protected $groupingsModel;
    protected $datasetsModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        
        // Initialize models
        $this->locationsModel = new LocationsModel();
        $this->cropsModel = new CropsModel();
        $this->groupingsModel = new GroupingsModel();
        $this->datasetsModel = new DatasetsModel();
        
        // Verify staff role
        if (session()->get('role') != "user") {
            echo 'Access denied';
            exit;
        }
    }

    public function index()
    {
        $data = [
            'title' => 'Staff Dashboard',
            'page_header' => 'Dashboard',
            'page_desc' => 'Staff Control Panel',
            'page_icon' => 'fa-tachometer-alt'
        ];
        return view('staff/dashboard', $data);
    }

    // ... rest of the methods remain the same ...
} 