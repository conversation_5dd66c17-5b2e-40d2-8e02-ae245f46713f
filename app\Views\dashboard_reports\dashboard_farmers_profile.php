<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1><?= $page_title ?></h1>
            </div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="<?= base_url('farmers/dashboard') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3">
                <!-- Profile Image -->
                <div class="card card-primary card-outline">
                    <div class="card-body box-profile">
                        <div class="text-center">
                            <?php if (!empty($farmer['id_photo'])): ?>
                                <img class="profile-user-img img-fluid img-circle"
                                    src="<?= imgcheck($farmer['id_photo']) ?>"
                                    alt="Farmer ID Photo">
                            <?php else: ?>
                                <img class="profile-user-img img-fluid img-circle"
                                    src="<?= imgcheck($farmer['id_photo']) ?>"
                                    alt="Default Profile">
                            <?php endif; ?>
                        </div>
                        <h3 class="profile-username text-center"><?= $farmer['given_name'] . ' ' . $farmer['surname'] ?></h3>
                        <p class="text-muted text-center"><?= $farmer['farmer_code'] ?></p>
                        <ul class="list-group list-group-unbordered mb-3">
                            <li class="list-group-item">
                                <b>Crop Blocks</b> <a class="float-right"><?= $farmer['crops_blocks_count'] ?? 0 ?></a>
                            </li>
                            <li class="list-group-item">
                                <b>Total Hectares</b> <a class="float-right"><?= number_format($farmer['total_hectares'] ?? 0, 2) ?></a>
                            </li>
                            <li class="list-group-item">
                                <b>Crops Revenue</b> <a class="float-right"><?= COUNTRY_CURRENCY ?> <?= number_format($farmer['total_crops_revenue'] ?? 0, 2) ?></a>
                            </li>
                            <li class="list-group-item">
                                <b>Livestock Blocks</b> <a class="float-right"><?= $farmer['livestock_blocks_count'] ?? 0 ?></a>
                            </li>
                            <li class="list-group-item">
                                <b>Livestock Value</b> <a class="float-right"><?= COUNTRY_CURRENCY ?> <?= number_format($farmer['total_livestock_value'] ?? 0, 2) ?></a>
                            </li>
                            <li class="list-group-item">
                                <b>Total Value</b> <a class="float-right"><?= COUNTRY_CURRENCY ?> <?= number_format(($farmer['total_crops_revenue'] ?? 0) + ($farmer['total_livestock_value'] ?? 0), 2) ?></a>
                            </li>
                        </ul>
                        <span class="badge badge-<?= $farmer['status'] === 'active' ? 'success' : 'danger' ?> w-100 py-2">
                            <?= ucfirst($farmer['status']) ?>
                        </span>
                    </div>
                </div>

                <!-- Location Box -->
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">Location Information</h3>
                    </div>
                    <div class="card-body">
                        <strong><i class="fas fa-map-marker-alt mr-1"></i> Country</strong>
                        <p class="text-muted"><?= $farmer['country_name'] ?? 'Not Specified' ?></p>
                        <hr>
                        <strong><i class="fas fa-map mr-1"></i> Province</strong>
                        <p class="text-muted"><?= $farmer['province_name'] ?></p>
                        <hr>
                        <strong><i class="fas fa-map-signs mr-1"></i> District</strong>
                        <p class="text-muted"><?= $farmer['district_name'] ?></p>
                        <hr>
                        <strong><i class="fas fa-building mr-1"></i> LLG</strong>
                        <p class="text-muted"><?= $farmer['llg_name'] ?></p>
                        <hr>
                        <strong><i class="fas fa-home mr-1"></i> Ward</strong>
                        <p class="text-muted"><?= $farmer['ward_name'] ?></p>
                        <hr>
                        <strong><i class="fas fa-map-pin mr-1"></i> Village</strong>
                        <p class="text-muted mb-0"><?= $farmer['village'] ?></p>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <!-- Personal Information -->
                <div class="card">
                    <div class="card-header p-2">
                        <h3 class="card-title">Personal Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong><i class="fas fa-user mr-1"></i> Personal Details</strong>
                                <div class="table-responsive">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="150">Gender</th>
                                            <td><?= $farmer['gender'] ?></td>
                                        </tr>
                                        <tr>
                                            <th>Date of Birth</th>
                                            <td><?= dateforms($farmer['date_of_birth']) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Age</th>
                                            <td><?= getAge($farmer['date_of_birth']) ?></td>
                                        </tr>
                                        <tr>
                                            <th>Marital Status</th>
                                            <td><?= ucfirst($farmer['marital_status'] ?? 'Not Specified') ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <strong><i class="fas fa-book mr-1"></i> Contact & Education</strong>
                                <div class="table-responsive">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="150">Phone</th>
                                            <td><?= $farmer['phone'] ?? 'Not Specified' ?></td>
                                        </tr>
                                        <tr>
                                            <th>Email</th>
                                            <td><?= $farmer['email'] ?? 'Not Specified' ?></td>
                                        </tr>
                                        <tr>
                                            <th>Education Level</th>
                                            <td><?= $farmer['highest_education'] ?? 'Not Specified' ?></td>
                                        </tr>
                                        <tr>
                                            <th>Course Taken</th>
                                            <td><?= $farmer['course_taken'] ?? 'Not Specified' ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Children Information -->
                <?php if (!empty($children)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Children Information</h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Gender</th>
                                            <th>Date of Birth</th>
                                            <th>Age</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($children as $child): ?>
                                            <tr>
                                                <td><?= $child['name'] ?></td>
                                                <td><?= $child['gender'] ?></td>
                                                <td><?= dateforms($child['date_of_birth']) ?></td>
                                                <td><?= getAge($child['date_of_birth']) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Farming Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Crop Distribution</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="cropTypesChart" style="min-height: 250px;"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Livestock Distribution</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="livestockTypesChart" style="min-height: 250px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Crop Blocks Details -->
                <?php if (!empty($crop_blocks)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Crop Blocks Details</h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped text-nowrap" id="cropBlocksTable">
                                    <thead>
                                        <tr>
                                            <th>Block Code</th>
                                            <th>Crop</th>
                                            <th>Hectares</th>
                                            <th>Location</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($crop_blocks as $block): ?>
                                            <tr>
                                                <td><?= $block['block_code'] ?></td>
                                                <td>
                                                    <span class="badge" style="background-color: <?= $block['crop_color_code'] ?>">
                                                        <?= $block['crop_name'] ?>
                                                    </span>
                                                </td>
                                                <td><?= number_format($block['hectares'], 2) ?></td>
                                                <td>
                                                    <?= $block['village'] ?>, <?= $block['ward_name'] ?> Ward,
                                                    <?= $block['llg_name'] ?> LLG, <?= $block['district_name'] ?> District,
                                                    <?= $block['province_name'] ?> Province
                                                    <?= !empty($block['block_site']) ? ' (' . $block['block_site'] . ')' : '' ?>
                                                </td>
                                                <td><?= ucfirst($block['status']) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Livestock Blocks Details -->
                <?php if (!empty($livestock_blocks)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Livestock Blocks Details</h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped text-nowrap" id="livestockBlocksTable">
                                    <thead>
                                        <tr>
                                            <th>Block Code</th>
                                            <th>Livestock</th>
                                            <th>Quantity</th>
                                            <th>Cost per Unit (<?= COUNTRY_CURRENCY ?>)</th>
                                            <th>Total Value (<?= COUNTRY_CURRENCY ?>)</th>
                                            <th>Location</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($livestock_blocks as $block): ?>
                                            <tr>
                                                <td><?= $block['block_code'] ?></td>
                                                <td>
                                                    <span class="badge" style="background-color: <?= $block['livestock_color_code'] ?>">
                                                        <?= $block['livestock_name'] ?>
                                                    </span>
                                                </td>
                                                <td><?= $block['quantity'] ?></td>
                                                <td><?= number_format($block['cost_per_livestock'], 2) ?></td>
                                                <td><?= number_format($block['quantity'] * $block['cost_per_livestock'], 2) ?></td>
                                                <td>
                                                    <?= $block['village'] ?>, <?= $block['ward_name'] ?> Ward,
                                                    <?= $block['llg_name'] ?> LLG, <?= $block['district_name'] ?> District,
                                                    <?= $block['province_name'] ?> Province
                                                    <?= !empty($block['block_site']) ? ' (' . $block['block_site'] . ')' : '' ?>
                                                </td>
                                                <td><?= ucfirst($block['status']) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Market Activities -->
                <?php if (!empty($market_activities)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Market Activities</h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped text-nowrap" id="marketActivitiesTable">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Crop</th>
                                            <th>Item</th>
                                            <th>Market Stage</th>
                                            <th>Item Type</th>
                                            <th>Description</th>
                                            <th>Unit</th>
                                            <th>Quantity</th>
                                            <th>Price/Unit (<?= COUNTRY_CURRENCY ?>)</th>
                                            <th>Total (<?= COUNTRY_CURRENCY ?>)</th>
                                            <th>Location</th>
                                            <th>Buyer</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($market_activities as $activity): ?>
                                            <tr>
                                                <td><?= dateforms($activity['market_date']) ?></td>
                                                <td>
                                                    <span class="badge" style="background-color: <?= $activity['crop_color_code'] ?>">
                                                        <?= $activity['crop_name'] ?>
                                                    </span>
                                                </td>
                                                <td><?= $activity['item'] ?></td>
                                                <td><?= $activity['market_stage'] ?></td>
                                                <td><?= $activity['item_type'] ?></td>
                                                <td><?= $activity['description'] ?></td>
                                                <td><?= $activity['unit_of_measure'] ?></td>
                                                <td><?= number_format($activity['quantity'], 2) ?></td>
                                                <td><?= number_format($activity['market_price_per_unit'], 2) ?></td>
                                                <td><?= number_format($activity['quantity'] * $activity['market_price_per_unit'], 2) ?></td>
                                                <td><?= $activity['selling_location'] ?></td>
                                                <td><?= $activity['buyer_name'] ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Initialize DataTables and Charts -->
<script>
    $(function() {
        // Initialize DataTables with improved styling
        $('#cropBlocksTable, #livestockBlocksTable, #marketActivitiesTable').DataTable({
            "responsive": false,
            "lengthChange": false,
            "autoWidth": false,
            "scrollX": true,
            "buttons": ["copy", "csv", "excel", "pdf", "print"],
            "pageLength": 5,
            "dom": '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6"f>>rtip'
        }).buttons().container().appendTo('#cropBlocksTable_wrapper .col-md-6:eq(0)');

        // Prepare data for Crop Types Chart
        const cropData = {
            labels: <?= json_encode(array_column($crop_blocks, 'crop_name')) ?>,
            datasets: [{
                data: <?= json_encode(array_column($crop_blocks, 'hectares')) ?>,
                backgroundColor: <?= json_encode(array_column($crop_blocks, 'crop_color_code')) ?>
            }]
        };

        // Create Crop Types Chart
        new Chart(document.getElementById('cropTypesChart'), {
            type: 'doughnut',
            data: cropData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Prepare data for Livestock Types Chart
        const livestockData = {
            labels: <?= json_encode(array_column($livestock_blocks, 'livestock_name')) ?>,
            datasets: [{
                data: <?= json_encode(array_column($livestock_blocks, 'quantity')) ?>,
                backgroundColor: <?= json_encode(array_column($livestock_blocks, 'livestock_color_code')) ?>
            }]
        };

        // Create Livestock Types Chart
        new Chart(document.getElementById('livestockTypesChart'), {
            type: 'doughnut',
            data: livestockData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
<?= $this->endSection() ?>