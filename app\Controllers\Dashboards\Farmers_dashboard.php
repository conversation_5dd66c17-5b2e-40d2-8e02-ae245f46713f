<?php

namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\AdxCountryModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\FarmersChildrenModel;
use App\Models\CropsModel;
use App\Models\LivestockModel;
use App\Models\CropsFarmBlockModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\LivestockFarmDataModel;
use App\Models\PermissionsUserDistrictsModel;

class Farmers_dashboard extends BaseController
{
    protected $models = [];
    protected $session;

    public function __construct()
    {
        helper(['url', 'form', 'info', 'weather']);
        
        // Initialize session
        $this->session = \Config\Services::session();
        
        // Validate session
        $this->validateSession();
        
        // Initialize models
        $this->initializeModels();
    }

    protected function validateSession(): void
    {
        $requiredSessionVars = [
            'emp_id', 'org_id', 'orgcountry_id',
            'orgprovince_id'
        ];
        
        foreach ($requiredSessionVars as $var) {
            if (!$this->session->has($var)) {
                throw new \Exception("Required session variable '$var' is not set");
            }
        }
    }

    protected function initializeModels(): void
    {
        $models = [
            'farmer' => FarmerInformationModel::class,
            'country' => AdxCountryModel::class,
            'province' => AdxProvinceModel::class,
            'district' => AdxDistrictModel::class,
            'llg' => AdxLlgModel::class,
            'ward' => AdxWardModel::class,
            'farmersChildren' => FarmersChildrenModel::class,
            'crops' => CropsModel::class,
            'livestock' => LivestockModel::class,
            'cropsFarmBlock' => CropsFarmBlockModel::class,
            'livestockFarmBlock' => LivestockFarmBlockModel::class,
            'livestockFarmData' => LivestockFarmDataModel::class,
            'permissionsUserDistricts' => PermissionsUserDistrictsModel::class,
        ];

        foreach ($models as $key => $modelClass) {
            $this->models[$key] = new $modelClass();
        }
    }

    public function index()
    {
        try {
            // Get all farmers
            $farmers = $this->models['farmer']->findAll();
            
            // Get all districts
            $districts = $this->models['district']->findAll();
            
            // Get all crops with their colors
            $crops = $this->models['crops']->findAll();
            
            // Get all livestock with their colors
            $livestock = $this->models['livestock']->findAll();

            // Get farmers by crop type (aggregated by district)
            $farmers_by_crop = [];
            foreach ($districts as $district) {
                $district_crops = $this->models['cropsFarmBlock']
                    ->select('adx_crops.crop_name, adx_crops.crop_color_code, COUNT(DISTINCT crops_farm_blocks.farmer_id) as total')
                    ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
                    ->where('crops_farm_blocks.status', 'active')
                    ->groupBy('crop_id')
                    ->findAll();
                
                if (!empty($district_crops)) {
                    $farmers_by_crop[$district['name']] = $district_crops;
                }
            }

            // Get farmers by livestock type (aggregated by district)
            $farmers_by_livestock = [];
            foreach ($districts as $district) {
                $district_livestock = $this->models['livestockFarmData']
                    ->select('adx_livestock.livestock_name, adx_livestock.livestock_color_code, COUNT(DISTINCT livestock_farm_blocks.farmer_id) as total')
                    ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
                    ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
                    ->where('livestock_farm_blocks.status', 'active')
                    ->groupBy('livestock_id')
                    ->findAll();
                
                if (!empty($district_livestock)) {
                    $farmers_by_livestock[$district['name']] = $district_livestock;
                }
            }

            // Get total farmers in crops farming
            $farmers_in_crops = $this->models['cropsFarmBlock']
                ->select('COUNT(DISTINCT farmer_id) as total')
                ->where('status', 'active')
                ->first();

            // Get total farmers in livestock farming
            $farmers_in_livestock = $this->models['livestockFarmBlock']
                ->select('COUNT(DISTINCT farmer_id) as total')
                ->where('status', 'active')
                ->first();

            // Get gender distribution
            $gender_distribution = $this->models['farmer']
                ->select('gender, COUNT(*) as total')
                ->groupBy('gender')
                ->findAll();

            // Calculate age distribution by gender
            $age_ranges = range(18, 80, 5);
            $male_age_dist = array_fill(0, count($age_ranges), 0);
            $female_age_dist = array_fill(0, count($age_ranges), 0);

            foreach ($farmers as $farmer) {
                if (!empty($farmer['date_of_birth'])) {
                    $age = date_diff(date_create($farmer['date_of_birth']), date_create('today'))->y;
                    $range_index = floor(($age - 18) / 5);
                    if ($range_index >= 0 && $range_index < count($age_ranges)) {
                        if ($farmer['gender'] === 'Male') {
                            $male_age_dist[$range_index]++;
                        } else {
                            $female_age_dist[$range_index]++;
                        }
                    }
                }
            }

            $data = [
                'total_farmers' => count($farmers),
                'districts' => $districts,
                'crops' => $crops,
                'livestock' => $livestock,
                'farmers_by_crop' => $farmers_by_crop,
                'farmers_by_livestock' => $farmers_by_livestock,
                'farmers_in_crops' => $farmers_in_crops['total'] ?? 0,
                'farmers_in_livestock' => $farmers_in_livestock['total'] ?? 0,
                'gender_distribution' => $gender_distribution,
                'age_ranges' => $age_ranges,
                'male_age_dist' => $male_age_dist,
                'female_age_dist' => $female_age_dist,
                'all_farmers' => $this->models['farmer']
                    ->select('
                        farmer_information.*,
                        farmer_information.status,
                        adx_province.name as province_name,
                        adx_district.name as district_name,
                        adx_llg.name as llg_name,
                        adx_ward.name as ward_name,
                        farmer_information.marital_status,
                        adx_education.name as highest_education,
                        (SELECT COUNT(*) FROM farmers_children WHERE farmers_children.farmer_id = farmer_information.id) as children_count,
                        (SELECT COUNT(*) FROM crops_farm_blocks WHERE crops_farm_blocks.farmer_id = farmer_information.id AND crops_farm_blocks.status = "active") as crops_blocks_count,
                        (SELECT SUM(hectares) FROM crops_farm_crops_data 
                            JOIN crops_farm_blocks ON crops_farm_blocks.id = crops_farm_crops_data.block_id 
                            WHERE crops_farm_blocks.farmer_id = farmer_information.id 
                            AND crops_farm_blocks.status = "active") as total_hectares,
                        (SELECT SUM(hectares) FROM crops_farm_disease_data 
                            JOIN crops_farm_blocks ON crops_farm_blocks.id = crops_farm_disease_data.block_id 
                            WHERE crops_farm_blocks.farmer_id = farmer_information.id
                            AND crops_farm_disease_data.status = "active") as disease_affected_hectares,
                        (SELECT SUM(market_price_per_unit * quantity) FROM crops_farm_marketing_data 
                            WHERE crops_farm_marketing_data.farmer_id = farmer_information.id 
                            AND crops_farm_marketing_data.status = "active") as total_crops_revenue,
                        (SELECT GROUP_CONCAT(DISTINCT adx_crops.crop_name) FROM crops_farm_crops_data 
                            JOIN crops_farm_blocks ON crops_farm_blocks.id = crops_farm_crops_data.block_id 
                            JOIN adx_crops ON adx_crops.id = crops_farm_crops_data.crop_id 
                            WHERE crops_farm_blocks.farmer_id = farmer_information.id) as crop_types,
                        (SELECT COUNT(*) FROM livestock_farm_blocks 
                            WHERE livestock_farm_blocks.farmer_id = farmer_information.id 
                            AND livestock_farm_blocks.status = "active") as livestock_blocks_count,
                        (SELECT AVG(cost_per_livestock) FROM livestock_farm_data 
                            JOIN livestock_farm_blocks ON livestock_farm_blocks.id = livestock_farm_data.block_id 
                            WHERE livestock_farm_blocks.farmer_id = farmer_information.id) as avg_livestock_value,
                        (SELECT GROUP_CONCAT(DISTINCT adx_livestock.livestock_name) FROM livestock_farm_data 
                            JOIN livestock_farm_blocks ON livestock_farm_blocks.id = livestock_farm_data.block_id 
                            JOIN adx_livestock ON adx_livestock.id = livestock_farm_data.livestock_id 
                            WHERE livestock_farm_blocks.farmer_id = farmer_information.id) as livestock_types
                    ')
                    ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
                    ->join('adx_province', 'adx_province.id = adx_district.province_id', 'left')
                    ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
                    ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
                    ->join('adx_education', 'adx_education.id = farmer_information.highest_education_id', 'left')
                    ->findAll(),
                'page_title' => 'Farmers Dashboard',
                'title' => 'Farmers Dashboard',
                'menu' => 'farmers-dashboard'
            ];

            return view('dashboard_reports/dashboard_farmers', $data);
        } catch (\Exception $e) {
            log_message('error', '[Farmers Dashboard] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while loading the dashboard');
        }
    }

    public function profile($farmer_id = null)
    {
        if ($farmer_id === null) {
            return redirect()->to(base_url('dashboards/farmers'));
        }

        try {
            // Use models from initialized array
            $farmerModel = $this->models['farmer'];
            $childrenModel = $this->models['farmersChildren'];
            $cropBlockModel = $this->models['cropsFarmBlock'];
            $livestockBlockModel = $this->models['livestockFarmBlock'];
            $livestockDataModel = $this->models['livestockFarmData'];
            $cropsModel = $this->models['crops'];
            $livestockModel = $this->models['livestock'];
            
            // Initialize models not in the shared array
            $cropDataModel = new \App\Models\CropsFarmCropsDataModel();
            $marketingModel = new \App\Models\CropsFarmMarketingDataModel();

            try {
                // Get farmer's basic information with location details
                $farmer = $farmerModel->select('
                    farmer_information.*,
                    adx_province.name as province_name,
                    adx_district.name as district_name,
                    adx_llg.name as llg_name,
                    adx_ward.name as ward_name,
                    adx_education.name as highest_education,
                    adx_country.name as country_name
                ')
                ->join('adx_province', 'adx_province.id = farmer_information.province_id')
                ->join('adx_district', 'adx_district.id = farmer_information.district_id')
                ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id')
                ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id')
                ->join('adx_education', 'adx_education.id = farmer_information.highest_education_id', 'left')
                ->join('adx_country', 'adx_country.id = farmer_information.country_id', 'left')
                ->find($farmer_id);

                if (!$farmer) {
                    throw new \Exception('Farmer not found');
                }
            } catch (\Exception $e) {
                log_message('error', '[Farmer Profile] Error fetching farmer data: ' . $e->getMessage());
                return redirect()->to(base_url('dashboards/farmers'))
                    ->with('error', 'Failed to load farmer profile: ' . $e->getMessage());
            }

            // Get children information
            $children = $childrenModel->where('farmer_id', $farmer_id)->findAll();

            try {
                // Get crop blocks with details
                $crop_blocks = $cropBlockModel->select('
                    crops_farm_blocks.*,
                    adx_crops.crop_name,
                    adx_crops.crop_color_code,
                    adx_province.name as province_name,
                    adx_district.name as district_name,
                    adx_llg.name as llg_name,
                    adx_ward.name as ward_name,
                    (
                        SELECT SUM(hectares)
                        FROM crops_farm_crops_data
                        WHERE block_id = crops_farm_blocks.id
                        AND action_type = "add"
                        AND status = "active"
                    ) - COALESCE(
                        (
                            SELECT SUM(hectares)
                            FROM crops_farm_crops_data
                            WHERE block_id = crops_farm_blocks.id
                            AND action_type = "remove"
                            AND status = "active"
                        ), 0
                    ) as hectares
                ')
                ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
                ->join('adx_province', 'adx_province.id = crops_farm_blocks.province_id')
                ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
                ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
                ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
                ->where('crops_farm_blocks.farmer_id', $farmer_id)
                ->where('crops_farm_blocks.status', 'active')
                ->findAll();
            } catch (\Exception $e) {
                log_message('error', '[Farmer Profile] Error fetching crop blocks: ' . $e->getMessage());
                return redirect()->to(base_url('dashboards/farmers'))
                    ->with('error', 'Failed to load crop blocks data');
            }

            try {
                // Get livestock blocks with details
                $livestock_blocks = $livestockBlockModel->select('
                    livestock_farm_blocks.*,
                    adx_livestock.name as livestock_name,
                    adx_livestock.color_code as livestock_color_code,
                    adx_province.name as province_name,
                    adx_district.name as district_name,
                    adx_llg.name as llg_name,
                    adx_ward.name as ward_name,
                    IFNULL(livestock_farm_data.he_total, 0) as he_total,
                    IFNULL(livestock_farm_data.she_total, 0) as she_total,
                    livestock_farm_data.cost_per_livestock,
                    livestock_farm_data.breed,
                    livestock_farm_data.growth_stage
                ')
                ->join('livestock_farm_data', 'livestock_farm_data.block_id = livestock_farm_blocks.id')
                ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
                ->join('adx_province', 'adx_province.id = livestock_farm_blocks.province_id')
                ->join('adx_district', 'adx_district.id = livestock_farm_blocks.district_id')
                ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
                ->join('adx_ward', 'adx_ward.id = livestock_farm_blocks.ward_id')
                ->where('livestock_farm_blocks.farmer_id', $farmer_id)
                ->where('livestock_farm_blocks.status', 'active')
                ->where('livestock_farm_data.status', 'active')
                ->findAll();
            } catch (\Exception $e) {
                log_message('error', '[Farmer Profile] Error fetching livestock blocks: ' . $e->getMessage());
                return redirect()->to(base_url('dashboards/farmers'))
                    ->with('error', 'Failed to load livestock blocks data');
            }

            // Calculate summary statistics
            $farmer['crops_blocks_count'] = count($crop_blocks);
            $farmer['livestock_blocks_count'] = count($livestock_blocks);
            $farmer['total_hectares'] = array_sum(array_column($crop_blocks, 'hectares'));
            
            // Calculate total crops revenue from marketing data
            $total_revenue = $marketingModel->select('SUM(market_price_per_unit * quantity) as total_revenue')
                ->where('farmer_id', $farmer_id)
                ->where('status', 'active')
                ->first();
            $farmer['total_crops_revenue'] = $total_revenue['total_revenue'] ?? 0;

            // Calculate total livestock value
            $farmer['total_livestock_value'] = array_sum(array_map(function($block) {
                return $block['he_total'] + $block['she_total'] * $block['cost_per_livestock'];
            }, $livestock_blocks));

            try {
                // Get market activities
                $market_activities = $marketingModel->select('
                    crops_farm_marketing_data.*,
                    adx_crops.crop_name,
                    adx_crops.crop_color_code,
                    crop_buyers.name as buyer_name
                ')
                ->join('adx_crops', 'adx_crops.id = crops_farm_marketing_data.crop_id')
                ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id', 'left')
                ->where('crops_farm_marketing_data.farmer_id', $farmer_id)
                ->where('crops_farm_marketing_data.status', 'active')
                ->orderBy('crops_farm_marketing_data.market_date', 'DESC')
                ->findAll();
            } catch (\Exception $e) {
                log_message('error', '[Farmer Profile] Error fetching market activities: ' . $e->getMessage());
                return redirect()->to(base_url('dashboards/farmers'))
                    ->with('error', 'Failed to load market activities data');
            }

            try {
                // Prepare livestock blocks data
                $prepared_livestock_blocks = array_map(function($block) {
                    return [
                        'block_code' => $block['block_code'],
                        'livestock_name' => $block['livestock_name'],
                        'livestock_color_code' => $block['livestock_color_code'],
                        'quantity' => $block['he_total'] + $block['she_total'],
                        'cost_per_livestock' => $block['cost_per_livestock'],
                        'province_name' => $block['province_name'],
                        'district_name' => $block['district_name'],
                        'llg_name' => $block['llg_name'],
                        'ward_name' => $block['ward_name'],
                        'village' => $block['village'],
                        'status' => $block['status'],
                        'breed' => $block['breed'],
                        'growth_stage' => $block['growth_stage']
                    ];
                }, $livestock_blocks);

                return view('dashboard_reports/dashboard_farmers_profile', [
                    'page_title' => 'Farmer Profile',
                    'title' => 'Farmer Profile',
                    'menu' => 'farmers-dashboard',
                    'farmer' => $farmer,
                    'children' => $children,
                    'crop_blocks' => $crop_blocks,
                    'market_activities' => $market_activities,
                    'livestock_blocks' => $prepared_livestock_blocks
                ]);
            } catch (\Exception $e) {
                log_message('error', '[Farmer Profile] Error preparing view data: ' . $e->getMessage());
                return redirect()->to(base_url('dashboards/farmers'))
                    ->with('error', 'Failed to prepare profile data');
            }
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return redirect()->to(base_url('dashboards/farmers'))->with('error', 'An error occurred while loading the farmer profile.');
        }
    }
}
