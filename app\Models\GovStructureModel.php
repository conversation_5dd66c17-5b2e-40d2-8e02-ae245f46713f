<?php

namespace App\Models;

use CodeIgniter\Model;

class GovStructureModel extends Model
{
    protected $table            = 'gov_structure';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'parent_id',
        'json_id',
        'level',
        'code',
        'name',
        'flag_filepath',
        'map_center',
        'map_zoom',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Timestamps
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'level' => 'required|in_list[province,district,llg,ward]',
        'code' => 'required|max_length[20]',
        'name' => 'required|max_length[255]',
        'json_id' => 'required|max_length[255]'
    ];

    protected $validationMessages = [
        'level' => [
            'required' => 'Level is required',
            'in_list' => 'Level must be one of: province, district, llg, ward'
        ],
        'code' => [
            'required' => 'Code is required',
            'max_length' => 'Code cannot exceed 20 characters'
        ],
        'name' => [
            'required' => 'Name is required',
            'max_length' => 'Name cannot exceed 255 characters'
        ],
        'json_id' => [
            'required' => 'JSON ID is required',
            'max_length' => 'JSON ID cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Helper methods
    public function getByLevel($level)
    {
        return $this->where('level', $level)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    public function getProvinces()
    {
        return $this->getByLevel('province');
    }

    public function getDistricts($provinceId = null)
    {
        $query = $this->where('level', 'district');
        if ($provinceId) {
            $query->where('parent_id', $provinceId);
        }
        return $query->orderBy('name', 'ASC')->findAll();
    }

    public function getLlgs($districtId = null)
    {
        $query = $this->where('level', 'llg');
        if ($districtId) {
            $query->where('parent_id', $districtId);
        }
        return $query->orderBy('name', 'ASC')->findAll();
    }

    public function getWards($llgId = null)
    {
        $query = $this->where('level', 'ward');
        if ($llgId) {
            $query->where('parent_id', $llgId);
        }
        return $query->orderBy('name', 'ASC')->findAll();
    }

    public function getChildren($parentId)
    {
        return $this->where('parent_id', $parentId)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    public function getHierarchy($id)
    {
        $item = $this->find($id);
        if (!$item) {
            return null;
        }

        $hierarchy = [$item];
        $currentParentId = $item['parent_id'];

        while ($currentParentId) {
            $parent = $this->find($currentParentId);
            if ($parent) {
                array_unshift($hierarchy, $parent);
                $currentParentId = $parent['parent_id'];
            } else {
                break;
            }
        }

        return $hierarchy;
    }

    public function getByCode($code)
    {
        return $this->where('code', $code)->first();
    }

    public function getByJsonId($jsonId)
    {
        return $this->where('json_id', $jsonId)->first();
    }

    // Get all items with their parent names
    public function getWithParentNames()
    {
        return $this->select('gov_structure.*, parent.name as parent_name')
                    ->join('gov_structure as parent', 'parent.id = gov_structure.parent_id', 'left')
                    ->orderBy('gov_structure.level', 'ASC')
                    ->orderBy('gov_structure.name', 'ASC')
                    ->findAll();
    }

    // Get statistics by level
    public function getStatsByLevel()
    {
        return $this->select('level, COUNT(*) as count')
                    ->groupBy('level')
                    ->findAll();
    }
}
