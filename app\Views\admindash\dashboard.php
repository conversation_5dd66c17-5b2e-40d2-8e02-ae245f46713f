<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="text-success"><?= session('orgname') ?></h1>
                <h5 class="m-0 text-muted">Agricultural Data Dashboard</h5>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><i class="fas fa-leaf text-success"></i></li>
                    <li class="breadcrumb-item active">Dashboard</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- License Warning -->
        <?php if (isset($org['license_status']) && $org['license_status'] == "trial") : ?>
            <div class="alert alert-warning alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-exclamation-triangle"></i> Trial License!</h5>
                You are currently using a trial version of AgriStats. Contact us to upgrade.
            </div>
        <?php endif; ?>

        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Farmers</span>
                        <span class="info-box-number"><?= number_format($total_farmers) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-info elevation-1"><i class="fas fa-store"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Crop Buyers</span>
                        <span class="info-box-number"><?= number_format($total_buyers) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-map-marker-alt"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Farm Blocks</span>
                        <span class="info-box-number"><?= number_format($total_farm_blocks) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-chart-line"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Sales</span>
                        <span class="info-box-number">K<?= number_format($marketing_data['total_sales'], 2) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Left col -->
            <div class="col-md-8">
                <!-- Crop Statistics -->
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-1"></i>
                            Crop Statistics
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="small-box bg-info">
                                    <div class="inner">
                                        <h3><?= number_format($crops_data['total_plantings']) ?></h3>
                                        <p>Total Plantings</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-seedling"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="small-box bg-success">
                                    <div class="inner">
                                        <h3><?= number_format($harvest_data['total_harvests']) ?></h3>
                                        <p>Total Harvests</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-tractor"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="small-box bg-warning">
                                    <div class="inner">
                                        <h3><?= number_format($marketing_data['total_sold_quantity']) ?></h3>
                                        <p>Total Sold Quantity</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fas fa-balance-scale"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="card-title">
                            <i class="fas fa-history mr-1"></i>
                            Recent Activity
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Date</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_harvests as $harvest): ?>
                                    <tr>
                                        <td><span class="badge bg-success">Harvest</span></td>
                                        <td><?= date('d M Y', strtotime($harvest['created_at'])) ?></td>
                                        <td>Quantity: <?= number_format($harvest['quantity']) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php foreach ($recent_sales as $sale): ?>
                                    <tr>
                                        <td><span class="badge bg-info">Sale</span></td>
                                        <td><?= date('d M Y', strtotime($sale['created_at'])) ?></td>
                                        <td>
                                            Quantity: <?= number_format($sale['quantity']) ?>, 
                                            Price: K<?= number_format($sale['market_price_per_unit'], 2) ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right col -->
            <div class="col-md-4">
                <!-- Geographical Stats -->
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="card-title">
                            <i class="fas fa-globe mr-1"></i>
                            Geographical Statistics
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <tr>
                                    <td>Provinces</td>
                                    <td><span class="badge bg-primary"><?= number_format($total_provinces) ?></span></td>
                                </tr>
                                <tr>
                                    <td>Districts</td>
                                    <td><span class="badge bg-info"><?= number_format($total_districts) ?></span></td>
                                </tr>
                                <tr>
                                    <td>LLGs</td>
                                    <td><span class="badge bg-success"><?= number_format($total_llgs) ?></span></td>
                                </tr>
                                <tr>
                                    <td>Wards</td>
                                    <td><span class="badge bg-warning"><?= number_format($total_wards) ?></span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Top Crops -->
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="card-title">
                            <i class="fas fa-leaf mr-1"></i>
                            Top Crops
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        <ul class="products-list product-list-in-card pl-2 pr-2">
                            <?php foreach ($top_crops as $crop): ?>
                            <li class="item">
                                <div class="product-info">
                                    <a href="javascript:void(0)" class="product-title">
                                        
                                    </a>
                                    <span class="product-description">
                                        <?= number_format($crop['count']) ?> plantings
                                    </span>
                                </div>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Custom Styles -->
<style>
.info-box {
    transition: transform 0.2s ease;
}

.info-box:hover {
    transform: translateY(-5px);
}

.small-box {
    border-radius: 0.5rem;
    transition: transform 0.2s ease;
}

.small-box:hover {
    transform: translateY(-5px);
}

.card {
    border-radius: 0.5rem;
    box-shadow: 0 0 1rem rgba(0,0,0,.15);
}

.table-responsive {
    border-radius: 0.5rem;
}

.badge {
    padding: 0.5em 0.75em;
}

.products-list .product-info {
    margin-left: 0;
}

.products-list .product-title {
    font-weight: 600;
}

.products-list .product-description {
    color: #6c757d;
}
</style>

<?= $this->endSection() ?>