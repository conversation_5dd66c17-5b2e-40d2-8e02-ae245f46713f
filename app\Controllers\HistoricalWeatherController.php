<?php
namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\ResponseInterface;
use DateTime;
use DateTimeZone;

class HistoricalWeatherController extends Controller
{
    public function getHistoricalWeatherData()
    {
        // Input parameters (you can modify these or pass them via request)
        $lat = -9.451101;  // Latitude for Port Moresby
        $lon = 147.192658; // Longitude for Port Moresby
        
        // Date for historical data (use current date if not specified)
        $dateString = "2024-11-25"; // date('Y-m-d');

        try {
            // Validate inputs
            if (!is_numeric($lat) || !is_numeric($lon)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Invalid coordinates'
                ])->setStatusCode(ResponseInterface::HTTP_BAD_REQUEST);
            }

            // Convert date string to Unix timestamp
            $timestamp = $this->convertDateToUnixTimestamp($dateString);

            // API Details
            $apiKey = "71cad559369fe7654c25426097afb69d";
            
            // Historical weather API endpoint
            $apiUrl = "https://api.openweathermap.org/data/3.0/onecall/timemachine";

            // Set up CURL request
            $client = \Config\Services::curlrequest();

            // Make the API request
            $response = $client->get($apiUrl, [
                'query' => [
                    'lat' => $lat,
                    'lon' => $lon,
                    'dt' => $timestamp,
                    'appid' => $apiKey,
                    'units' => 'metric' // Get temperature in Celsius
                ]
            ]);

            // Check response status
            if ($response->getStatusCode() === 200) {
                // Parse JSON response
                $data = json_decode($response->getBody(), true);

                // Check if data was successfully parsed
                if ($data === null) {
                    throw new \Exception('Failed to parse JSON response');
                }

                // Prepare structured historical weather data
                $historicalWeatherData = $this->processHistoricalWeatherData($data, $lat, $lon, $dateString);

                // Create HTML output
                $html = $this->generateWeatherHTML($historicalWeatherData);

                // Display the HTML
                echo $html;

                // Return JSON response
                return $this->response->setJSON([
                    'status' => 'success',
                    'data' => $historicalWeatherData
                ]);
            } else {
                // Handle API error
                $errorBody = $response->getBody();
                log_message('error', 'Historical Weather API Error: ' . $errorBody);

                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Failed to fetch historical weather data',
                    'details' => $errorBody
                ])->setStatusCode(ResponseInterface::HTTP_INTERNAL_SERVER_ERROR);
            }
        } catch (\Exception $e) {
            // Log the full exception for debugging
            log_message('error', '[Historical Weather API Error] ' . $e->getMessage());
            log_message('error', $e->getTraceAsString());

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'An unexpected error occurred',
                'error_details' => $e->getMessage()
            ])->setStatusCode(ResponseInterface::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function convertDateToUnixTimestamp(string $dateString): int
    {
        try {
            // Create DateTime object
            $date = new DateTime($dateString, new DateTimeZone('UTC'));
            
            // Ensure the date is not in the future
            $now = new DateTime('now', new DateTimeZone('UTC'));
            if ($date > $now) {
                throw new \Exception('Date cannot be in the future');
            }

            // Check if the date is within the last 5 days (OpenWeather API limitation)
            $fiveDaysAgo = (new DateTime('now', new DateTimeZone('UTC')))->modify('-5 days');
            if ($date < $fiveDaysAgo) {
                throw new \Exception('Historical data is only available for the past 5 days');
            }

            // Return Unix timestamp
            return $date->getTimestamp();
        } catch (\Exception $e) {
            throw new \Exception('Invalid date: ' . $e->getMessage());
        }
    }

    private function processHistoricalWeatherData(array $data, float $lat, float $lon, string $dateString): array
    {
        // Check if data contains hourly information
        if (!isset($data['data']) || empty($data['data'])) {
            throw new \Exception('No weather data available');
        }

        // Get the weather data
        $weatherData = $data['data'][0];

        return [
            'location' => [
                'latitude' => $lat,
                'longitude' => $lon,
                'date' => $dateString
            ],
            'temperature' => [
                'current' => $weatherData['temp'] ?? null,
                'feels_like' => $weatherData['feels_like'] ?? null,
            ],
            'weather' => [
                'main' => $weatherData['weather'][0]['main'] ?? 'Unknown',
                'description' => $weatherData['weather'][0]['description'] ?? 'No description',
                'icon' => $weatherData['weather'][0]['icon'] ?? null
            ],
            'additional_details' => [
                'humidity' => $weatherData['humidity'] ?? null,
                'pressure' => $weatherData['pressure'] ?? null,
                'wind_speed' => $weatherData['wind_speed'] ?? null,
                'wind_direction' => $weatherData['wind_deg'] ?? null,
                'cloudiness' => $weatherData['clouds'] ?? null,
                'uvi' => $weatherData['uvi'] ?? null,
                'visibility' => $weatherData['visibility'] ?? null
            ],
            'timestamp' => date('Y-m-d H:i:s', $weatherData['dt'] ?? time())
        ];
    }

    private function generateWeatherHTML(array $weatherData): string
    {
        $html = '<div class="weather-container">';
        
        // Location and Date
        $html .= '<div class="weather-header">';
        $html .= '<h2>Historical Weather Data</h2>';
        $html .= '<p>Date: ' . $weatherData['location']['date'] . '</p>';
        $html .= '<p>Location: ' . $weatherData['location']['latitude'] . ', ' . $weatherData['location']['longitude'] . '</p>';
        $html .= '</div>';

        // Main Weather Info
        $html .= '<div class="weather-main">';
        $html .= '<h3>' . $weatherData['weather']['main'] . '</h3>';
        $html .= '<p>' . $weatherData['weather']['description'] . '</p>';
        if ($weatherData['weather']['icon']) {
            $html .= '<img src="https://openweathermap.org/img/w/' . $weatherData['weather']['icon'] . '.png" alt="Weather Icon">';
        }
        $html .= '</div>';

        // Temperature
        $html .= '<div class="weather-temp">';
        $html .= '<h3>Temperature</h3>';
        $html .= '<p>Current: ' . $weatherData['temperature']['current'] . '°C</p>';
        $html .= '<p>Feels Like: ' . $weatherData['temperature']['feels_like'] . '°C</p>';
        $html .= '</div>';

        // Additional Details
        $html .= '<div class="weather-details">';
        $html .= '<h3>Additional Details</h3>';
        $html .= '<p>Humidity: ' . $weatherData['additional_details']['humidity'] . '%</p>';
        $html .= '<p>Pressure: ' . $weatherData['additional_details']['pressure'] . ' hPa</p>';
        $html .= '<p>Wind Speed: ' . $weatherData['additional_details']['wind_speed'] . ' m/s</p>';
        $html .= '<p>Wind Direction: ' . $weatherData['additional_details']['wind_direction'] . '°</p>';
        $html .= '<p>Cloudiness: ' . $weatherData['additional_details']['cloudiness'] . '%</p>';
        if (isset($weatherData['additional_details']['uvi'])) {
            $html .= '<p>UV Index: ' . $weatherData['additional_details']['uvi'] . '</p>';
        }
        if (isset($weatherData['additional_details']['visibility'])) {
            $html .= '<p>Visibility: ' . ($weatherData['additional_details']['visibility'] / 1000) . ' km</p>';
        }
        $html .= '</div>';

        $html .= '<p class="timestamp">Last Updated: ' . $weatherData['timestamp'] . '</p>';
        
        $html .= '</div>';

        return $html;
    }
} 