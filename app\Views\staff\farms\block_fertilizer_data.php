<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/farms/fertilizer_data') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Fertilizer Data
        </a>
        <div>
            <!-- Button trigger modal -->
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addFertilizerDataModal">
                <i class="fas fa-plus-circle"></i> Add Fertilizer Data
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- Farm Block Details Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Block Code:</strong> <?= esc($block['block_code']) ?></p>
                <p><strong>Farmer:</strong> <?= esc($farmer['given_name'] ?? '') ?> <?= esc($farmer['surname'] ?? '') ?></p>
                <p><strong>Remarks:</strong> <?= esc($block['remarks']) ?: 'No remarks' ?></p>
            </div>
        </div>
    </div>
    
    <!-- Farm Block Location Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Location</h5>
            </div>
            <div class="card-body">
                <p><strong>Village:</strong> <?= esc($block['village']) ?></p>
                <p><strong>Block Site:</strong> <?= esc($block['block_site']) ?></p>
                <p><strong>Coordinates:</strong> <?= esc($block['lon']) ?>, <?= esc($block['lat']) ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Fertilizer Data History Section -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Fertilizer Application History
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped text-nowrap" id="fertilizerDataTable">
                        <thead>
                            <tr>
                                <th class="text-nowrap">Date</th>
                                <th class="text-nowrap">Fertilizer Type</th>
                                <th class="text-nowrap">Application Method</th>
                                <th class="text-nowrap">Quantity (kg)</th>
                                <th class="text-nowrap">Area Covered (ha)</th>
                                <th class="text-nowrap">Cost</th>
                                <th class="text-nowrap">Applied By</th>
                                <th class="text-nowrap">Remarks</th>
                                <th class="text-nowrap">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable('#fertilizerDataTable')) {
        $('#fertilizerDataTable').DataTable().destroy();
    }
    
    // Initialize DataTable
    $('#fertilizerDataTable').DataTable({
        pageLength: 10,
        responsive: false,
        columnDefs: [
            { orderable: true, targets: '_all' }
        ],
        order: [[0, 'desc']]
    });
});
</script>
<?= $this->endSection() ?> 