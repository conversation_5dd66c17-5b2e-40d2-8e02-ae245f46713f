<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Add New Farmer</h5>
    </div>
    <div class="card-body">
        <form action="<?= base_url('staff/farmers') ?>" method="post" enctype="multipart/form-data">
            <?= csrf_field() ?>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="given_name" class="form-label">Given Name *</label>
                    <input type="text" class="form-control" id="given_name" name="given_name" required>
                </div>
                <div class="col-md-6">
                    <label for="surname" class="form-label">Surname *</label>
                    <input type="text" class="form-control" id="surname" name="surname" required>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="date_of_birth" class="form-label">Date of Birth *</label>
                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                </div>
                <div class="col-md-4">
                    <label for="gender" class="form-label">Gender *</label>
                    <select class="form-select" id="gender" name="gender" required>
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="marital_status" class="form-label">Marital Status *</label>
                    <select class="form-select" id="marital_status" name="marital_status" required>
                        <option value="">Select Status</option>
                        <option value="Single">Single</option>
                        <option value="married">Married</option>
                        <option value="divorce">Divorce</option>
                        <option value="widow">Widow</option>
                        <option value="de-facto">De-facto</option>
                    </select>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="district_id" class="form-label">District</label>
                    <input type="text" class="form-control" value="<?= esc($district_name) ?>" readonly>
                    <input type="hidden" name="district_id" value="<?= session()->get('district_id') ?>">
                </div>
                <div class="col-md-4">
                    <label for="llg_id" class="form-label">LLG *</label>
                    <select class="form-select" id="llg_id" name="llg_id" required>
                        <option value="">Select LLG</option>
                        <?php foreach ($llgs as $llg): ?>
                            <option value="<?= $llg['id'] ?>"><?= esc($llg['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="ward_id" class="form-label">Ward *</label>
                    <select class="form-select" id="ward_id" name="ward_id" required disabled>
                        <option value="">Select Ward</option>
                    </select>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="village" class="form-label">Village</label>
                    <input type="text" class="form-control" id="village" name="village">
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="phone" class="form-label">Phone</label>
                    <input type="tel" class="form-control" id="phone" name="phone">
                </div>
                <div class="col-md-4">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email">
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                    </select>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-12">
                    <label for="address" class="form-label">Address</label>
                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="highest_education_id" class="form-label">Highest Education Level</label>
                    <select class="form-select" id="highest_education_id" name="highest_education_id">
                        <option value="">Select Education Level</option>
                        <?php foreach ($education_levels as $edu): ?>
                            <option value="<?= $edu['id'] ?>"><?= esc($edu['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="course_taken" class="form-label">Course Taken</label>
                    <input type="text" class="form-control" id="course_taken" name="course_taken">
                </div>
                <div class="col-md-4">
                    <label for="id_photo" class="form-label">ID Photo</label>
                    <input type="file" class="form-control" id="id_photo" name="id_photo" accept="image/*">
                </div>
            </div>

            <div class="text-end">
                <a href="<?= base_url('staff/farmers') ?>" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Farmer</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // LLG change event
    $('#llg_id').on('change', function() {
        const llgId = $(this).val();
        const wardSelect = $('#ward_id');
        
        if (llgId) {
            // Enable Ward dropdown and fetch Wards
            wardSelect.prop('disabled', false);
            
            $.ajax({
                url: '<?= base_url('staff/farmers/getWardsByLlg') ?>/' + llgId,
                method: 'GET',
                success: function(response) {
                    wardSelect.empty().append('<option value="">Select Ward</option>');
                    response.forEach(function(ward) {
                        wardSelect.append(`<option value="${ward.id}">${ward.name}</option>`);
                    });
                },
                error: function() {
                    wardSelect.empty().append('<option value="">Error loading wards</option>');
                    wardSelect.prop('disabled', true);
                }
            });
        } else {
            // Disable and reset Ward dropdown
            wardSelect.prop('disabled', true).empty().append('<option value="">Select Ward</option>');
        }
    });
});
</script>
<?= $this->endSection() ?> 