<?php

namespace App\Controllers;

use App\Models\TrainingsModel;
use CodeIgniter\RESTful\ResourceController;

class StaffTrainingController extends ResourceController
{
    protected $trainingModel;
    protected $helpers = ['form', 'url', 'file', 'text', 'info'];

    public function __construct()
    {
        $this->trainingModel = new TrainingsModel();
        // Fix any training records with incorrectly formatted attendees data
        $this->fixTrainingData();
    }

    /**
     * Fix existing training data to ensure attendees is properly JSON-encoded
     */
    private function fixTrainingData()
    {
        $trainings = $this->trainingModel->findAll();

        foreach ($trainings as $training) {
            $needsUpdate = false;

            // Check if attendees needs fixing
            if (!empty($training['attendees']) && !is_array(json_decode($training['attendees'], true))) {
                // If it's not a valid JSON array, create an empty array
                $training['attendees'] = json_encode([]);
                $needsUpdate = true;
            }

            // Check other JSON fields that might need fixing
            $jsonFields = ['locations', 'gps', 'content', 'trainers', 'materials'];
            foreach ($jsonFields as $field) {
                if (!empty($training[$field]) && !is_array(json_decode($training[$field], true))) {
                    // If it's not a valid JSON array/object, create an empty one
                    if ($field === 'locations') {
                        $training[$field] = json_encode(['venue' => '', 'location' => '']);
                    } elseif ($field === 'gps') {
                        $training[$field] = json_encode(['latitude' => '', 'longitude' => '']);
                    } elseif ($field === 'content') {
                        $training[$field] = json_encode(['modules' => '', 'activities' => '', 'notes' => '']);
                    } elseif ($field === 'materials') {
                        $training[$field] = json_encode(['materials' => '']);
                    } else {
                        $training[$field] = json_encode([]);
                    }
                    $needsUpdate = true;
                }
            }

            // Update the record if needed
            if ($needsUpdate) {
                $this->trainingModel->update($training['id'], $training);
            }
        }
    }

    /**
     * Display a listing of trainings
     */
    public function index()
    {
        $data = [
            'title' => 'Training Management',
            'trainings' => $this->trainingModel->where('district_id', session('district_id'))
                ->orderBy('created_at', 'DESC')
                ->findAll()
        ];

        return view('staff/staff_training/staff_training_index', $data);
    }

    /**
     * Show the form for creating a new training
     */
    public function new()
    {
        $data = [
            'title' => 'Create New Training',
        ];

        return view('staff/staff_training/staff_training_create', $data);
    }

    /**
     * Store a newly created training in database
     */
    public function create()
    {
        // Prepare data for insertion
        $data = [
            'country_id' => session('orgcountry_id'),
            'province_id' => session('orgprovince_id'),
            'district_id' => session('district_id'),
            'llg_id' => session('llg_id') ?? 0,
            'locations' => json_encode([
                'venue' => $this->request->getPost('venue'),
                'location' => $this->request->getPost('location')
            ]),
            'gps' => json_encode([
                'latitude' => $this->request->getPost('latitude'),
                'longitude' => $this->request->getPost('longitude')
            ]),
            'date_start' => $this->request->getPost('date_start'),
            'date_end' => $this->request->getPost('date_end'),
            'topic' => $this->request->getPost('topic'),
            'objectives' => $this->request->getPost('objectives'),
            'content' => json_encode([
                'modules' => $this->request->getPost('modules'),
                'activities' => $this->request->getPost('activities'),
                'notes' => $this->request->getPost('notes')
            ]),
            'trainers' => json_encode([]), // Empty array initially, will be added separately
            'attendees' => json_encode([]), // Empty array initially, will be added separately
            'materials' => json_encode([
                'materials' => $this->request->getPost('materials')
            ]),
            'status' => $this->request->getPost('status'),
            'created_by' => session('emp_id')
        ];

        // Save the training record
        if ($this->trainingModel->save($data)) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('success', 'Training created successfully');
        } else {
            return redirect()->back()
                ->with('errors', $this->trainingModel->errors())
                ->withInput();
        }
    }

    /**
     * Display the specified training
     */
    public function show($id = null)
    {
        $training = $this->trainingModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        $data = [
            'title' => 'Training Details',
            'training' => $training
        ];

        return view('staff/staff_training/staff_training_view', $data);
    }

    /**
     * Show the form for editing the specified training
     */
    public function edit($id = null)
    {
        $training = $this->trainingModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        $data = [
            'title' => 'Edit Training',
            'training' => $training
        ];

        return view('staff/staff_training/staff_training_edit', $data);
    }

    /**
     * Update the specified training in the database
     */
    public function update($id = null)
    {
        $training = $this->trainingModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Prepare data for update
        $data = [
            'id' => $id,
            'locations' => json_encode([
                'venue' => $this->request->getPost('venue'),
                'location' => $this->request->getPost('location')
            ]),
            'gps' => json_encode([
                'latitude' => $this->request->getPost('latitude'),
                'longitude' => $this->request->getPost('longitude')
            ]),
            'date_start' => $this->request->getPost('date_start'),
            'date_end' => $this->request->getPost('date_end'),
            'topic' => $this->request->getPost('topic'),
            'objectives' => $this->request->getPost('objectives'),
            'content' => json_encode([
                'modules' => $this->request->getPost('modules'),
                'activities' => $this->request->getPost('activities'),
                'notes' => $this->request->getPost('notes')
            ]),
            'materials' => json_encode([
                'materials' => $this->request->getPost('materials')
            ]),
            'status' => $this->request->getPost('status'),
            'updated_by' => session('emp_id')
        ];

        // Update the training record
        if ($this->trainingModel->save($data)) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('success', 'Training updated successfully');
        } else {
            return redirect()->back()
                ->with('errors', $this->trainingModel->errors())
                ->withInput();
        }
    }

    /**
     * Delete the specified training from database
     */
    public function delete($id = null)
    {
        $training = $this->trainingModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Perform soft delete
        if ($this->trainingModel->delete($id)) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('success', 'Training deleted successfully');
        } else {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Failed to delete training');
        }
    }

    /**
     * Delete the specified training using AJAX
     */
    public function ajaxDelete()
    {
        // Check if this is an AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        // Get the training ID from POST data
        $id = $this->request->getPost('id');

        if (!$id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No training ID provided'
            ]);
        }

        $training = $this->trainingModel->find($id);

        if (!$training) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Training not found'
            ]);
        }

        // Perform soft delete
        if ($this->trainingModel->delete($id)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Training deleted successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete training',
                'errors' => $this->trainingModel->errors()
            ]);
        }
    }

    /**
     * Display training participants
     */
    public function participants($id = null)
    {
        $training = $this->trainingModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Make sure attendees is an array
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        $data = [
            'title' => 'Training Participants',
            'training' => $training,
            'attendees' => $attendees
        ];

        return view('staff/staff_training/staff_training_participants', $data);
    }

    /**
     * Show form to add a new participant
     */
    public function newParticipant($id = null)
    {
        $training = $this->trainingModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        $data = [
            'title' => 'Add Participant',
            'training' => $training
        ];

        return view('staff/staff_training/staff_training_participant_create', $data);
    }

    /**
     * Add a participant to the training
     */
    public function addParticipant($id = null)
    {
        $training = $this->trainingModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Get existing attendees - safely decode
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        // Create new participant data
        $newParticipant = [
            'id' => uniqid(), // Generate a unique ID for the participant
            'name' => $this->request->getPost('name'),
            'type' => $this->request->getPost('type'),
            'farmer_id' => $this->request->getPost('farmer_id') ?? '',
            'gender' => $this->request->getPost('gender'),
            'age' => $this->request->getPost('age'),
            'phone' => $this->request->getPost('phone'),
            'email' => $this->request->getPost('email'),
            'added_at' => date('Y-m-d H:i:s')
        ];

        // Add to the array
        $attendees[] = $newParticipant;

        // Update the training record
        $data = [
            'id' => $id,
            'attendees' => json_encode($attendees),
            'updated_by' => session('emp_id')
        ];

        if ($this->trainingModel->save($data)) {
            return redirect()->to(base_url('staff/extension/trainings/participants/' . $id))
                ->with('success', 'Participant added successfully');
        } else {
            return redirect()->back()
                ->with('errors', $this->trainingModel->errors())
                ->withInput();
        }
    }

    /**
     * Show form to edit a participant
     */
    public function editParticipant($trainingId = null, $participantId = null)
    {
        $training = $this->trainingModel->find($trainingId);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Get existing attendees - safely decode
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        $participant = null;

        // Find the specific participant
        foreach ($attendees as $attendee) {
            if ($attendee['id'] == $participantId) {
                $participant = $attendee;
                break;
            }
        }

        if (!$participant) {
            return redirect()->to(base_url('staff/extension/trainings/participants/' . $trainingId))
                ->with('error', 'Participant not found');
        }

        $data = [
            'title' => 'Edit Participant',
            'training' => $training,
            'participant' => $participant
        ];

        return view('staff/staff_training/staff_training_participant_edit', $data);
    }

    /**
     * Update a participant's information
     */
    public function updateParticipant($trainingId = null, $participantId = null)
    {
        $training = $this->trainingModel->find($trainingId);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Get existing attendees - safely decode
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        $updated = false;

        // Update the specific participant
        foreach ($attendees as $key => $attendee) {
            if ($attendee['id'] == $participantId) {
                $attendees[$key] = [
                    'id' => $participantId,
                    'name' => $this->request->getPost('name'),
                    'type' => $this->request->getPost('type'),
                    'farmer_id' => $this->request->getPost('farmer_id') ?? '',
                    'gender' => $this->request->getPost('gender'),
                    'age' => $this->request->getPost('age'),
                    'phone' => $this->request->getPost('phone'),
                    'email' => $this->request->getPost('email'),
                    'added_at' => $attendee['added_at']
                ];
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            return redirect()->to(base_url('staff/extension/trainings/participants/' . $trainingId))
                ->with('error', 'Participant not found');
        }

        // Update the training record
        $data = [
            'id' => $trainingId,
            'attendees' => json_encode($attendees),
            'updated_by' => session('emp_id')
        ];

        if ($this->trainingModel->save($data)) {
            return redirect()->to(base_url('staff/extension/trainings/participants/' . $trainingId))
                ->with('success', 'Participant updated successfully');
        } else {
            return redirect()->back()
                ->with('errors', $this->trainingModel->errors())
                ->withInput();
        }
    }

    /**
     * Delete a participant from the training
     */
    public function deleteParticipant($trainingId = null, $participantId = null)
    {
        $training = $this->trainingModel->find($trainingId);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Get existing attendees - safely decode
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        $updated = false;

        // Remove the specific participant
        foreach ($attendees as $key => $attendee) {
            if ($attendee['id'] == $participantId) {
                unset($attendees[$key]);
                $attendees = array_values($attendees); // Reindex array
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            return redirect()->to(base_url('staff/extension/trainings/participants/' . $trainingId))
                ->with('error', 'Participant not found');
        }

        // Update the training record
        $data = [
            'id' => $trainingId,
            'attendees' => json_encode($attendees),
            'updated_by' => session('emp_id')
        ];

        if ($this->trainingModel->save($data)) {
            return redirect()->to(base_url('staff/extension/trainings/participants/' . $trainingId))
                ->with('success', 'Participant removed successfully');
        } else {
            return redirect()->to(base_url('staff/extension/trainings/participants/' . $trainingId))
                ->with('error', 'Failed to remove participant');
        }
    }

    /**
     * Export training data to CSV
     */
    public function exportTrainings()
    {
        // Get all trainings for the current district
        $trainings = $this->trainingModel->where('district_id', session('district_id'))
            ->orderBy('created_at', 'DESC')
            ->findAll();

        // Set the CSV header
        $filename = 'trainings_export_' . date('Y-m-d') . '.csv';
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        // Open the output stream
        $output = fopen('php://output', 'w');

        // Set column headers
        fputcsv($output, [
            'ID',
            'Topic',
            'Venue',
            'Location',
            'Start Date',
            'End Date',
            'Objectives',
            'Modules',
            'Status',
            'Number of Participants',
            'Created Date'
        ]);

        // Add data rows
        foreach ($trainings as $training) {
            // Parse JSON data
            $locations = json_decode($training['locations'], true);
            $content = json_decode($training['content'], true);
            $attendees = json_decode($training['attendees'], true);

            // Determine status
            $statuses = [
                '1' => 'Completed',
                '2' => 'Ongoing',
                '3' => 'Scheduled',
                '4' => 'Cancelled'
            ];
            $status = $statuses[$training['status']] ?? 'Unknown';

            // Count participants
            $participantCount = is_array($attendees) ? count($attendees) : 0;

            // Add row
            fputcsv($output, [
                $training['id'],
                $training['topic'],
                $locations['venue'] ?? 'N/A',
                $locations['location'] ?? 'N/A',
                date('Y-m-d', strtotime($training['date_start'])),
                date('Y-m-d', strtotime($training['date_end'])),
                $training['objectives'],
                $content['modules'] ?? 'N/A',
                $status,
                $participantCount,
                date('Y-m-d', strtotime($training['created_at']))
            ]);
        }

        fclose($output);
        exit;
    }

    /**
     * Export training participants to CSV
     */
    public function exportParticipants($id = null)
    {
        $training = $this->trainingModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Parse attendees data
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        // Set the CSV header
        $topic = preg_replace('/[^A-Za-z0-9_\-]/', '', $training['topic']); // Sanitize filename
        $filename = 'training_participants_' . $topic . '_' . date('Y-m-d') . '.csv';
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        // Open the output stream
        $output = fopen('php://output', 'w');

        // Set column headers
        fputcsv($output, [
            'Name',
            'Type',
            'Farmer ID',
            'Gender',
            'Age',
            'Phone',
            'Email',
            'Date Added'
        ]);

        // Add data rows
        foreach ($attendees as $attendee) {
            fputcsv($output, [
                $attendee['name'] ?? 'N/A',
                $attendee['type'] ?? 'N/A',
                $attendee['farmer_id'] ?? 'N/A',
                $attendee['gender'] ?? 'N/A',
                $attendee['age'] ?? 'N/A',
                $attendee['phone'] ?? 'N/A',
                $attendee['email'] ?? 'N/A',
                isset($attendee['added_at']) ? date('Y-m-d', strtotime($attendee['added_at'])) : 'N/A'
            ]);
        }

        fclose($output);
        exit;
    }

    /**
     * Add a participant using AJAX
     */
    public function ajaxAddParticipant()
    {
        // Check if this is an AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        // Get the training ID and participant data
        $trainingId = $this->request->getPost('training_id');

        if (!$trainingId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No training ID provided'
            ]);
        }

        $training = $this->trainingModel->find($trainingId);

        if (!$training) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Training not found'
            ]);
        }

        // Get existing attendees - safely decode
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        // Create new participant data
        $newParticipant = [
            'id' => uniqid(), // Generate a unique ID for the participant
            'name' => $this->request->getPost('name'),
            'type' => $this->request->getPost('type'),
            'farmer_id' => $this->request->getPost('farmer_id') ?? '',
            'gender' => $this->request->getPost('gender'),
            'age' => $this->request->getPost('age'),
            'phone' => $this->request->getPost('phone'),
            'email' => $this->request->getPost('email'),
            'added_at' => date('Y-m-d H:i:s')
        ];

        // Validate required fields
        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|min_length[3]|max_length[255]',
            'type' => 'required',
            'gender' => 'required',
            'age' => 'required|numeric|greater_than[0]|less_than_equal_to[120]'
        ]);

        $validData = [
            'name' => $newParticipant['name'],
            'type' => $newParticipant['type'],
            'gender' => $newParticipant['gender'],
            'age' => $newParticipant['age']
        ];

        if (!$validation->run($validData)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        // Add to the array
        $attendees[] = $newParticipant;

        // Update the training record
        $data = [
            'id' => $trainingId,
            'attendees' => json_encode($attendees),
            'updated_by' => session('emp_id')
        ];

        if ($this->trainingModel->save($data)) {
            // Format the row HTML for the response
            $rowHtml = $this->formatParticipantRow($newParticipant, $trainingId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Participant added successfully',
                'participant' => $newParticipant,
                'rowHtml' => $rowHtml
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to add participant',
                'errors' => $this->trainingModel->errors()
            ]);
        }
    }

    /**
     * Format a participant row for AJAX response
     */
    private function formatParticipantRow($participant, $trainingId)
    {
        $contacts = [];
        if (!empty($participant['phone'])) {
            $contacts[] = '<i class="fas fa-phone me-1"></i> ' . esc($participant['phone']);
        }
        if (!empty($participant['email'])) {
            $contacts[] = '<i class="fas fa-envelope me-1"></i> ' . esc($participant['email']);
        }
        $contactHtml = !empty($contacts) ? implode('<br>', $contacts) : 'N/A';

        $dateAdded = !empty($participant['added_at']) ? date('d-M-Y', strtotime($participant['added_at'])) : 'N/A';

        return '<tr id="participant-row-' . $participant['id'] . '">
            <td>
                <div class="form-check">
                    <input class="form-check-input participant-checkbox" type="checkbox"
                           data-id="' . $participant['id'] . '"
                           data-name="' . esc($participant['name']) . '">
                </div>
            </td>
            <td>' . esc($participant['name'] ?? 'N/A') . '</td>
            <td>' . esc($participant['type'] ?? 'N/A') . '</td>
            <td>' . (!empty($participant['farmer_id']) ? esc($participant['farmer_id']) : 'N/A') . '</td>
            <td>' . esc($participant['gender'] ?? 'N/A') . '</td>
            <td>' . esc($participant['age'] ?? 'N/A') . '</td>
            <td>' . $contactHtml . '</td>
            <td>' . $dateAdded . '</td>
            <td>
                <div class="btn-group">
                    <a href="' . base_url('staff/extension/trainings/participants/edit/' . $trainingId . '/' . $participant['id']) . '"
                       class="btn btn-sm btn-warning">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button type="button"
                       class="btn btn-sm btn-danger ajax-delete-participant-btn"
                       data-id="' . $participant['id'] . '"
                       data-name="' . esc($participant['name']) . '">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>';
    }

    /**
     * Delete a participant using AJAX
     */
    public function ajaxDeleteParticipant()
    {
        // Check if this is an AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        // Get the training ID and participant ID
        $trainingId = $this->request->getPost('training_id');
        $participantId = $this->request->getPost('participant_id');

        if (!$trainingId || !$participantId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Training ID and Participant ID are required'
            ]);
        }

        $training = $this->trainingModel->find($trainingId);

        if (!$training) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Training not found'
            ]);
        }

        // Get existing attendees - safely decode
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        $updated = false;
        $removedParticipant = null;

        // Remove the specific participant
        foreach ($attendees as $key => $attendee) {
            if ($attendee['id'] == $participantId) {
                $removedParticipant = $attendee;
                unset($attendees[$key]);
                $attendees = array_values($attendees); // Reindex array
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Participant not found'
            ]);
        }

        // Update the training record
        $data = [
            'id' => $trainingId,
            'attendees' => json_encode($attendees),
            'updated_by' => session('emp_id')
        ];

        if ($this->trainingModel->save($data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Participant removed successfully',
                'participant' => $removedParticipant
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to remove participant',
                'errors' => $this->trainingModel->errors()
            ]);
        }
    }

    /**
     * Show a dashboard with training statistics
     */
    public function dashboard()
    {
        // Get all trainings for the current district
        $trainings = $this->trainingModel->where('district_id', session('district_id'))
            ->findAll();

        // Initialize counters
        $totalTrainings = count($trainings);
        $totalParticipants = 0;
        $maleParticipants = 0;
        $femaleParticipants = 0;
        $otherParticipants = 0;
        $statusCounts = [
            '1' => 0, // Completed
            '2' => 0, // Ongoing
            '3' => 0, // Scheduled
            '4' => 0  // Cancelled
        ];
        $monthlyTrainings = [];
        $locationTrainings = [];
        $topicCounts = [];

        // Current year
        $currentYear = date('Y');

        // Process training data for statistics
        foreach ($trainings as $training) {
            // Count by status
            $status = $training['status'];
            if (isset($statusCounts[$status])) {
                $statusCounts[$status]++;
            }

            // Count participants and gender breakdown
            $attendees = json_decode($training['attendees'], true);
            if (is_array($attendees)) {
                $totalParticipants += count($attendees);

                // Count by gender
                foreach ($attendees as $attendee) {
                    $gender = isset($attendee['gender']) ? strtolower($attendee['gender']) : '';
                    if ($gender === 'male') {
                        $maleParticipants++;
                    } elseif ($gender === 'female') {
                        $femaleParticipants++;
                    } else {
                        $otherParticipants++;
                    }
                }
            }

            // Group by month (for current year)
            $trainingYear = date('Y', strtotime($training['date_start']));
            if ($trainingYear == $currentYear) {
                $month = date('n', strtotime($training['date_start']));
                if (!isset($monthlyTrainings[$month])) {
                    $monthlyTrainings[$month] = 0;
                }
                $monthlyTrainings[$month]++;
            }

            // Group by location
            $locations = json_decode($training['locations'], true);
            $location = $locations['location'] ?? 'Unknown';
            if (!isset($locationTrainings[$location])) {
                $locationTrainings[$location] = 0;
            }
            $locationTrainings[$location]++;

            // Group by topic category (simple implementation)
            $topic = $training['topic'];
            $topicCategory = $this->categorizeTopicSimple($topic);
            if (!isset($topicCounts[$topicCategory])) {
                $topicCounts[$topicCategory] = 0;
            }
            $topicCounts[$topicCategory]++;
        }

        // Fill in missing months with zeros
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($monthlyTrainings[$i])) {
                $monthlyTrainings[$i] = 0;
            }
        }
        ksort($monthlyTrainings); // Sort by month number

        // Sort locations and get top 5
        arsort($locationTrainings);
        $locationTrainings = array_slice($locationTrainings, 0, 5);

        // Sort topics and get top 5
        arsort($topicCounts);
        $topicCounts = array_slice($topicCounts, 0, 5);

        // Recent trainings (latest 5)
        $recentTrainings = $this->trainingModel->where('district_id', session('district_id'))
            ->orderBy('created_at', 'DESC')
            ->limit(5)
            ->findAll();

        // Calculate gender percentages
        $malePercentage = $totalParticipants > 0 ? round(($maleParticipants / $totalParticipants) * 100, 1) : 0;
        $femalePercentage = $totalParticipants > 0 ? round(($femaleParticipants / $totalParticipants) * 100, 1) : 0;
        $otherPercentage = $totalParticipants > 0 ? round(($otherParticipants / $totalParticipants) * 100, 1) : 0;

        // Prepare data for view
        $data = [
            'title' => 'Training Dashboard',
            'totalTrainings' => $totalTrainings,
            'totalParticipants' => $totalParticipants,
            'statusCounts' => $statusCounts,
            'monthlyTrainings' => $monthlyTrainings,
            'locationTrainings' => $locationTrainings,
            'topicCounts' => $topicCounts,
            'recentTrainings' => $recentTrainings,
            'averageParticipants' => $totalTrainings > 0 ? round($totalParticipants / $totalTrainings, 1) : 0,
            'maleParticipants' => $maleParticipants,
            'femaleParticipants' => $femaleParticipants,
            'otherParticipants' => $otherParticipants,
            'malePercentage' => $malePercentage,
            'femalePercentage' => $femalePercentage,
            'otherPercentage' => $otherPercentage
        ];

        return view('staff/staff_training/staff_training_dashboard', $data);
    }

    /**
     * Simple categorization of training topics
     */
    private function categorizeTopicSimple($topic)
    {
        $topic = strtolower($topic);

        // Define some common categories and keywords
        $categories = [
            'Crop Management' => ['crop', 'planting', 'harvest', 'seed', 'irrigation', 'farming'],
            'Livestock' => ['animal', 'livestock', 'cattle', 'goat', 'pig', 'poultry', 'sheep'],
            'Pest Control' => ['pest', 'disease', 'insect', 'fungicide', 'herbicide', 'weed'],
            'Soil Health' => ['soil', 'fertilizer', 'compost', 'nutrition', 'fertility'],
            'Business Skills' => ['market', 'business', 'finance', 'profit', 'sale', 'economic', 'budget'],
            'Technology' => ['technology', 'digital', 'mobile', 'app', 'computer', 'data'],
            'Climate' => ['climate', 'weather', 'adaptation', 'resilience', 'drought', 'flood']
        ];

        foreach ($categories as $category => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($topic, $keyword) !== false) {
                    return $category;
                }
            }
        }

        return 'Other'; // Default category if no match
    }

    /**
     * Batch delete participants using AJAX
     */
    public function ajaxBatchDeleteParticipants()
    {
        // Check if this is an AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        // Get the training ID and participant IDs
        $trainingId = $this->request->getPost('training_id');
        $participantIds = $this->request->getPost('participant_ids');

        if (!$trainingId || !$participantIds || !is_array($participantIds)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Training ID and Participant IDs are required'
            ]);
        }

        $training = $this->trainingModel->find($trainingId);

        if (!$training) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Training not found'
            ]);
        }

        // Get existing attendees - safely decode
        $attendees = [];
        if (!empty($training['attendees'])) {
            $decoded = json_decode($training['attendees'], true);
            if (is_array($decoded)) {
                $attendees = $decoded;
            }
        }

        $removed = 0;
        $removedParticipants = [];
        $remaining = [];

        // Filter out the participants to be deleted
        foreach ($attendees as $attendee) {
            if (in_array($attendee['id'], $participantIds)) {
                $removedParticipants[] = $attendee;
                $removed++;
            } else {
                $remaining[] = $attendee;
            }
        }

        if ($removed === 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No participants found to delete'
            ]);
        }

        // Update the training record with remaining participants
        $data = [
            'id' => $trainingId,
            'attendees' => json_encode($remaining),
            'updated_by' => session('emp_id')
        ];

        if ($this->trainingModel->save($data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => $removed . ' participant(s) removed successfully',
                'removedCount' => $removed,
                'removedIds' => $participantIds
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to remove participants',
                'errors' => $this->trainingModel->errors()
            ]);
        }
    }

    /**
     * Save all participants at once
     */
    public function batchSaveParticipants($id = null)
    {
        // Check if this is an AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        $training = $this->trainingModel->find($id);

        if (!$training) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Training not found'
            ]);
        }

        // Get the participants data from the form
        $participants = $this->request->getPost('participants');

        if (!is_array($participants)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid participants data'
            ]);
        }

        // Validate each participant
        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|min_length[3]|max_length[255]',
            'type' => 'required',
            'gender' => 'required',
            'age' => 'required|numeric|greater_than[0]|less_than_equal_to[120]'
        ]);

        $validParticipants = [];
        $hasErrors = false;
        $errorMessages = [];

        foreach ($participants as $index => $participant) {
            if (!$validation->run($participant)) {
                $hasErrors = true;
                $errorMessages["Row " . ($index + 1)] = $validation->getErrors();
            }

            // Add required fields
            $participant['id'] = !empty($participant['id']) ? $participant['id'] : uniqid();
            $participant['added_at'] = date('Y-m-d H:i:s');

            // Clean up farmer_id if type is not Farmer
            if ($participant['type'] !== 'Farmer') {
                $participant['farmer_id'] = '';
            }

            $validParticipants[] = $participant;
        }

        if ($hasErrors) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $errorMessages
            ]);
        }

        // Update the training record with the new participants
        $data = [
            'id' => $id,
            'attendees' => json_encode($validParticipants),
            'updated_by' => session('emp_id')
        ];

        if ($this->trainingModel->save($data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'All participants saved successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to save participants',
                'errors' => $this->trainingModel->errors()
            ]);
        }
    }
}