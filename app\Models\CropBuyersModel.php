<?php

namespace App\Models;

use CodeIgniter\Model;

class CropBuyersModel extends Model
{
    protected $table = 'crop_buyers';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    
    protected $allowedFields = [
        'crop_id',
        'buyer_code',
        'name',
        'address',
        'contact_number',
        'email',
        'operation_span', //local, national - local is province wide, national is country wide
        'location_id', //province id if local, country id if national
        'description',
        'created_by',
        'updated_by',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

   
} 