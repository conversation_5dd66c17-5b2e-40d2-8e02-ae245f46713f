<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmPesticidesDataModel extends Model
{
    protected $table = 'crops_farm_pesticides_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'exercise_id',
        'block_id',
        'pesticide_id',
        'crop_id',
        'name',
        'brand',
        'unit_of_measure',
        'unit',
        'quantity',
        'action_date',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $skipValidation = true;
    protected $cleanValidationRules = true;

    public function getPesticidesReportData()
    {
        return $this->select('
            crops_farm_pesticides_data.*,
            crops_farm_blocks.block_code,
            crops_farm_blocks.block_site,
            crops_farm_blocks.status as block_status,
            farmer_information.given_name,
            farmer_information.surname,
            adx_crops.crop_name,
            adx_crops.crop_color_code,
            adx_district.name as district_name,
            adx_llg.name as llg_name,
            adx_ward.name as ward_name,
            adx_pesticides.name as pesticide_name
        ')
        ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_pesticides_data.block_id')
        ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
        ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
        ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
        ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
        ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
        ->join('adx_pesticides', 'adx_pesticides.id = crops_farm_pesticides_data.pesticide_id', 'left')
        ->where('crops_farm_pesticides_data.status', 'active')
        ->where('crops_farm_blocks.status', 'active')
        ->where('crops_farm_blocks.district_id', session()->get('district_id'))
        ->findAll();
    }
}
