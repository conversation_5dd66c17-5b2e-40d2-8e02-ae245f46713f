<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_title ?></h1>
            </div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="<?= base_url('dashboards/farmers') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <!-- Total Crop Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-primary">
                    <div class="inner">
                        <h3><?= $farmer['crops_blocks_count'] ?? 0 ?></h3>
                        <p>Crop Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <!-- Total Hectares Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= number_format($farmer['total_hectares'] ?? 0, 2) ?></h3>
                        <p>Total Hectares</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                </div>
            </div>
            <!-- Total Revenue Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?= number_format($farmer['total_crops_revenue'] ?? 0, 2) ?></h3>
                        <p>Total Revenue (<?= COUNTRY_CURRENCY ?>)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
            <!-- Livestock Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?= $farmer['livestock_blocks_count'] ?? 0 ?></h3>
                        <p>Livestock Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-cow"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farmer Details Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Farmer Details</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5 class="mb-3">Personal Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="150">Farmer Code</th>
                                <td><?= $farmer['farmer_code'] ?></td>
                            </tr>
                            <tr>
                                <th>Given Name</th>
                                <td><?= $farmer['given_name'] ?></td>
                            </tr>
                            <tr>
                                <th>Surname</th>
                                <td><?= $farmer['surname'] ?></td>
                            </tr>
                            <tr>
                                <th>Gender</th>
                                <td><?= $farmer['gender'] ?></td>
                            </tr>
                            <tr>
                                <th>Date of Birth</th>
                                <td><?= $farmer['date_of_birth'] ?></td>
                            </tr>
                            <tr>
                                <th>Age</th>
                                <td><?= !empty($farmer['date_of_birth']) ? date_diff(date_create($farmer['date_of_birth']), date_create('today'))->y : 'N/A' ?></td>
                            </tr>
                            <tr>
                                <th>Marital Status</th>
                                <td><?= ucfirst($farmer['marital_status'] ?? 'Not Specified') ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        <h5 class="mb-3">Contact & Education</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="150">Phone</th>
                                <td><?= $farmer['phone'] ?? 'Not Specified' ?></td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td><?= $farmer['email'] ?? 'Not Specified' ?></td>
                            </tr>
                            <tr>
                                <th>Address</th>
                                <td><?= $farmer['address'] ?? 'Not Specified' ?></td>
                            </tr>
                            <tr>
                                <th>Education Level</th>
                                <td><?= $farmer['highest_education'] ?? 'Not Specified' ?></td>
                            </tr>
                            <tr>
                                <th>Course Taken</th>
                                <td><?= $farmer['course_taken'] ?? 'Not Specified' ?></td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td><span class="badge bg-<?= $farmer['status'] === 'active' ? 'success' : 'danger' ?>"><?= ucfirst($farmer['status']) ?></span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        <h5 class="mb-3">Location Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <th width="150">Country</th>
                                <td><?= $farmer['country_name'] ?? 'Not Specified' ?></td>
                            </tr>
                            <tr>
                                <th>Province</th>
                                <td><?= $farmer['province_name'] ?></td>
                            </tr>
                            <tr>
                                <th>District</th>
                                <td><?= $farmer['district_name'] ?></td>
                            </tr>
                            <tr>
                                <th>LLG</th>
                                <td><?= $farmer['llg_name'] ?></td>
                            </tr>
                            <tr>
                                <th>Ward</th>
                                <td><?= $farmer['ward_name'] ?></td>
                            </tr>
                            <tr>
                                <th>Village</th>
                                <td><?= $farmer['village'] ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <?php if (!empty($farmer['id_photo'])): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h5>ID Photo</h5>
                        <img src="<?= base_url('uploads/farmers/' . $farmer['id_photo']) ?>" alt="Farmer ID Photo" class="img-thumbnail" style="max-width: 200px;">
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Children Information Card -->
        <?php if (!empty($children)): ?>
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Children Information</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Gender</th>
                                <th>Date of Birth</th>
                                <th>Age</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($children as $child): ?>
                                <tr>
                                    <td><?= $child['name'] ?></td>
                                    <td><?= $child['gender'] ?></td>
                                    <td><?= $child['date_of_birth'] ?></td>
                                    <td><?= date_diff(date_create($child['date_of_birth']), date_create('today'))->y ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Crop Information Row -->
        <div class="row">
            <!-- Crop Types Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Types Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropTypesChart" style="min-height: 250px;"></canvas>
                    </div>
                </div>
            </div>
            <!-- Hectares by Crop Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Hectares by Crop Type</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="hectaresChart" style="min-height: 250px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Livestock Information Row -->
        <div class="row">
            <!-- Livestock Types Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Livestock Types Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="livestockTypesChart" style="min-height: 250px;"></canvas>
                    </div>
                </div>
            </div>
            <!-- Livestock Value Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Livestock Value Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="livestockValueChart" style="min-height: 250px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crop Blocks Details -->
        <?php if (!empty($crop_blocks)): ?>
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Crop Blocks Details</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="cropBlocksTable">
                        <thead>
                            <tr>
                                <th>Block Code</th>
                                <th>Crop</th>
                                <th>Hectares</th>
                                <th>Province</th>
                                <th>District</th>
                                <th>LLG</th>
                                <th>Ward</th>
                                <th>Village</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($crop_blocks as $block): ?>
                                <tr>
                                    <td><?= $block['block_code'] ?></td>
                                    <td>
                                        <span class="badge" style="background-color: <?= $block['crop_color_code'] ?>">
                                            <?= $block['crop_name'] ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($block['hectares'], 2) ?></td>
                                    <td><?= $block['province_name'] ?></td>
                                    <td><?= $block['district_name'] ?></td>
                                    <td><?= $block['llg_name'] ?></td>
                                    <td><?= $block['ward_name'] ?></td>
                                    <td><?= $block['village'] ?></td>
                                    <td><?= ucfirst($block['status']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Livestock Blocks Details -->
        <?php if (!empty($livestock_blocks)): ?>
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Livestock Blocks Details</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="livestockBlocksTable">
                        <thead>
                            <tr>
                                <th>Block Code</th>
                                <th>Livestock</th>
                                <th>Quantity</th>
                                <th>Cost per Unit (<?= COUNTRY_CURRENCY ?>)</th>
                                <th>Total Value (<?= COUNTRY_CURRENCY ?>)</th>
                                <th>Province</th>
                                <th>District</th>
                                <th>LLG</th>
                                <th>Ward</th>
                                <th>Village</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($livestock_blocks as $block): ?>
                                <tr>
                                    <td><?= $block['block_code'] ?></td>
                                    <td>
                                        <span class="badge" style="background-color: <?= $block['livestock_color_code'] ?>">
                                            <?= $block['livestock_name'] ?>
                                        </span>
                                    </td>
                                    <td><?= $block['quantity'] ?></td>
                                    <td><?= number_format($block['cost_per_livestock'], 2) ?></td>
                                    <td><?= number_format($block['quantity'] * $block['cost_per_livestock'], 2) ?></td>
                                    <td><?= $block['province_name'] ?></td>
                                    <td><?= $block['district_name'] ?></td>
                                    <td><?= $block['llg_name'] ?></td>
                                    <td><?= $block['ward_name'] ?></td>
                                    <td><?= $block['village'] ?></td>
                                    <td><?= ucfirst($block['status']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Initialize DataTables and Charts -->
<script>
$(function() {
    // Initialize DataTables
    $('#cropBlocksTable, #livestockBlocksTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print"]
    });

    // Prepare data for Crop Types Chart
    const cropData = {
        labels: <?= json_encode(array_column($crop_blocks, 'crop_name')) ?>,
        datasets: [{
            data: <?= json_encode(array_column($crop_blocks, 'hectares')) ?>,
            backgroundColor: <?= json_encode(array_column($crop_blocks, 'crop_color_code')) ?>
        }]
    };

    // Create Crop Types Chart
    new Chart(document.getElementById('cropTypesChart'), {
        type: 'pie',
        data: cropData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Prepare data for Hectares Chart
    const hectaresData = {
        labels: <?= json_encode(array_column($crop_blocks, 'crop_name')) ?>,
        datasets: [{
            label: 'Hectares',
            data: <?= json_encode(array_column($crop_blocks, 'hectares')) ?>,
            backgroundColor: <?= json_encode(array_column($crop_blocks, 'crop_color_code')) ?>,
            borderColor: <?= json_encode(array_column($crop_blocks, 'crop_color_code')) ?>,
            borderWidth: 1
        }]
    };

    // Create Hectares Chart
    new Chart(document.getElementById('hectaresChart'), {
        type: 'bar',
        data: hectaresData,
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Prepare data for Livestock Types Chart
    const livestockData = {
        labels: <?= json_encode(array_column($livestock_blocks, 'livestock_name')) ?>,
        datasets: [{
            data: <?= json_encode(array_column($livestock_blocks, 'quantity')) ?>,
            backgroundColor: <?= json_encode(array_column($livestock_blocks, 'livestock_color_code')) ?>
        }]
    };

    // Create Livestock Types Chart
    new Chart(document.getElementById('livestockTypesChart'), {
        type: 'pie',
        data: livestockData,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Prepare data for Livestock Value Chart
    const livestockValueData = {
        labels: <?= json_encode(array_column($livestock_blocks, 'livestock_name')) ?>,
        datasets: [{
            label: 'Total Value',
            data: <?= json_encode(array_map(function($block) {
                return $block['quantity'] * $block['cost_per_livestock'];
            }, $livestock_blocks)) ?>,
            backgroundColor: <?= json_encode(array_column($livestock_blocks, 'livestock_color_code')) ?>,
            borderColor: <?= json_encode(array_column($livestock_blocks, 'livestock_color_code')) ?>,
            borderWidth: 1
        }]
    };

    // Create Livestock Value Chart
    new Chart(document.getElementById('livestockValueChart'), {
        type: 'bar',
        data: livestockValueData,
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});
</script>
<?= $this->endSection() ?> 