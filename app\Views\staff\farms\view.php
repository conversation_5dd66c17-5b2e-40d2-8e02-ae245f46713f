<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Farms List</h5>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="farmsTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Farmer Code</th>
                        <th>Farmer Name</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($farmers as $index => $farmer): ?>
                    <tr>
                        <td><?= $index + 1 ?></td>
                        <td><?= esc($farmer['farmer_code']) ?></td>
                        <td><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></td>
                        <td>
                            <a href="<?= base_url('staff/farms/farm_blocks/' . $farmer['id']) ?>" 
                               class="btn btn-sm btn-info">
                               View Farms
                                <i class="fas fa-eye"></i>
                            </a>
                            
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        $('#farmsTable').DataTable({
            "responsive": true,
            "order": [[1, "asc"]]
        });
    });
</script>
<?= $this->endSection() ?> 