<?php

namespace App\Models;

use CodeIgniter\Model;

class DocumentsFolderModel extends Model
{
    protected $table            = 'documents_folder';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    protected $allowedFields = [
        'parent_folder_id',
        'folder_name',
        'description',
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'folder_name'   => 'required|min_length[2]|max_length[255]',
        'country_id'    => 'permit_empty|numeric',
        'province_id'   => 'permit_empty|numeric',
        'district_id'   => 'permit_empty|numeric',
        'llg_id'        => 'permit_empty|numeric',
        'status'        => 'permit_empty|numeric',
        'created_by'    => 'permit_empty|numeric'
    ];

    protected $validationMessages = [
        'folder_name' => [
            'required'   => 'Folder name is required',
            'min_length' => 'Folder name must be at least 2 characters long',
            'max_length' => 'Folder name cannot exceed 255 characters'
        ],
        'country_id' => [
            'required' => 'Country ID is required',
            'numeric'  => 'Country ID must be a number'
        ],
        'province_id' => [
            'required' => 'Province ID is required',
            'numeric'  => 'Province ID must be a number'
        ]
    ];

    protected $skipValidation     = false;
    protected $cleanValidationRules = true;

    /**
     * Get folders with parent folder name
     *
     * @return array
     */
    public function getFoldersWithParent()
    {
        return $this->select('documents_folder.*, parent.folder_name as parent_folder_name')
            ->join('documents_folder as parent', 'parent.id = documents_folder.parent_folder_id', 'left')
            ->where('documents_folder.deleted_at IS NULL')
            ->findAll();
    }

    /**
     * Get folders by location
     *
     * @param int $countryId
     * @param int $provinceId
     * @param int|null $districtId
     * @param int|null $llgId
     * @return array
     */
    public function getFoldersByLocation($countryId, $provinceId, $districtId = null, $llgId = null)
    {
        $builder = $this->where('country_id', $countryId)
                        ->where('province_id', $provinceId)
                        ->where('deleted_at IS NULL');

        if ($districtId) {
            $builder->where('district_id', $districtId);
        }

        if ($llgId) {
            $builder->where('llg_id', $llgId);
        }

        return $builder->findAll();
    }
}
