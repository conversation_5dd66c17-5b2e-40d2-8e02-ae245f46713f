<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Helpers\DummyDataHelper;

class Staff_Reports extends BaseController
{
    public function __construct()
    {
        helper(['form', 'url']);
    }

    /**
     * Farmers Report
     */
    public function farmers()
    {
        $farmers = DummyDataHelper::getFarmers();
        $stats = DummyDataHelper::getFarmersReport();

        $data = [
            'title' => 'Farmers Report',
            'page_header' => 'Farmers Report',
            'farmers' => $farmers,
            'stats' => $stats,
            'total_farmers' => count($farmers)
        ];

        return view('staff/reports/farmers', $data);
    }

    /**
     * Individual Farmer Profile
     */
    public function farmer_profile($id)
    {
        $farmers = DummyDataHelper::getFarmers();
        $farmer = null;
        
        foreach ($farmers as $f) {
            if ($f['id'] == $id) {
                $farmer = $f;
                break;
            }
        }
        
        if (!$farmer) {
            session()->setFlashdata('error', 'Farmer not found');
            return redirect()->to('staff/reports/farmers');
        }

        $data = [
            'title' => 'Farmer Profile - ' . $farmer['full_name'],
            'page_header' => 'Farmer Profile Report',
            'farmer' => $farmer,
            'crop_blocks' => array_filter(DummyDataHelper::getCropFarmBlocks(), fn($b) => $b['farmer_id'] == $id),
            'livestock_blocks' => array_filter(DummyDataHelper::getLivestockFarmBlocks(), fn($b) => $b['farmer_id'] == $id)
        ];

        return view('staff/reports/farmer_profile', $data);
    }

    /**
     * Crops Report
     */
    public function crops()
    {
        $data = [
            'title' => 'Crops Report',
            'page_header' => 'Crops Production Report',
            'crops' => DummyDataHelper::getCrops(),
            'crop_blocks' => DummyDataHelper::getCropFarmBlocks(),
            'total_blocks' => count(DummyDataHelper::getCropFarmBlocks()),
            'total_hectares' => 15.2,
            'total_plants' => 2840
        ];

        return view('staff/reports/crops', $data);
    }

    /**
     * Blocks Report
     */
    public function blocks()
    {
        $cropBlocks = DummyDataHelper::getCropFarmBlocks();

        $data = [
            'title' => 'Farm Blocks Report',
            'page_header' => 'Farm Blocks Report',
            'blocks' => $cropBlocks,
            'total_blocks' => count($cropBlocks),
            'crops' => DummyDataHelper::getCrops()
        ];

        return view('staff/reports/blocks', $data);
    }

    /**
     * Block Profile Report
     */
    public function block_profile($id)
    {
        $cropBlocks = DummyDataHelper::getCropFarmBlocks();
        $block = null;
        
        foreach ($cropBlocks as $b) {
            if ($b['id'] == $id) {
                $block = $b;
                break;
            }
        }
        
        if (!$block) {
            session()->setFlashdata('error', 'Block not found');
            return redirect()->to('staff/reports/blocks');
        }

        // Generate detailed block data
        $cropsData = [
            ['crop_name' => $block['crop_name'], 'plants' => 500, 'hectares' => 2.5, 'date' => '2024-01-15'],
            ['crop_name' => $block['crop_name'], 'plants' => 200, 'hectares' => 1.0, 'date' => '2024-02-10'],
        ];

        $data = [
            'title' => 'Block Profile - ' . $block['block_code'],
            'page_header' => 'Farm Block Profile Report',
            'block' => $block,
            'crops_data' => $cropsData,
            'total_plants' => 700,
            'total_hectares' => 3.5
        ];

        return view('staff/reports/block_profile', $data);
    }

    /**
     * Livestock Blocks Report
     */
    public function livestock_blocks()
    {
        $livestockBlocks = DummyDataHelper::getLivestockFarmBlocks();

        $data = [
            'title' => 'Livestock Blocks Report',
            'page_header' => 'Livestock Farm Blocks Report',
            'blocks' => $livestockBlocks,
            'total_blocks' => count($livestockBlocks),
            'livestock_types' => DummyDataHelper::getLivestock()
        ];

        return view('staff/reports/livestock_blocks', $data);
    }

    /**
     * Livestock Data Report
     */
    public function livestock_data()
    {
        // Generate dummy livestock data
        $livestockData = [
            [
                'id' => 1,
                'block_code' => 'LB001',
                'farmer_name' => 'Michael Temu',
                'livestock_name' => 'Pig',
                'breed' => 'Local breed',
                'he_total' => 3,
                'she_total' => 5,
                'cost_per_livestock' => 150.00,
                'block_site' => 'Boboa Pig Farm'
            ],
            [
                'id' => 2,
                'block_code' => 'LB002',
                'farmer_name' => 'Sarah Kila',
                'livestock_name' => 'Chicken',
                'breed' => 'Rhode Island Red',
                'he_total' => 2,
                'she_total' => 18,
                'cost_per_livestock' => 25.00,
                'block_site' => 'Urban Poultry House'
            ]
        ];

        $data = [
            'title' => 'Livestock Data Report',
            'page_header' => 'Livestock Production Data Report',
            'livestock_data' => $livestockData,
            'total_records' => count($livestockData),
            'total_animals' => 28
        ];

        return view('staff/reports/livestock_data', $data);
    }

    /**
     * Fertilizer Report
     */
    public function fertilizer()
    {
        // Generate dummy fertilizer application data
        $fertilizerData = [
            [
                'block_code' => 'CB001',
                'farmer_name' => 'Michael Temu',
                'crop_name' => 'Sweet Potato',
                'fertilizer_name' => 'NPK 15-15-15',
                'quantity' => 50,
                'unit' => 'kg',
                'action_date' => '2024-01-20'
            ],
            [
                'block_code' => 'CB003',
                'farmer_name' => 'Sarah Kila',
                'crop_name' => 'Banana',
                'fertilizer_name' => 'Urea',
                'quantity' => 30,
                'unit' => 'kg',
                'action_date' => '2024-02-15'
            ]
        ];

        $data = [
            'title' => 'Fertilizer Application Report',
            'page_header' => 'Fertilizer Application Report',
            'fertilizer_data' => $fertilizerData,
            'fertilizers' => DummyDataHelper::getFertilizers(),
            'total_applications' => count($fertilizerData)
        ];

        return view('staff/reports/fertilizer', $data);
    }

    /**
     * Harvests Report
     */
    public function harvests()
    {
        // Generate dummy harvest data
        $harvestData = [
            [
                'block_code' => 'CB001',
                'farmer_name' => 'Michael Temu',
                'crop_name' => 'Sweet Potato',
                'item' => 'Sweet Potato Tubers',
                'quantity' => 1200,
                'unit' => 'kg',
                'harvest_date' => '2024-03-15'
            ],
            [
                'block_code' => 'CB003',
                'farmer_name' => 'Sarah Kila',
                'crop_name' => 'Banana',
                'item' => 'Banana Bunches',
                'quantity' => 85,
                'unit' => 'bunches',
                'harvest_date' => '2024-03-10'
            ]
        ];

        $data = [
            'title' => 'Harvest Report',
            'page_header' => 'Crop Harvest Report',
            'harvest_data' => $harvestData,
            'total_harvests' => count($harvestData),
            'total_production' => '1,285 units'
        ];

        return view('staff/reports/harvests', $data);
    }

    /**
     * Marketing Report
     */
    public function marketing()
    {
        // Generate dummy marketing data
        $marketingData = [
            [
                'farmer_name' => 'Michael Temu',
                'crop_name' => 'Sweet Potato',
                'product' => 'Sweet Potato Tubers',
                'quantity' => 800,
                'unit' => 'kg',
                'price_per_unit' => 2.50,
                'total_value' => 2000.00,
                'market_date' => '2024-03-20',
                'selling_location' => 'Daru Market'
            ],
            [
                'farmer_name' => 'Sarah Kila',
                'crop_name' => 'Banana',
                'product' => 'Banana Bunches',
                'quantity' => 60,
                'unit' => 'bunches',
                'price_per_unit' => 15.00,
                'total_value' => 900.00,
                'market_date' => '2024-03-12',
                'selling_location' => 'Daru Town Market'
            ]
        ];

        $data = [
            'title' => 'Marketing Report',
            'page_header' => 'Crop Marketing Report',
            'marketing_data' => $marketingData,
            'total_sales' => count($marketingData),
            'total_value' => 2900.00
        ];

        return view('staff/reports/marketing', $data);
    }
}