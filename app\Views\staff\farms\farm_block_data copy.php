<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3">
        <a href="<?= base_url('staff/farms/farm-blocks') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Farm Blocks
        </a>
    </div>
</div>

<div class="row">
    <!-- Farm Block Details Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Block Code</th>
                        <td><?= esc($block['block_code']) ?></td>
                    </tr>
                    <tr>
                        <th>Crop</th>
                        <td><?= esc($crop['item']) ?></td>
                    </tr>
                    <tr>
                        <th>Remarks</th>
                        <td><?= esc($block['remarks']) ?: 'No remarks' ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Farmer Information Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-user"></i> Farmer Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Farmer Code</th>
                        <td><?= esc($farmer['farmer_code']) ?></td>
                    </tr>
                    <tr>
                        <th>Name</th>
                        <td><?= esc($farmer['given_name']) ?> <?= esc($farmer['surname']) ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Location Details Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Location Details</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">District</th>
                        <td><?= esc($district['name']) ?></td>
                    </tr>
                    <tr>
                        <th>LLG</th>
                        <td><?= esc($llg['name']) ?></td>
                    </tr>
                    <tr>
                        <th>Ward</th>
                        <td><?= esc($ward['name']) ?></td>
                    </tr>
                    <tr>
                        <th>Village</th>
                        <td><?= esc($block['village']) ?></td>
                    </tr>
                    <tr>
                        <th>Block Site</th>
                        <td><?= esc($block['block_site']) ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Coordinates Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning">
                <h5 class="mb-0"><i class="fas fa-map"></i> GPS Coordinates</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Latitude</th>
                        <td><?= esc($block['lat']) ?></td>
                    </tr>
                    <tr>
                        <th>Longitude</th>
                        <td><?= esc($block['lon']) ?></td>
                    </tr>
                </table>
                <div class="mt-3">
                    <a href="https://www.google.com/maps/search/?api=1&query=<?= $block['lat'] ?>,<?= $block['lon'] ?>" 
                       class="btn btn-primary" 
                       target="_blank">
                        <i class="fas fa-map-marked-alt"></i> View on Google Maps
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?> 