<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="card-title mb-0">Edit Participant</h4>
                        <p class="text-muted small mb-0">Training: <?= esc($training['topic']) ?></p>
                    </div>
                    <div class="col-auto">
                        <a href="<?= base_url('staff/extension/trainings/participants/' . $training['id']) ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Participants
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?= form_open('staff/extension/trainings/participants/update/' . $training['id'] . '/' . $participant['id']) ?>
                
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Participant Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?= old('name', $participant['name']) ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="type" class="form-label">Participant Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">Select Type</option>
                                <option value="Farmer" <?= old('type', $participant['type']) == 'Farmer' ? 'selected' : '' ?>>Farmer</option>
                                <option value="Official" <?= old('type', $participant['type']) == 'Official' ? 'selected' : '' ?>>Official</option>
                                <option value="Other" <?= old('type', $participant['type']) == 'Other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6 farmer-id-field" style="display:<?= old('type', $participant['type']) == 'Farmer' ? 'block' : 'none' ?>;">
                            <label for="farmer_id" class="form-label">Farmer ID</label>
                            <input type="text" class="form-control" id="farmer_id" name="farmer_id" value="<?= old('farmer_id', $participant['farmer_id'] ?? '') ?>">
                            <small class="text-muted">Enter the registered Farmer ID if available</small>
                        </div>
                        <div class="col-md-6">
                            <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">Select Gender</option>
                                <option value="Male" <?= old('gender', $participant['gender']) == 'Male' ? 'selected' : '' ?>>Male</option>
                                <option value="Female" <?= old('gender', $participant['gender']) == 'Female' ? 'selected' : '' ?>>Female</option>
                                <option value="Other" <?= old('gender', $participant['gender']) == 'Other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="age" class="form-label">Age <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="age" name="age" min="1" max="120" value="<?= old('age', $participant['age'] ?? '') ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="<?= old('phone', $participant['phone'] ?? '') ?>">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?= old('email', $participant['email'] ?? '') ?>">
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="<?= base_url('staff/extension/trainings/participants/' . $training['id']) ?>" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update Participant</button>
                    </div>
                    
                <?= form_close() ?>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Show/hide farmer ID field based on participant type
        $('#type').on('change', function() {
            if ($(this).val() === 'Farmer') {
                $('.farmer-id-field').show();
            } else {
                $('.farmer-id-field').hide();
                $('#farmer_id').val('');
            }
        });
    });
</script>
<?= $this->endSection() ?> 