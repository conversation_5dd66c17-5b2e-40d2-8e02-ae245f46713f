<?php

namespace App\Controllers;

use App\Models\DakoiiUsersModel;

class DakoiiUsers extends BaseController
{
    public $session;
    public $dusersModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dusersModel = new DakoiiUsersModel();
    }

    /**
     * Display users list
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "User Management";
        $data['menu'] = "users";
        $data['users'] = $this->dusersModel->findAll();

        return view('dakoii/dakoii_users_index', $data);
    }

    /**
     * Show create user form
     */
    public function create()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Create User";
        $data['menu'] = "users";

        return view('dakoii/dakoii_users_create', $data);
    }

    /**
     * Store new user
     */
    public function store()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/users');
        }

        // Validate input
        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username]',
            'password' => 'required|min_length[6]',
            'role' => 'required|in_list[user,moderator,admin]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid user data. Username must be unique.');
            return redirect()->to('dakoii/users/create')->withInput();
        }

        $data = [
            'name' => trim($this->request->getPost('name')),
            'username' => trim($this->request->getPost('username')),
            'password' => $this->request->getPost('password'), // Will be hashed by model
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0
        ];

        if ($this->dusersModel->createDakoiiUser($data)) {
            session()->setFlashdata('success', 'User "' . $data['name'] . '" created successfully!');
            return redirect()->to('dakoii/users');
        } else {
            session()->setFlashdata('error', 'Failed to create user. Username may already exist.');
            return redirect()->to('dakoii/users/create')->withInput();
        }
    }

    /**
     * Show portal user details
     */
    public function show($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $user = $this->dusersModel->getSafeDakoiiUser($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        $data['title'] = "User Details - " . $user['name'];
        $data['menu'] = "users";
        $data['user'] = $user;

        return view('dakoii/dakoii_users_show', $data);
    }

    /**
     * Show edit portal user form
     */
    public function edit($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $user = $this->dusersModel->find($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        $data['title'] = "Edit User - " . $user['name'];
        $data['menu'] = "users";
        $data['user'] = $user;

        return view('dakoii/dakoii_users_edit', $data);
    }

    /**
     * Update portal user
     */
    public function update($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/users');
        }

        $user = $this->dusersModel->find($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        // Validate input
        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username,id,' . $id . ']',
            'role' => 'required|in_list[user,moderator,admin]'
        ];

        // Add password validation only if password is provided
        if (!empty($this->request->getPost('password'))) {
            $rules['password'] = 'min_length[6]';
        }

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid user data. Username must be unique.');
            return redirect()->to('dakoii/users/edit/' . $id)->withInput();
        }

        $data = [
            'name' => trim($this->request->getPost('name')),
            'username' => trim($this->request->getPost('username')),
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0
        ];

        // Add password to update data only if provided
        if (!empty($this->request->getPost('password'))) {
            $data['password'] = $this->request->getPost('password'); // Will be hashed by model
        }

        if ($this->dusersModel->updateDakoiiUser($id, $data)) {
            session()->setFlashdata('success', 'User "' . $data['name'] . '" updated successfully!');
            return redirect()->to('dakoii/users');
        } else {
            session()->setFlashdata('error', 'Failed to update user. Username may already exist.');
            return redirect()->to('dakoii/users/edit/' . $id)->withInput();
        }
    }

    /**
     * Delete portal user (soft delete by setting is_active to 0)
     */
    public function delete($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/users');
        }

        $user = $this->dusersModel->find($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        // Prevent deleting the current user
        if ($this->session->get('dakoii_user_id') == $id) {
            session()->setFlashdata('error', 'You cannot delete your own account');
            return redirect()->to('dakoii/users');
        }

        // Soft delete by deactivating the user
        if ($this->dusersModel->deactivateDakoiiUser($id)) {
            session()->setFlashdata('success', 'User "' . $user['name'] . '" has been deactivated successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to deactivate user. Please try again.');
        }

        return redirect()->to('dakoii/users');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Get current user information
     */
    private function getCurrentUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'id' => $this->session->get('dakoii_user_id'),
            'name' => $this->session->get('dakoii_name'),
            'username' => $this->session->get('dakoii_username'),
            'role' => $this->session->get('dakoii_role'),
            'orgcode' => $this->session->get('dakoii_orgcode')
        ];
    }

    /**
     * Show change password form
     */
    public function changePassword($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $user = $this->dusersModel->find($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        // Only allow users to change their own password unless they are admins
        if ($this->session->get('dakoii_user_id') != $id && $this->session->get('dakoii_role') !== 'admin') {
            session()->setFlashdata('error', 'You can only change your own password');
            return redirect()->to('dakoii/users');
        }

        $data['title'] = "Change Password - " . $user['name'];
        $data['menu'] = "users";
        $data['user'] = $user;

        return view('dakoii/dakoii_users_change_password', $data);
    }

    /**
     * Process password change
     */
    public function processPasswordChange($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/users');
        }

        $user = $this->dusersModel->find($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        // Only allow users to change their own password unless they are admins
        if ($this->session->get('dakoii_user_id') != $id && $this->session->get('dakoii_role') !== 'admin') {
            session()->setFlashdata('error', 'You can only change your own password');
            return redirect()->to('dakoii/users');
        }

        // Validate input
        $rules = [
            'current_password' => 'required',
            'new_password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[new_password]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid password information');
            return redirect()->to('dakoii/users/change-password/' . $id)->withInput();
        }

        $currentPassword = $this->request->getPost('current_password');
        $newPassword = $this->request->getPost('new_password');

        // Use the model's changeUserPassword method
        if ($this->dusersModel->changeUserPassword($id, $currentPassword, $newPassword)) {
            session()->setFlashdata('success', 'Password changed successfully!');
            return redirect()->to('dakoii/users');
        } else {
            session()->setFlashdata('error', 'Failed to change password. Current password may be incorrect.');
            return redirect()->to('dakoii/users/change-password/' . $id);
        }
    }
}
