<?php namespace App\Models;

use CodeIgniter\Model;

class GroupingsModel extends Model
{
    protected $table = 'groupings';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    // Fields that can be set during insert or update
    protected $allowedFields = [
        'org_id',
        'name',
        'description',
        'parent_id',
        'created_by',
        'updated_by'
    ];

    // Enable timestamps and set fields for created and updated timestamps
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $dateFormat = 'datetime';

    // Skipping validation
    protected $skipValidation = true;

    // Helper method to get group hierarchy
    public function getGroupHierarchy($orgId)
    {
        $groups = $this->where('org_id', $orgId)
                      ->where('parent_id', 0)
                      ->findAll();

        foreach ($groups as &$group) {
            $group['children'] = $this->getChildGroups($group['id']);
        }

        return $groups;
    }

    // Helper method to get child groups
    private function getChildGroups($parentId)
    {
        $children = $this->where('parent_id', $parentId)->findAll();

        foreach ($children as &$child) {
            $child['children'] = $this->getChildGroups($child['id']);
        }

        return $children;
    }

    // Get groups as dropdown options
    public function getGroupsDropdown($orgId)
    {
        $groups = $this->where('org_id', $orgId)->findAll();
        $dropdown = [];
        
        foreach ($groups as $group) {
            $dropdown[$group['id']] = $group['name'];
        }

        return $dropdown;
    }

    // Check if group has children
    public function hasChildren($groupId)
    {
        return $this->where('parent_id', $groupId)->countAllResults() > 0;
    }

    // Get group path (breadcrumb)
    public function getGroupPath($groupId)
    {
        $path = [];
        $current = $this->find($groupId);

        while ($current) {
            array_unshift($path, $current);
            $current = $current['parent_id'] ? $this->find($current['parent_id']) : null;
        }

        return $path;
    }
} 