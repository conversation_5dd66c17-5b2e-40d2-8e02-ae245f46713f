<?php namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\CropsFarmBlockModel;
use App\Models\CropsFarmCropsDataModel;
use App\Models\ProvinceModel;
use App\Models\DistrictModel;
use App\Models\LlgModel;
use App\Models\WardModel;

class CropsBlock_Dashboard extends BaseController
{
    protected $cropBlockModel;
    protected $cropDataModel;
    protected $session;

    public function __construct()
    {
        helper(['url', 'form', 'info']);
        $this->session = \Config\Services::session();
        $this->cropBlockModel = new CropsFarmBlockModel();
        $this->cropDataModel = new CropsFarmCropsDataModel();
    }

    public function dashboard_crops_blocks()
    {
        $db = \Config\Database::connect();

        // Get unique locations from active blocks for filtering
        $locations = [
            'provinces' => $db->table('crops_farm_blocks b')
                ->select('p.id, p.name')
                ->join('adx_province p', 'p.id = b.province_id')
                ->where('b.status', 'active')
                ->groupBy('p.id')
                ->get()->getResultArray(),
            'districts' => $db->table('crops_farm_blocks b')
                ->select('d.id, d.name')
                ->join('adx_district d', 'd.id = b.district_id')
                ->where('b.status', 'active')
                ->groupBy('d.id')
                ->get()->getResultArray(),
            'llgs' => $db->table('crops_farm_blocks b')
                ->select('l.id, l.name')
                ->join('adx_llg l', 'l.id = b.llg_id')
                ->where('b.status', 'active')
                ->groupBy('l.id')
                ->get()->getResultArray(),
            'wards' => $db->table('crops_farm_blocks b')
                ->select('w.id, w.name')
                ->join('adx_ward w', 'w.id = b.ward_id')
                ->where('b.status', 'active')
                ->groupBy('w.id')
                ->get()->getResultArray()
        ];

        $blocks = $this->cropBlockModel
            ->select('
                crops_farm_blocks.*,
                farmers.given_name, farmers.surname,
                crops.crop_name,
                provinces.name as province_name,
                districts.name as district_name,
                llgs.name as llg_name,
                wards.name as ward_name,
                (
                    SELECT SUM(hectares) - COALESCE((
                        SELECT SUM(hectares) 
                        FROM crops_farm_crops_data 
                        WHERE block_id = crops_farm_blocks.id 
                        AND action_type = "remove" 
                        AND status = "active"
                    ), 0)
                    FROM crops_farm_crops_data 
                    WHERE block_id = crops_farm_blocks.id 
                    AND action_type = "add" 
                    AND status = "active"
                ) as area,
                (
                    SELECT SUM(number_of_plants) - COALESCE((
                        SELECT SUM(number_of_plants)
                        FROM crops_farm_crops_data
                        WHERE block_id = crops_farm_blocks.id
                        AND action_type = "remove"
                        AND status = "active"
                    ), 0)
                    FROM crops_farm_crops_data
                    WHERE block_id = crops_farm_blocks.id
                    AND action_type = "add"
                    AND status = "active"
                ) as total_plants,
                (
                    SELECT MAX(action_date)
                    FROM crops_farm_crops_data
                    WHERE block_id = crops_farm_blocks.id
                    AND action_type = "add"
                    AND status = "active"
                ) as planting_date
            ')
            ->join('farmer_information farmers', 'farmers.id = crops_farm_blocks.farmer_id')
            ->join('adx_crops crops', 'crops.id = crops_farm_blocks.crop_id')
            ->join('adx_province provinces', 'provinces.id = crops_farm_blocks.province_id')
            ->join('adx_district districts', 'districts.id = crops_farm_blocks.district_id')
            ->join('adx_llg llgs', 'llgs.id = crops_farm_blocks.llg_id')
            ->join('adx_ward wards', 'wards.id = crops_farm_blocks.ward_id')
            ->where('crops_farm_blocks.status', 'active')
            ->findAll();

        return view('dashboard_reports/dashboard_crops_blocks', [
            'title' => 'Crops Farm Blocks Dashboard',
            'page_header' => 'Crops Farm Blocks Dashboard',
            'menu' => 'crop-farm-blocks',
            'crop_blocks' => $blocks,
            'locations' => $locations
        ]);
    }

    public function view($id)
    {
        $db = \Config\Database::connect();
        
        // Load all required models
        $cropBlockModel = new \App\Models\CropsFarmBlockModel();
        $cropDataModel = new \App\Models\CropsFarmCropsDataModel();
        $diseaseDataModel = new \App\Models\CropsFarmDiseaseDataModel();
        $fertilizerDataModel = new \App\Models\CropsFarmFertilizerDataModel();
        $pesticideDataModel = new \App\Models\CropsFarmPesticidesDataModel();
        $harvestDataModel = new \App\Models\CropsFarmHarvestDataModel();
        $farmerModel = new \App\Models\FarmerInformationModel();

        // Get block details with location info
        $block = $cropBlockModel->select('
            crops_farm_blocks.*,
            farmers.given_name, farmers.surname, farmers.phone, farmers.email,
            farmers.village as farmer_village, farmers.date_of_birth, farmers.gender,
            crops.crop_name,
            provinces.name as province_name,
            districts.name as district_name,
            llgs.name as llg_name,
            wards.name as ward_name,
            (
                SELECT SUM(hectares) - COALESCE((
                    SELECT SUM(hectares) 
                    FROM crops_farm_crops_data 
                    WHERE block_id = crops_farm_blocks.id 
                    AND action_type = "remove" 
                    AND status = "active"
                ), 0)
                FROM crops_farm_crops_data 
                WHERE block_id = crops_farm_blocks.id 
                AND action_type = "add" 
                AND status = "active"
            ) as total_area,
            (
                SELECT SUM(number_of_plants) - COALESCE((
                    SELECT SUM(number_of_plants)
                    FROM crops_farm_crops_data
                    WHERE block_id = crops_farm_blocks.id
                    AND action_type = "remove"
                    AND status = "active"
                ), 0)
                FROM crops_farm_crops_data
                WHERE block_id = crops_farm_blocks.id
                AND action_type = "add"
                AND status = "active"
            ) as total_plants
        ')
        ->join('farmer_information farmers', 'farmers.id = crops_farm_blocks.farmer_id')
        ->join('adx_crops crops', 'crops.id = crops_farm_blocks.crop_id')
        ->join('adx_province provinces', 'provinces.id = crops_farm_blocks.province_id')
        ->join('adx_district districts', 'districts.id = crops_farm_blocks.district_id')
        ->join('adx_llg llgs', 'llgs.id = crops_farm_blocks.llg_id')
        ->join('adx_ward wards', 'wards.id = crops_farm_blocks.ward_id')
        ->where('crops_farm_blocks.id', $id)
        ->first();

        if (!$block) {
            return redirect()->to('/dashboards/crops-block')->with('error', 'Block not found');
        }

        // Get disease data
        $diseases = $diseaseDataModel->select('
            crops_farm_disease_data.*,
            adx_infections.name as infection_name
        ')
        ->join('adx_infections', 'adx_infections.name = crops_farm_disease_data.disease_name', 'left')
        ->where('block_id', $id)
        ->where('crops_farm_disease_data.status', 'active')
        ->findAll();

        // Get fertilizer usage
        $fertilizers = $fertilizerDataModel->select('
            crops_farm_fertilizer_data.*,
            adx_fertilizers.name as fertilizer_name
        ')
        ->join('adx_fertilizers', 'adx_fertilizers.id = crops_farm_fertilizer_data.fertilizer_id', 'left')
        ->where('block_id', $id)
        ->where('crops_farm_fertilizer_data.status', 'active')
        ->findAll();

        // Get pesticide usage
        $pesticides = $pesticideDataModel->select('
            crops_farm_pesticides_data.*,
            adx_pesticides.name as pesticide_name
        ')
        ->join('adx_pesticides', 'adx_pesticides.id = crops_farm_pesticides_data.pesticide_id', 'left')
        ->where('block_id', $id)
        ->where('crops_farm_pesticides_data.status', 'active')
        ->findAll();

        // Get harvest data
        $harvests = $harvestDataModel->where('block_id', $id)
            ->where('status', 'active')
            ->findAll();

        // Calculate totals
        $totals = [
            'diseases' => [
                'affected_plants' => array_sum(array_column($diseases, 'number_of_plants')),
                'affected_area' => array_sum(array_column($diseases, 'hectares'))
            ],
            'fertilizers' => array_sum(array_column($fertilizers, 'quantity')),
            'pesticides' => array_sum(array_column($pesticides, 'quantity')),
            'harvests' => array_sum(array_column($harvests, 'quantity'))
        ];

        return view('dashboard_reports/crops_block_profile', [
            'title' => 'Crop Block Profile',
            'page_header' => 'Crop Block Profile: ' . $block['block_code'],
            'menu' => 'crop-farm-blocks',
            'block' => $block,
            'diseases' => $diseases,
            'fertilizers' => $fertilizers,
            'pesticides' => $pesticides,
            'harvests' => $harvests,
            'totals' => $totals
        ]);
    }
}
