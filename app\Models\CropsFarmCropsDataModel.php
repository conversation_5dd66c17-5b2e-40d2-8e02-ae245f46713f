<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmCropsDataModel extends Model
{
    protected $table = 'crops_farm_crops_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'exercise_id',
        'block_id',
        'crop_id',
        'action_type',      // enum('add', 'remove')
        'action_reason',    // varchar(100)
        'number_of_plants', // int(11)
        'breed',           // varchar(255)
        'action_date',     // date
        'hectares',        // decimal(10,2)
        'remarks',         // text
        'created_by',
        'updated_by',
        'deleted_by',
        'status'           // enum('active', 'inactive', 'deleted')
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $validationRules = [
        'block_id' => 'required|numeric',
        'crop_id' => 'required|numeric',
        'action_type' => 'required|in_list[add,remove]',
        'action_reason' => 'required|max_length[100]',
        'number_of_plants' => 'required|numeric',
        'breed' => 'permit_empty|max_length[255]',
        'action_date' => 'required|valid_date',
        'hectares' => 'required|decimal',
        'remarks' => 'permit_empty',
        'created_by' => 'required|numeric',
        'status' => 'required|in_list[active,inactive,deleted]'
    ];

    protected $validationMessages = [
        'block_id' => [
            'required' => 'Block ID is required',
            'numeric' => 'Block ID must be numeric'
        ],
        'crop_id' => [
            'required' => 'Crop ID is required',
            'numeric' => 'Crop ID must be numeric'
        ],
        'action_type' => [
            'required' => 'Action type is required',
            'in_list' => 'Action type must be either add or remove'
        ],
        'action_reason' => [
            'required' => 'Action reason is required',
            'max_length' => 'Action reason cannot exceed 100 characters'
        ],
        'number_of_plants' => [
            'required' => 'Number of plants is required',
            'numeric' => 'Number of plants must be numeric'
        ],
        'action_date' => [
            'required' => 'Action date is required',
            'valid_date' => 'Invalid date format'
        ],
        'hectares' => [
            'required' => 'Hectares is required',
            'decimal' => 'Hectares must be a decimal number'
        ],
        'created_by' => [
            'required' => 'Created by is required',
            'numeric' => 'Created by must be numeric'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be active, inactive, or deleted'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    public function getCropsReportData()
    {
        return $this->select('
            crops_farm_blocks.id as block_id,
            crops_farm_blocks.block_code,
            crops_farm_blocks.block_site,
            crops_farm_blocks.status,
            farmer_information.given_name,
            farmer_information.surname,
            adx_crops.crop_name,
            adx_crops.crop_color_code,
            adx_district.name as district_name,
            adx_llg.name as llg_name,
            adx_ward.name as ward_name,
            MAX(crops_farm_crops_data.action_date) as latest_action_date,
            (SELECT SUM(number_of_plants) FROM crops_farm_crops_data cfd2
             WHERE cfd2.block_id = crops_farm_blocks.id
             AND cfd2.action_type = "add"
             AND cfd2.status = "active") as total_plants_added,
            (SELECT SUM(number_of_plants) FROM crops_farm_crops_data cfd2
             WHERE cfd2.block_id = crops_farm_blocks.id
             AND cfd2.action_type = "remove"
             AND cfd2.status = "active") as total_plants_removed,
            (SELECT SUM(hectares) FROM crops_farm_crops_data cfd2
             WHERE cfd2.block_id = crops_farm_blocks.id
             AND cfd2.action_type = "add"
             AND cfd2.status = "active") as total_hectares_added,
            (SELECT SUM(hectares) FROM crops_farm_crops_data cfd2
             WHERE cfd2.block_id = crops_farm_blocks.id
             AND cfd2.action_type = "remove"
             AND cfd2.status = "active") as total_hectares_removed
        ')
        ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
        ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
        ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
        ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
        ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
        ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
        ->where('crops_farm_crops_data.status', 'active')
        ->where('crops_farm_blocks.district_id', session()->get('district_id'))
        ->groupBy('crops_farm_blocks.id')
        ->orderBy('latest_action_date', 'DESC')
        ->findAll();
    }
}