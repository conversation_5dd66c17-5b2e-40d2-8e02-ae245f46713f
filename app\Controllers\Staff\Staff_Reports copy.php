<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel as FarmBlockModel;
use App\Models\CropsFarmCropsDataModel as FarmCropsDataModel;
use App\Models\CropsFarmFertilizerDataModel as FarmFertilizerDataModel;
use App\Models\CropsFarmPesticidesDataModel as FarmPesticidesDataModel;
use App\Models\CropsFarmHarvestDataModel as FarmHarvestDataModel;
use App\Models\CropsFarmMarketingDataModel as FarmMarketingDataModel;
use App\Models\CropsFarmDiseaseDataModel as FarmDiseaseDataModel;
use App\Models\CropsModel;
use App\Models\CropBuyersModel;
use App\Models\provinceModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\wardModel;
use App\Models\InfectionsModel;
use App\Models\PesticidesModel;
use App\Models\FertilizersModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\LivestockFarmDataModel;
use App\Models\LivestockModel;

class Staff_Reports extends BaseController
{
    protected $farmersModel;
    protected $farmBlockModel;
    protected $farmCropsDataModel;
    protected $farmDiseaseDataModel;
    protected $farmFertilizerDataModel;
    protected $farmPesticidesDataModel;
    protected $farmHarvestDataModel;
    protected $farmMarketingDataModel;
    protected $cropsModel;
    protected $infectionsModel;
    protected $pesticidesModel;
    protected $fertilizersModel;
    protected $cropBuyersModel;
    protected $livestockFarmBlockModel;
    protected $livestockFarmDataModel;
    protected $livestockModel;

    public function __construct()
    {
        helper(['url', 'form', 'info']);
        $this->farmersModel = new FarmerInformationModel();
        $this->farmBlockModel = new FarmBlockModel();
        $this->farmCropsDataModel = new FarmCropsDataModel();
        $this->farmDiseaseDataModel = new FarmDiseaseDataModel();
        $this->farmFertilizerDataModel = new FarmFertilizerDataModel();
        $this->farmPesticidesDataModel = new FarmPesticidesDataModel();
        $this->farmHarvestDataModel = new FarmHarvestDataModel();
        $this->farmMarketingDataModel = new FarmMarketingDataModel();
        $this->cropsModel = new CropsModel();
        $this->infectionsModel = new InfectionsModel();
        $this->pesticidesModel = new PesticidesModel();
        $this->fertilizersModel = new FertilizersModel();
        $this->cropBuyersModel = new CropBuyersModel();
        $this->livestockFarmBlockModel = new \App\Models\LivestockFarmBlockModel();
        $this->livestockFarmDataModel = new \App\Models\LivestockFarmDataModel();
        $this->livestockModel = new \App\Models\LivestockModel();
    }

    public function farmers()
    {
        $farmers = $this->getFarmersWithDetails();
        
        $data = [
            'title' => 'Farmers Reports',
            'page_header' => 'Farmers Reports',
            'farmers' => $farmers,
            'stats' => [
                'total_farmers' => count($farmers),
                'active_farmers' => count(array_filter($farmers, fn($f) => $f['status'] === 'active')),
                'inactive_farmers' => count(array_filter($farmers, fn($f) => $f['status'] === 'inactive'))
            ]
        ];

        // For debugging
        // log_message('debug', 'Farmers data: ' . json_encode($farmers));

        return view('staff/reports/farmers', $data);
    }

    private function getFarmersWithDetails()
    {
        $farmers = $this->farmersModel
            ->select('farmer_information.*, 
                     adx_province.name as province_name,
                     adx_district.name as district_name,
                     adx_llg.name as llg_name,
                     adx_ward.name as ward_name,
                     (SELECT COUNT(*) FROM farmers_children 
                      WHERE farmers_children.farmer_id = farmer_information.id) as children_count')
            ->join('adx_province', 'adx_province.id = farmer_information.province_id', 'left')
            ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
            ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
            ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
            ->where('farmer_information.created_by', session()->get('emp_id'))
            ->where('farmer_information.status', 'active')
            ->findAll();

        return $farmers;
    }

    public function crops()
    {
        $data = [
            'title' => 'Crops Reports',
            'page_header' => 'Crops Reports',
            'crops_data' => $this->farmCropsDataModel->getCropsReportData(session()->get('emp_id')),
            'crops' => $this->cropsModel->findAll()
        ];

        return view('staff/reports/crops', $data);
    }

    public function blocks()
    {
        $farmBlockModel = new FarmBlockModel();
        $blocks_data = $farmBlockModel->getFarmBlocksWithDetails();

        // Get statistics for blocks
        $stats = [
            'total_blocks' => count($blocks_data),
            'total_active' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'active')),
            'by_district' => [],
            'by_crop' => [],
            'by_llg' => []
        ];

        // Process statistics
        foreach ($blocks_data as $block) {
            // Count by district
            if (!isset($stats['by_district'][$block['district_name']])) {
                $stats['by_district'][$block['district_name']] = 0;
            }
            $stats['by_district'][$block['district_name']]++;

            // Count by crop
            if (!isset($stats['by_crop'][$block['crop_name']])) {
                $stats['by_crop'][$block['crop_name']] = 0;
            }
            $stats['by_crop'][$block['crop_name']]++;

            // Count by LLG
            if (!isset($stats['by_llg'][$block['llg_name']])) {
                $stats['by_llg'][$block['llg_name']] = 0;
            }
            $stats['by_llg'][$block['llg_name']]++;
        }

        $data = [
            'title' => 'Farm Blocks Reports',
            'page_header' => 'Farm Blocks Reports',
            'blocks_data' => $blocks_data,
            'stats' => $stats
        ];

        return view('staff/reports/blocks', $data);
    }

    public function diseases()
    {
        // Get diseases data with active blocks only
        $diseases_data = $this->farmDiseaseDataModel->getDiseasesReportData(session()->get('emp_id'));
        
        // Filter for active blocks only
        $diseases_data = array_filter($diseases_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $diseases_data = array_values($diseases_data);
        
        // Process statistics
        $stats = [
            'total_cases' => count($diseases_data),
            'total_plants_affected' => array_sum(array_column($diseases_data, 'number_of_plants')),
            'total_hectares_affected' => array_sum(array_column($diseases_data, 'hectares')),
            'by_disease' => [],
            'by_crop' => [],
            'by_district' => [],
            'monthly_cases' => array_fill(0, 12, 0)
        ];

        foreach ($diseases_data as $case) {
            // Count by disease type
            if (!isset($stats['by_disease'][$case['disease_name']])) {
                $stats['by_disease'][$case['disease_name']] = [
                    'count' => 0,
                    'plants' => 0,
                    'hectares' => 0
                ];
            }
            $stats['by_disease'][$case['disease_name']]['count']++;
            $stats['by_disease'][$case['disease_name']]['plants'] += $case['number_of_plants'];
            $stats['by_disease'][$case['disease_name']]['hectares'] += $case['hectares'];

            // Count by crop
            if (!isset($stats['by_crop'][$case['crop_name']])) {
                $stats['by_crop'][$case['crop_name']] = [
                    'count' => 0,
                    'color' => $case['crop_color_code']
                ];
            }
            $stats['by_crop'][$case['crop_name']]['count']++;

            // Count by district
            if (!isset($stats['by_district'][$case['district_name']])) {
                $stats['by_district'][$case['district_name']] = 0;
            }
            $stats['by_district'][$case['district_name']]++;

            // Count monthly cases
            $month = date('n', strtotime($case['action_date'])) - 1;
            $stats['monthly_cases'][$month]++;
        }

        $data = [
            'title' => 'Diseases Reports',
            'page_header' => 'Diseases Reports',
            'diseases_data' => $diseases_data,
            'stats' => $stats
        ];

        return view('staff/reports/diseases', $data);
    }

    public function fertilizer()
    {
        // Get fertilizer data with active blocks only
        $fertilizer_data = $this->farmFertilizerDataModel->getFertilizerReportData(session()->get('emp_id'));
        
        // Filter for active blocks only
        $fertilizer_data = array_filter($fertilizer_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $fertilizer_data = array_values($fertilizer_data);

        $data = [
            'title' => 'Fertilizer Reports',
            'page_header' => 'Fertilizer Reports',
            'fertilizer_data' => $fertilizer_data,
            'fertilizers' => $this->fertilizersModel->findAll()
        ];

        return view('staff/reports/fertilizer', $data);
    }

    public function pesticides()
    {
        // Get pesticides data with active blocks only
        $pesticides_data = $this->farmPesticidesDataModel->getPesticidesReportData(session()->get('emp_id'));
        
        // Filter for active blocks only
        $pesticides_data = array_filter($pesticides_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $pesticides_data = array_values($pesticides_data);

        $data = [
            'title' => 'Pesticides Reports',
            'page_header' => 'Pesticides Reports',
            'pesticides_data' => $pesticides_data,
            'pesticides' => $this->pesticidesModel->findAll()
        ];

        return view('staff/reports/pesticides', $data);
    }

    public function harvests()
    {
        // Get harvest data with active blocks only
        $harvest_data = $this->farmHarvestDataModel->getHarvestReportData(session()->get('emp_id'));
        
        // Filter for active blocks only
        $harvest_data = array_filter($harvest_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $harvest_data = array_values($harvest_data);
        
        // Process statistics
        $stats = [
            'total_harvests' => count($harvest_data),
            'total_quantity' => array_sum(array_column($harvest_data, 'quantity')),
            'by_crop' => [],
            'by_district' => [],
            'monthly_harvests' => array_fill(0, 12, 0),
            'monthly_quantity' => array_fill(0, 12, 0)
        ];

        foreach ($harvest_data as $harvest) {
            // Stats by crop
            if (!isset($stats['by_crop'][$harvest['crop_name']])) {
                $stats['by_crop'][$harvest['crop_name']] = [
                    'quantity' => 0,
                    'harvests' => 0,
                    'color' => $harvest['crop_color_code']
                ];
            }
            $stats['by_crop'][$harvest['crop_name']]['quantity'] += $harvest['quantity'];
            $stats['by_crop'][$harvest['crop_name']]['harvests']++;

            // Stats by district
            if (!isset($stats['by_district'][$harvest['district_name']])) {
                $stats['by_district'][$harvest['district_name']] = [
                    'quantity' => 0,
                    'harvests' => 0
                ];
            }
            $stats['by_district'][$harvest['district_name']]['quantity'] += $harvest['quantity'];
            $stats['by_district'][$harvest['district_name']]['harvests']++;

            // Monthly stats
            $month = date('n', strtotime($harvest['harvest_date'])) - 1;
            $stats['monthly_harvests'][$month]++;
            $stats['monthly_quantity'][$month] += $harvest['quantity'];
        }

        // Sort arrays by quantity
        arsort($stats['by_crop']);
        arsort($stats['by_district']);

        $data = [
            'title' => 'Harvest Reports',
            'page_header' => 'Harvest Reports',
            'harvest_data' => $harvest_data,
            'stats' => $stats
        ];

        return view('staff/reports/harvests', $data);
    }

    public function marketing()
    {
        // Get marketing data
        $marketing_data = $this->farmMarketingDataModel->getMarketingReportData(session()->get('emp_id'));
        
        // Process statistics
        $stats = [
            'total_transactions' => count($marketing_data),
            'total_revenue' => array_sum(array_map(function($item) {
                return $item['quantity'] * $item['market_price_per_unit'];
            }, $marketing_data)),
            'total_quantity' => array_sum(array_column($marketing_data, 'quantity')),
            'by_crop' => [],
            'by_buyer' => [],
            'by_district' => [],
            'monthly_revenue' => array_fill(0, 12, 0),
            'monthly_quantity' => array_fill(0, 12, 0)
        ];

        foreach ($marketing_data as $transaction) {
            $revenue = $transaction['quantity'] * $transaction['market_price_per_unit'];
            
            // Stats by crop
            if (!isset($stats['by_crop'][$transaction['crop_name']])) {
                $stats['by_crop'][$transaction['crop_name']] = [
                    'revenue' => 0,
                    'quantity' => 0,
                    'transactions' => 0,
                    'color' => $transaction['crop_color_code']
                ];
            }
            $stats['by_crop'][$transaction['crop_name']]['revenue'] += $revenue;
            $stats['by_crop'][$transaction['crop_name']]['quantity'] += $transaction['quantity'];
            $stats['by_crop'][$transaction['crop_name']]['transactions']++;

            // Stats by buyer
            $buyer_name = $transaction['buyer_name'] ?? 'Unknown';
            if (!isset($stats['by_buyer'][$buyer_name])) {
                $stats['by_buyer'][$buyer_name] = [
                    'revenue' => 0,
                    'quantity' => 0,
                    'transactions' => 0
                ];
            }
            $stats['by_buyer'][$buyer_name]['revenue'] += $revenue;
            $stats['by_buyer'][$buyer_name]['quantity'] += $transaction['quantity'];
            $stats['by_buyer'][$buyer_name]['transactions']++;

            // Stats by district
            if (!isset($stats['by_district'][$transaction['district_name']])) {
                $stats['by_district'][$transaction['district_name']] = [
                    'revenue' => 0,
                    'quantity' => 0,
                    'transactions' => 0
                ];
            }
            $stats['by_district'][$transaction['district_name']]['revenue'] += $revenue;
            $stats['by_district'][$transaction['district_name']]['quantity'] += $transaction['quantity'];
            $stats['by_district'][$transaction['district_name']]['transactions']++;

            // Monthly stats
            $month = date('n', strtotime($transaction['market_date'])) - 1;
            $stats['monthly_revenue'][$month] += $revenue;
            $stats['monthly_quantity'][$month] += $transaction['quantity'];
        }

        // Sort arrays by revenue
        arsort($stats['by_crop']);
        arsort($stats['by_buyer']);
        arsort($stats['by_district']);

        $data = [
            'title' => 'Marketing Reports',
            'page_header' => 'Marketing Reports',
            'marketing_data' => $marketing_data,
            'stats' => $stats,
            'buyers' => $this->cropBuyersModel->findAll()
        ];

        return view('staff/reports/marketing', $data);
    }

    public function livestock_blocks()
    {
        // Get livestock farm blocks data using basic model features
        $blocks_data = $this->livestockFarmBlockModel
            ->select('livestock_farm_blocks.id, 
                    livestock_farm_blocks.block_code,
                    livestock_farm_blocks.block_site,
                    livestock_farm_blocks.status,
                    livestock_farm_blocks.lat,
                    livestock_farm_blocks.lon,
                    farmer_information.given_name,
                    farmer_information.surname,
                    ward.name as ward_name,
                    llg.name as llg_name,
                    district.name as district_name,
                    province.name as province_name')
            ->distinct()
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_ward ward', 'ward.id = livestock_farm_blocks.ward_id', 'left')
            ->join('adx_llg llg', 'llg.id = livestock_farm_blocks.llg_id', 'left')
            ->join('adx_district district', 'district.id = livestock_farm_blocks.district_id', 'left')
            ->join('adx_province province', 'province.id = livestock_farm_blocks.province_id', 'left')
            ->where('livestock_farm_blocks.created_by', session()->get('emp_id'))
            ->where('livestock_farm_blocks.status !=', 'deleted')
            ->orderBy('livestock_farm_blocks.id', 'ASC')
            ->findAll();

        // Remove duplicates at PHP level using block ID
        $unique_blocks = [];
        foreach ($blocks_data as $block) {
            $unique_blocks[$block['id']] = $block;
        }
        $blocks_data = array_values($unique_blocks);

        // Get livestock types
        $livestock_types = $this->livestockModel->where('status', 'active')->orderBy('livestock_name', 'ASC')->findAll();

        // Get livestock data for each block
        foreach ($blocks_data as &$block) {
            // Get the latest livestock data for each livestock type in this block
            $livestock_data = $this->livestockFarmDataModel
                ->select('livestock_farm_data.*, adx_livestock.livestock_name, adx_livestock.id as livestock_type_id')
                ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
                ->where('block_id', $block['id'])
                ->where('livestock_farm_data.status !=', 'deleted')
                ->orderBy('livestock_farm_data.action_date', 'DESC')
                ->findAll();

            // Group livestock data by type to avoid duplicates
            $grouped_livestock = [];
            $seen_records = [];
            
            foreach ($livestock_data as $data) {
                $type_id = $data['livestock_type_id'];
                $record_key = $type_id . '-' . $data['id'];
                
                // Skip if we've already processed this exact record
                if (isset($seen_records[$record_key])) {
                    continue;
                }
                
                // Track seen records
                $seen_records[$record_key] = true;
                
                // If we don't have this type yet, or this record is newer
                if (!isset($grouped_livestock[$type_id]) || 
                    strtotime($data['action_date']) > strtotime($grouped_livestock[$type_id]['action_date'])) {
                    $grouped_livestock[$type_id] = $data;
                }
            }
            
            // Convert grouped data to array and reset keys
            $block['livestock'] = array_values($grouped_livestock);
        }

        // Process statistics
        $stats = [
            'total_blocks' => count($blocks_data),
            'total_active' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'active')),
            'total_inactive' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'inactive')),
            'by_district' => [],
            'by_llg' => [],
            'by_ward' => []
        ];

        foreach ($blocks_data as $block) {
            // Count by district
            if (!empty($block['district_name'])) {
                if (!isset($stats['by_district'][$block['district_name']])) {
                    $stats['by_district'][$block['district_name']] = 0;
                }
                $stats['by_district'][$block['district_name']]++;
            }

            // Count by LLG
            if (!empty($block['llg_name'])) {
                if (!isset($stats['by_llg'][$block['llg_name']])) {
                    $stats['by_llg'][$block['llg_name']] = 0;
                }
                $stats['by_llg'][$block['llg_name']]++;
            }

            // Count by Ward
            if (!empty($block['ward_name'])) {
                if (!isset($stats['by_ward'][$block['ward_name']])) {
                    $stats['by_ward'][$block['ward_name']] = 0;
                }
                $stats['by_ward'][$block['ward_name']]++;
            }
        }

        $data = [
            'title' => 'Livestock Farm Blocks Reports',
            'page_header' => 'Livestock Farm Blocks Reports',
            'blocks_data' => $blocks_data,
            'livestock_types' => $livestock_types,
            'stats' => $stats
        ];

        return view('staff/reports/livestock_blocks', $data);
    }

    public function livestock_data()
    {
        // Get livestock farm data with details
        $livestock_data = $this->livestockFarmDataModel
            ->select('livestock_farm_data.*, livestock_farm_blocks.block_code, livestock_farm_blocks.status as block_status,
                    farmer_information.given_name, farmer_information.surname,
                    adx_livestock.livestock_name,
                    adx_district.name as district_name,
                    adx_llg.name as llg_name')
            ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
            ->join('adx_district', 'adx_district.id = livestock_farm_blocks.district_id')
            ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
            ->where('livestock_farm_blocks.created_by', session()->get('emp_id'))
            ->where('livestock_farm_data.status !=', 'deleted')
            ->findAll();

        // Process statistics
        $stats = [
            'total_records' => count($livestock_data),
            'total_male' => array_sum(array_column($livestock_data, 'he_total')),
            'total_female' => array_sum(array_column($livestock_data, 'she_total')),
            'by_livestock' => [],
            'by_district' => [],
            'by_llg' => [],
            'monthly_data' => array_fill(0, 12, ['count' => 0, 'total' => 0])
        ];

        foreach ($livestock_data as $record) {
            $total = $record['he_total'] + $record['she_total'];
            
            // Stats by district
            if (!isset($stats['by_district'][$record['district_name']])) {
                $stats['by_district'][$record['district_name']] = [
                    'total' => 0,
                    'count' => 0,
                    'total_cost' => 0,
                    'total_low_price' => 0,
                    'total_high_price' => 0,
                    'min_cost' => PHP_FLOAT_MAX,
                    'max_cost' => 0,
                    'min_low_price' => PHP_FLOAT_MAX,
                    'max_low_price' => 0,
                    'min_high_price' => PHP_FLOAT_MAX,
                    'max_high_price' => 0,
                    'by_livestock_type' => []
                ];
            }

            // Initialize livestock type stats for this district if not exists
            if (!isset($stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']])) {
                $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']] = [
                    'total' => 0,
                    'count' => 0,
                    'total_cost' => 0,
                    'total_low_price' => 0,
                    'total_high_price' => 0,
                    'min_cost' => PHP_FLOAT_MAX,
                    'max_cost' => 0,
                    'min_low_price' => PHP_FLOAT_MAX,
                    'max_low_price' => 0,
                    'min_high_price' => PHP_FLOAT_MAX,
                    'max_high_price' => 0
                ];
            }

            // Stats by LLG
            if (!isset($stats['by_llg'][$record['llg_name']])) {
                $stats['by_llg'][$record['llg_name']] = 0;
            }

            // Cost statistics
            $cost = floatval($record['cost_per_livestock']);
            $low_price = floatval($record['low_price_per_livestock']);
            $high_price = floatval($record['high_price_per_livestock']);

            // Update district totals
            $stats['by_district'][$record['district_name']]['total'] += $total;
            $stats['by_district'][$record['district_name']]['count']++;
            $stats['by_district'][$record['district_name']]['total_cost'] += $cost;
            $stats['by_district'][$record['district_name']]['total_low_price'] += $low_price;
            $stats['by_district'][$record['district_name']]['total_high_price'] += $high_price;
            
            // Update min/max values for district
            $stats['by_district'][$record['district_name']]['min_cost'] = min($stats['by_district'][$record['district_name']]['min_cost'], $cost);
            $stats['by_district'][$record['district_name']]['max_cost'] = max($stats['by_district'][$record['district_name']]['max_cost'], $cost);
            $stats['by_district'][$record['district_name']]['min_low_price'] = min($stats['by_district'][$record['district_name']]['min_low_price'], $low_price);
            $stats['by_district'][$record['district_name']]['max_low_price'] = max($stats['by_district'][$record['district_name']]['max_low_price'], $low_price);
            $stats['by_district'][$record['district_name']]['min_high_price'] = min($stats['by_district'][$record['district_name']]['min_high_price'], $high_price);
            $stats['by_district'][$record['district_name']]['max_high_price'] = max($stats['by_district'][$record['district_name']]['max_high_price'], $high_price);

            // Update livestock type stats for this district
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['total'] += $total;
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['count']++;
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['total_cost'] += $cost;
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['total_low_price'] += $low_price;
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['total_high_price'] += $high_price;
            
            // Update min/max values for livestock type in this district
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['min_cost'] = 
                min($stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['min_cost'], $cost);
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['max_cost'] = 
                max($stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['max_cost'], $cost);
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['min_low_price'] = 
                min($stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['min_low_price'], $low_price);
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['max_low_price'] = 
                max($stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['max_low_price'], $low_price);
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['min_high_price'] = 
                min($stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['min_high_price'], $high_price);
            $stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['max_high_price'] = 
                max($stats['by_district'][$record['district_name']]['by_livestock_type'][$record['livestock_name']]['max_high_price'], $high_price);

            // Update LLG totals
            $stats['by_llg'][$record['llg_name']] += $total;
        }

        $data = [
            'title' => 'Livestock Farm Data Reports',
            'page_header' => 'Livestock Farm Data Reports',
            'livestock_data' => $livestock_data,
            'stats' => $stats
        ];

        return view('staff/reports/livestock_data', $data);
    }

    // Helper method to export reports to Excel/PDF if needed
    protected function exportReport($data, $type)
    {
        // Implementation for exporting reports
    }
}
