<?php

namespace App\Models;

use CodeIgniter\Model;

class ExerciseOfficerModel extends Model
{
    protected $table      = 'exercise_officers';
    protected $primaryKey = 'id';
    
    protected $returnType     = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'exercise_id',
        'user_id',
        'created_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = '';
    protected $deletedField  = 'deleted_at';

    protected $validationRules = [
        'exercise_id' => 'required|integer',
        'user_id'     => 'required|integer',
        'created_by'  => 'required|integer'
    ];

    /**
     * Get officers for a specific exercise
     *
     * @param int $exerciseId
     * @return array
     */
    public function getOfficersByExercise($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)->findAll();
    }

    /**
     * Get exercises for a specific officer/user
     *
     * @param int $userId
     * @return array
     */
    public function getExercisesByUser($userId)
    {
        return $this->where('user_id', $userId)->findAll();
    }
} 