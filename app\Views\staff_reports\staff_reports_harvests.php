<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Harvests Report</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/reports') ?>">Reports</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Harvests</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Total Harvests</h6>
                            <h2 class="mt-2 mb-0"><?= number_format($stats['total_harvests']) ?></h2>
                        </div>
                        <div class="fs-1 text-primary">
                            <i class="fas fa-apple-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">Total Quantity</h6>
                            <h2 class="mt-2 mb-0"><?= number_format($stats['total_quantity']) ?></h2>
                        </div>
                        <div class="fs-1 text-success">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Harvests by Crop</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="cropChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Quantity by Crop</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="quantityChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Distribution by LLG</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="llgDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Trend Chart -->
    <div class="card mb-4">
        <div class="card-body">
            <h6 class="card-title">Monthly Harvest Trend (<?= date('Y') ?>)</h6>
            <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="trendChart"></canvas>
            </div>
        </div>
    </div>

    <!-- LLG Distribution Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribution by LLG</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>LLG</th>
                            <?php 
                            // Get unique crop names
                            $unique_crops = array_unique(array_map(function($item) {
                                return $item['crop_name'];
                            }, $harvest_data));
                            sort($unique_crops);
                            
                            foreach ($unique_crops as $crop_name): ?>
                                <th class="text-center" colspan="2"><?= esc($crop_name) ?></th>
                            <?php endforeach; ?>
                            <th class="text-center" colspan="2">Total</th>
                        </tr>
                        <tr>
                            <th></th>
                            <?php foreach ($unique_crops as $crop_name): ?>
                                <th class="text-center">Harvests</th>
                                <th class="text-center">Quantity</th>
                            <?php endforeach; ?>
                            <th class="text-center">Harvests</th>
                            <th class="text-center">Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Get unique LLGs and sort them
                        $unique_llgs = array_unique(array_map(function($item) {
                            return $item['llg_name'];
                        }, $harvest_data));
                        sort($unique_llgs);

                        // Initialize totals
                        $crop_totals = array_fill_keys($unique_crops, ['harvests' => 0, 'quantity' => 0]);
                        $grand_total = ['harvests' => 0, 'quantity' => 0];

                        // Group data by LLG and Crop
                        $llg_data = [];
                        foreach ($harvest_data as $harvest) {
                            $llg = $harvest['llg_name'];
                            $crop_name = $harvest['crop_name'];
                            
                            if (!isset($llg_data[$llg])) {
                                $llg_data[$llg] = array_fill_keys($unique_crops, ['harvests' => 0, 'quantity' => 0]);
                            }
                            
                            $llg_data[$llg][$crop_name]['harvests']++;
                            $llg_data[$llg][$crop_name]['quantity'] += (float)$harvest['quantity'];
                        }

                        // Display data
                        foreach ($unique_llgs as $llg):
                            $llg_total = ['harvests' => 0, 'quantity' => 0];
                        ?>
                            <tr>
                                <td><?= esc($llg) ?></td>
                                <?php foreach ($unique_crops as $crop_name): 
                                    $harvests = $llg_data[$llg][$crop_name]['harvests'] ?? 0;
                                    $quantity = $llg_data[$llg][$crop_name]['quantity'] ?? 0;
                                    
                                    // Update totals
                                    $crop_totals[$crop_name]['harvests'] += $harvests;
                                    $crop_totals[$crop_name]['quantity'] += $quantity;
                                    $llg_total['harvests'] += $harvests;
                                    $llg_total['quantity'] += $quantity;
                                ?>
                                    <td class="text-end"><?= $harvests ? number_format($harvests) : '-' ?></td>
                                    <td class="text-end"><?= $quantity ? number_format($quantity, 2) : '-' ?></td>
                                <?php endforeach; ?>
                                <td class="text-end fw-bold"><?= number_format($llg_total['harvests']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($llg_total['quantity'], 2) ?></td>
                            </tr>
                        <?php 
                            $grand_total['harvests'] += $llg_total['harvests'];
                            $grand_total['quantity'] += $llg_total['quantity'];
                        endforeach; 
                        ?>
                        <tr class="table-light">
                            <td class="fw-bold">Total</td>
                            <?php foreach ($unique_crops as $crop_name): ?>
                                <td class="text-end fw-bold"><?= number_format($crop_totals[$crop_name]['harvests']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($crop_totals[$crop_name]['quantity'], 2) ?></td>
                            <?php endforeach; ?>
                            <td class="text-end fw-bold"><?= number_format($grand_total['harvests']) ?></td>
                            <td class="text-end fw-bold"><?= number_format($grand_total['quantity'], 2) ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Table Card -->
    <div class="card">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-apple-alt me-2"></i>Harvest Records</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Block Code</th>
                            <th>Farmer Name</th>
                            <th>Crop</th>
                            <th>Item</th>
                            <th>Harvest Date</th>
                            <th>Quantity</th>
                            <th>Unit</th>
                            <th>Location</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Group data by block and keep only the latest harvest
                        $distinct_harvests = [];
                        foreach ($harvest_data as $harvest) {
                            $block_id = $harvest['block_id'];
                            $harvest_date = strtotime($harvest['harvest_date']);
                            
                            // If block not seen before or this harvest is more recent
                            if (!isset($distinct_harvests[$block_id]) || 
                                strtotime($distinct_harvests[$block_id]['harvest_date']) < $harvest_date) {
                                $distinct_harvests[$block_id] = $harvest;
                            }
                        }

                        // Sort by latest harvest date
                        uasort($distinct_harvests, function($a, $b) {
                            return strtotime($b['harvest_date']) - strtotime($a['harvest_date']);
                        });

                        $loop_count = 1;
                        foreach ($distinct_harvests as $harvest): ?>
                            <tr>
                                <td><?= $loop_count++ ?></td>
                                <td><?= esc($harvest['block_code']) ?></td>
                                <td><?= esc($harvest['given_name']) . ' ' . esc($harvest['surname']) ?></td>
                                <td><?= esc($harvest['crop_name']) ?></td>
                                <td><?= esc($harvest['item']) ?></td>
                                <td>
                                    <?php 
                                    $harvest_date = strtotime($harvest['harvest_date']);
                                    $is_recent = (time() - $harvest_date) < (7 * 24 * 60 * 60); // Within last 7 days
                                    ?>
                                    <?= date('d M Y', $harvest_date) ?>
                                    <?php if ($is_recent): ?>
                                        <span class="badge bg-success ms-1" title="Recent harvest">New</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end"><?= number_format($harvest['quantity'], 2) ?></td>
                                <td><?= esc($harvest['unit_of_measure']) ?></td>
                                <td><?= esc($harvest['llg_name']) ?></td>
                                <td>
                                    <span class="badge bg-<?= $harvest['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($harvest['status']) ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    const stats = <?= json_encode($stats) ?>;
    const harvest_data = <?= json_encode($harvest_data) ?>;
    
    // Chart configurations
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 15
                }
            }
        }
    };

    // Process data for charts
    const cropStats = harvest_data.reduce((acc, harvest) => {
        if (!acc[harvest.crop_name]) {
            acc[harvest.crop_name] = {
                count: 0,
                quantity: 0,
                color: harvest.crop_color_code
            };
        }
        acc[harvest.crop_name].count++;
        acc[harvest.crop_name].quantity += parseFloat(harvest.quantity);
        return acc;
    }, {});

    // Process data for LLG distribution
    const llgStats = harvest_data.reduce((acc, harvest) => {
        const llgName = harvest.llg_name || 'Unknown';
        if (!acc[llgName]) {
            acc[llgName] = {
                crops: {}
            };
        }
        if (!acc[llgName].crops[harvest.crop_name]) {
            acc[llgName].crops[harvest.crop_name] = {
                count: 0,
                quantity: 0,
                color: harvest.crop_color_code
            };
        }
        acc[llgName].crops[harvest.crop_name].count++;
        acc[llgName].crops[harvest.crop_name].quantity += parseFloat(harvest.quantity);
        return acc;
    }, {});

    // Harvests by Crop Chart
    new Chart(document.getElementById('cropChart'), {
        type: 'pie',
        data: {
            labels: Object.keys(cropStats),
            datasets: [{
                data: Object.values(cropStats).map(crop => crop.count),
                backgroundColor: Object.values(cropStats).map(crop => crop.color)
            }]
        },
        options: chartOptions
    });

    // Quantity by Crop Chart
    new Chart(document.getElementById('quantityChart'), {
        type: 'bar',
        data: {
            labels: Object.keys(cropStats),
            datasets: [{
                label: 'Quantity',
                data: Object.values(cropStats).map(crop => crop.quantity),
                backgroundColor: Object.values(cropStats).map(crop => crop.color)
            }]
        },
        options: {
            ...chartOptions,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2);
                        }
                    }
                }
            }
        }
    });

    // LLG Distribution Chart
    const llgLabels = Object.keys(llgStats);
    const cropTypes = [...new Set(harvest_data.map(d => d.crop_name))];
    const llgDatasets = cropTypes.map(cropName => ({
        label: cropName,
        data: llgLabels.map(llg => llgStats[llg].crops[cropName]?.quantity || 0),
        backgroundColor: harvest_data.find(d => d.crop_name === cropName)?.crop_color_code
    }));

    new Chart(document.getElementById('llgDistributionChart'), {
        type: 'bar',
        data: {
            labels: llgLabels,
            datasets: llgDatasets
        },
        options: {
            ...chartOptions,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2);
                        }
                    }
                }
            }
        }
    });

    // Monthly Trend Chart
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthlyStats = Array(12).fill().map(() => ({ count: 0, quantity: 0 }));

    harvest_data.forEach(harvest => {
        const month = new Date(harvest.harvest_date).getMonth();
        monthlyStats[month].count++;
        monthlyStats[month].quantity += parseFloat(harvest.quantity);
    });

    new Chart(document.getElementById('trendChart'), {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'Harvests',
                data: monthlyStats.map(stat => stat.count),
                borderColor: '#4BC0C0',
                backgroundColor: '#4BC0C020',
                fill: true
            }]
        },
        options: {
            ...chartOptions,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
