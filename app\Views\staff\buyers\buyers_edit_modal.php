<!-- File Path: app/Views/staff/buyers/buyers_edit_modal.php -->
<!-- Edit Buyer Modal -->
<div class="modal fade" id="editBuyerModal" tabindex="-1" aria-labelledby="editBuyerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editBuyerModalLabel"><i class="fas fa-edit"></i> Edit Crop Buyer</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <!-- Form starts here -->
            <?= form_open('staff/buyers/update', ['id' => 'editBuyerForm']) ?>
            <div class="modal-body">
                <!-- Hidden CSRF Field -->
                <?= csrf_field() ?>
                <!-- Hidden field for Buyer ID -->
                <input type="hidden" name="edit_buyer_id" id="edit_buyer_id" value="">
                
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="edit_crop_id" class="form-label">Crop <span class="text-danger">*</span></label>
                        <select class="form-select" id="edit_crop_id" name="edit_crop_id" required>
                            <option value="">Select Crop...</option>
                            <?php if (!empty($crops)): ?>
                                <?php foreach ($crops as $crop): ?>
                                    <option value="<?= esc($crop['id']) ?>"><?= esc($crop['crop_name']) ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                         <!-- Placeholder for validation error -->
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="edit_name" class="form-label">Buyer Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="edit_name" required>
                         <!-- Placeholder for validation error -->
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="edit_contact_number" class="form-label">Contact Number</label>
                        <input type="text" class="form-control" id="edit_contact_number" name="edit_contact_number">
                         <!-- Placeholder for validation error -->
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="edit_email">
                         <!-- Placeholder for validation error -->
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="edit_operation_span" class="form-label">Operation Span <span class="text-danger">*</span></label>
                        <select class="form-select" id="edit_operation_span" name="edit_operation_span" required>
                            <option value="">Select Operation Span...</option>
                            <option value="local">Local (Province Wide)</option>
                            <option value="national">National (Country Wide)</option>
                        </select>
                         <!-- Placeholder for validation error -->
                    </div>
                </div>

                <div class="mb-3">
                    <label for="edit_address" class="form-label">Address</label>
                    <textarea class="form-control" id="edit_address" name="edit_address" rows="2"></textarea>
                     <!-- Placeholder for validation error -->
                </div>

                <div class="mb-3">
                    <label for="edit_description" class="form-label">Description / Notes</label>
                    <textarea class="form-control" id="edit_description" name="edit_description" rows="3"></textarea>
                     <!-- Placeholder for validation error -->
                </div>

            </div> <!-- /modal-body -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Update Buyer</button>
            </div>
            <?= form_close() ?>
            <!-- Form ends here -->
        </div>
    </div>
</div>
<!-- /Edit Buyer Modal --> 