<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;
use App\Models\AdxCountryModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\CropsModel;
use App\Models\FertilizersModel;
use App\Models\PesticidesModel;
use App\Models\LivestockModel;
use App\Models\EducationModel;
use App\Models\DakoiiOrgModel;
use App\Models\DakoiiUsersModel;
use App\Models\UsersModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\ExerciseModel;

class DummyDataSeeder extends Seeder
{
    public function run()
    {
        // Start with master data and work down the hierarchy
        $this->call('CreateMasterData');
        $this->call('CreateGeographicData');
        $this->call('CreateOrganizationsAndUsers');
        $this->call('CreateExercises');
        $this->call('CreateFarmersData');
        $this->call('CreateFarmBlocksData');
        $this->call('CreateAgriculturalData');
        $this->call('CreateExtensionData');
        
        echo "✅ Dummy data seeding completed successfully!\n";
    }

    // 1. Master Data (crops, fertilizers, etc.)
    private function CreateMasterData()
    {
        echo "🌱 Creating master data...\n";
        
        // Countries
        $countryModel = new AdxCountryModel();
        $countries = [
            ['name' => 'Papua New Guinea', 'code' => 'PG', 'created_at' => date('Y-m-d H:i:s')],
            ['name' => 'Solomon Islands', 'code' => 'SB', 'created_at' => date('Y-m-d H:i:s')],
            ['name' => 'Vanuatu', 'code' => 'VU', 'created_at' => date('Y-m-d H:i:s')]
        ];
        foreach ($countries as $country) {
            $countryModel->insert($country);
        }

        // Crops
        $cropsModel = new CropsModel();
        $crops = [
            ['crop_name' => 'Sweet Potato', 'crop_icon' => 'sweet-potato.png', 'crop_color_code' => '#FF6B35', 'remarks' => 'Staple root vegetable', 'created_by' => 1],
            ['crop_name' => 'Taro', 'crop_icon' => 'taro.png', 'crop_color_code' => '#8B4513', 'remarks' => 'Traditional root crop', 'created_by' => 1],
            ['crop_name' => 'Banana', 'crop_icon' => 'banana.png', 'crop_color_code' => '#FFD700', 'remarks' => 'Popular fruit crop', 'created_by' => 1],
            ['crop_name' => 'Coconut', 'crop_icon' => 'coconut.png', 'crop_color_code' => '#8B4513', 'remarks' => 'Commercial palm crop', 'created_by' => 1],
            ['crop_name' => 'Coffee', 'crop_icon' => 'coffee.png', 'crop_color_code' => '#6F4E37', 'remarks' => 'Export cash crop', 'created_by' => 1],
            ['crop_name' => 'Cocoa', 'crop_icon' => 'cocoa.png', 'crop_color_code' => '#D2691E', 'remarks' => 'Export cash crop', 'created_by' => 1]
        ];
        foreach ($crops as $crop) {
            $cropsModel->insert($crop);
        }

        // Fertilizers
        $fertilizerModel = new FertilizersModel();
        $fertilizers = [
            ['name' => 'NPK 15-15-15', 'icon' => 'npk.png', 'color_code' => '#32CD32', 'remarks' => 'Balanced fertilizer', 'created_by' => 1],
            ['name' => 'Urea', 'icon' => 'urea.png', 'color_code' => '#FFFFFF', 'remarks' => 'Nitrogen fertilizer', 'created_by' => 1],
            ['name' => 'Super Phosphate', 'icon' => 'phosphate.png', 'color_code' => '#D3D3D3', 'remarks' => 'Phosphorus fertilizer', 'created_by' => 1],
            ['name' => 'Potash', 'icon' => 'potash.png', 'color_code' => '#FF69B4', 'remarks' => 'Potassium fertilizer', 'created_by' => 1],
            ['name' => 'Organic Compost', 'icon' => 'compost.png', 'color_code' => '#8B4513', 'remarks' => 'Organic matter', 'created_by' => 1]
        ];
        foreach ($fertilizers as $fertilizer) {
            $fertilizerModel->insert($fertilizer);
        }

        // Pesticides
        $pesticideModel = new PesticidesModel();
        $pesticides = [
            ['name' => 'Roundup', 'icon' => 'herbicide.png', 'color_code' => '#228B22', 'remarks' => 'Glyphosate herbicide', 'created_by' => 1],
            ['name' => 'Malathion', 'icon' => 'insecticide.png', 'color_code' => '#DC143C', 'remarks' => 'Insecticide', 'created_by' => 1],
            ['name' => 'Copper Fungicide', 'icon' => 'fungicide.png', 'color_code' => '#B87333', 'remarks' => 'Fungal control', 'created_by' => 1],
            ['name' => 'Neem Oil', 'icon' => 'neem.png', 'color_code' => '#6B8E23', 'remarks' => 'Organic pesticide', 'created_by' => 1]
        ];
        foreach ($pesticides as $pesticide) {
            $pesticideModel->insert($pesticide);
        }

        // Livestock
        $livestockModel = new LivestockModel();
        $livestock = [
            ['name' => 'Pig', 'icon' => 'pig.png', 'color_code' => '#FFC0CB', 'remarks' => 'Common livestock', 'created_by' => 1],
            ['name' => 'Chicken', 'icon' => 'chicken.png', 'color_code' => '#FFFF00', 'remarks' => 'Poultry', 'created_by' => 1],
            ['name' => 'Duck', 'icon' => 'duck.png', 'color_code' => '#87CEEB', 'remarks' => 'Water fowl', 'created_by' => 1],
            ['name' => 'Cattle', 'icon' => 'cattle.png', 'color_code' => '#8B4513', 'remarks' => 'Large livestock', 'created_by' => 1],
            ['name' => 'Goat', 'icon' => 'goat.png', 'color_code' => '#DEB887', 'remarks' => 'Small ruminant', 'created_by' => 1]
        ];
        foreach ($livestock as $animal) {
            $livestockModel->insert($animal);
        }

        // Education levels
        $educationModel = new EducationModel();
        $education = [
            ['name' => 'No Formal Education', 'icon' => 'no-education.png', 'color_code' => '#808080', 'created_by' => 1],
            ['name' => 'Primary School', 'icon' => 'primary.png', 'color_code' => '#90EE90', 'created_by' => 1],
            ['name' => 'Secondary School', 'icon' => 'secondary.png', 'color_code' => '#87CEEB', 'created_by' => 1],
            ['name' => 'High School', 'icon' => 'high-school.png', 'color_code' => '#DDA0DD', 'created_by' => 1],
            ['name' => 'Technical/Vocational', 'icon' => 'vocational.png', 'color_code' => '#F0E68C', 'created_by' => 1],
            ['name' => 'University/College', 'icon' => 'university.png', 'color_code' => '#FFD700', 'created_by' => 1]
        ];
        foreach ($education as $edu) {
            $educationModel->insert($edu);
        }

        echo "✅ Master data created\n";
    }

    // 2. Geographic hierarchy
    private function CreateGeographicData()
    {
        echo "🗺️ Creating geographic data...\n";

        // Provinces
        $provinceModel = new AdxProvinceModel();
        $provinces = [
            ['provincecode' => 'WP', 'name' => 'Western Province', 'country_id' => 1, 'json_id' => 'pg_western'],
            ['provincecode' => 'SP', 'name' => 'Southern Highlands Province', 'country_id' => 1, 'json_id' => 'pg_southern_highlands'],
            ['provincecode' => 'EP', 'name' => 'Eastern Highlands Province', 'country_id' => 1, 'json_id' => 'pg_eastern_highlands'],
            ['provincecode' => 'MP', 'name' => 'Morobe Province', 'country_id' => 1, 'json_id' => 'pg_morobe'],
            ['provincecode' => 'NCD', 'name' => 'National Capital District', 'country_id' => 1, 'json_id' => 'pg_ncd']
        ];
        foreach ($provinces as $province) {
            $provinceModel->insert($province);
        }

        // Districts
        $districtModel = new AdxDistrictModel();
        $districts = [
            ['districtcode' => 'WP001', 'name' => 'Daru District', 'country_id' => 1, 'province_id' => 1, 'json_id' => 'daru_district'],
            ['districtcode' => 'WP002', 'name' => 'Kiunga District', 'country_id' => 1, 'province_id' => 1, 'json_id' => 'kiunga_district'],
            ['districtcode' => 'SP001', 'name' => 'Mendi District', 'country_id' => 1, 'province_id' => 2, 'json_id' => 'mendi_district'],
            ['districtcode' => 'SP002', 'name' => 'Nipa District', 'country_id' => 1, 'province_id' => 2, 'json_id' => 'nipa_district'],
            ['districtcode' => 'EP001', 'name' => 'Goroka District', 'country_id' => 1, 'province_id' => 3, 'json_id' => 'goroka_district']
        ];
        foreach ($districts as $district) {
            $districtModel->insert($district);
        }

        // LLGs
        $llgModel = new AdxLlgModel();
        $llgs = [
            ['llgcode' => 'LLG001', 'name' => 'Daru Urban LLG', 'country_id' => 1, 'province_id' => 1, 'district_id' => 1, 'json_id' => 'daru_urban'],
            ['llgcode' => 'LLG002', 'name' => 'Daru Rural LLG', 'country_id' => 1, 'province_id' => 1, 'district_id' => 1, 'json_id' => 'daru_rural'],
            ['llgcode' => 'LLG003', 'name' => 'Kiunga Urban LLG', 'country_id' => 1, 'province_id' => 1, 'district_id' => 2, 'json_id' => 'kiunga_urban'],
            ['llgcode' => 'LLG004', 'name' => 'Mendi Urban LLG', 'country_id' => 1, 'province_id' => 2, 'district_id' => 3, 'json_id' => 'mendi_urban'],
            ['llgcode' => 'LLG005', 'name' => 'Goroka Urban LLG', 'country_id' => 1, 'province_id' => 3, 'district_id' => 5, 'json_id' => 'goroka_urban']
        ];
        foreach ($llgs as $llg) {
            $llgModel->insert($llg);
        }

        // Wards
        $wardModel = new AdxWardModel();
        $wards = [
            ['wardcode' => 1001, 'name' => 'Ward 1 Daru', 'country_id' => 1, 'province_id' => 1, 'district_id' => 1, 'llg_id' => 1],
            ['wardcode' => 1002, 'name' => 'Ward 2 Daru', 'country_id' => 1, 'province_id' => 1, 'district_id' => 1, 'llg_id' => 1],
            ['wardcode' => 1003, 'name' => 'Ward 3 Daru Rural', 'country_id' => 1, 'province_id' => 1, 'district_id' => 1, 'llg_id' => 2],
            ['wardcode' => 2001, 'name' => 'Ward 1 Kiunga', 'country_id' => 1, 'province_id' => 1, 'district_id' => 2, 'llg_id' => 3],
            ['wardcode' => 3001, 'name' => 'Ward 1 Mendi', 'country_id' => 1, 'province_id' => 2, 'district_id' => 3, 'llg_id' => 4],
            ['wardcode' => 4001, 'name' => 'Ward 1 Goroka', 'country_id' => 1, 'province_id' => 3, 'district_id' => 5, 'llg_id' => 5],
            ['wardcode' => 4002, 'name' => 'Ward 2 Goroka', 'country_id' => 1, 'province_id' => 3, 'district_id' => 5, 'llg_id' => 5]
        ];
        foreach ($wards as $ward) {
            $wardModel->insert($ward);
        }

        echo "✅ Geographic data created\n";
    }

    // 3. Organizations and Users
    private function CreateOrganizationsAndUsers()
    {
        echo "🏢 Creating organizations and users...\n";

        // Organizations
        $orgModel = new DakoiiOrgModel();
        $organizations = [
            [
                'orgcode' => 'PNG-AGRI-001',
                'name' => 'PNG Department of Agriculture',
                'description' => 'Primary agricultural development organization',
                'addlockprov' => 'All Provinces',
                'addlockcountry' => 'Papua New Guinea',
                'orglogo' => 'png_agri_logo.png',
                'is_locationlocked' => 0,
                'province_json' => '["1","2","3","4","5"]',
                'district_json' => '["1","2","3","4","5"]',
                'llg_json' => '["1","2","3","4","5"]',
                'is_active' => 1,
                'license_status' => 'active'
            ],
            [
                'orgcode' => 'WEST-AGR-002',
                'name' => 'Western Province Agriculture',
                'description' => 'Regional agricultural services for Western Province',
                'addlockprov' => 'Western Province',
                'addlockcountry' => 'Papua New Guinea',
                'orglogo' => 'western_agri_logo.png',
                'is_locationlocked' => 1,
                'province_json' => '["1"]',
                'district_json' => '["1","2"]',
                'llg_json' => '["1","2","3"]',
                'is_active' => 1,
                'license_status' => 'active'
            ]
        ];
        foreach ($organizations as $org) {
            $orgModel->insert($org);
        }

        // Dakoii System Users
        $dakoiiUserModel = new DakoiiUsersModel();
        $dakoiiUsers = [
            [
                'name' => 'System Administrator',
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'admin',
                'is_active' => 1
            ],
            [
                'name' => 'Data Manager',
                'username' => 'datamanager',
                'password' => password_hash('data123', PASSWORD_DEFAULT),
                'role' => 'manager',
                'is_active' => 1
            ]
        ];
        foreach ($dakoiiUsers as $user) {
            $dakoiiUserModel->insert($user);
        }

        // Organization Users
        $userModel = new UsersModel();
        $users = [
            [
                'sys_no' => 2024001,
                'org_id' => 1,
                'name' => 'John Smith',
                'password' => password_hash('field123', PASSWORD_DEFAULT),
                'role' => 'user',
                'is_admin' => 1,
                'is_supervisor' => 1,
                'position' => 'Senior Agricultural Officer',
                'id_photo' => 'john_smith.jpg',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'status' => 1,
                'created_by' => '1'
            ],
            [
                'sys_no' => 2024002,
                'org_id' => 1,
                'name' => 'Mary Wilson',
                'password' => password_hash('field123', PASSWORD_DEFAULT),
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 1,
                'position' => 'Field Extension Officer',
                'id_photo' => 'mary_wilson.jpg',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'status' => 1,
                'created_by' => '1'
            ],
            [
                'sys_no' => 2024003,
                'org_id' => 2,
                'name' => 'Peter Johnson',
                'password' => password_hash('field123', PASSWORD_DEFAULT),
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 0,
                'position' => 'Data Collection Officer',
                'id_photo' => 'peter_johnson.jpg',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'status' => 1,
                'created_by' => '1'
            ]
        ];
        foreach ($users as $user) {
            $userModel->insert($user);
        }

        echo "✅ Organizations and users created\n";
    }

    // 4. Exercises (Data Collection Periods)
    private function CreateExercises()
    {
        echo "📊 Creating exercises...\n";

        $exerciseModel = new ExerciseModel();
        $exercises = [
            [
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'title' => 'Western Province Agricultural Survey 2024 Q1',
                'description' => 'Comprehensive agricultural data collection covering crops, livestock, and farm practices',
                'date_from' => '2024-01-01',
                'date_to' => '2024-03-31',
                'officer_responsible_id' => 1,
                'status' => 'active',
                'status_at' => date('Y-m-d H:i:s'),
                'status_by' => 1,
                'status_remarks' => 'Active data collection period',
                'created_by' => 1
            ],
            [
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 2,
                'district_id' => 3,
                'title' => 'Southern Highlands Coffee Assessment 2024',
                'description' => 'Specialized assessment of coffee production and quality in Southern Highlands',
                'date_from' => '2024-02-01',
                'date_to' => '2024-04-30',
                'officer_responsible_id' => 2,
                'status' => 'active',
                'status_at' => date('Y-m-d H:i:s'),
                'status_by' => 1,
                'status_remarks' => 'Coffee season assessment',
                'created_by' => 1
            ],
            [
                'org_id' => 2,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 2,
                'title' => 'Kiunga District Baseline Study 2024',
                'description' => 'Baseline agricultural data collection for development planning',
                'date_from' => '2024-01-15',
                'date_to' => '2024-06-15',
                'officer_responsible_id' => 3,
                'status' => 'active',
                'status_at' => date('Y-m-d H:i:s'),
                'status_by' => 1,
                'status_remarks' => 'Baseline data collection',
                'created_by' => 1
            ]
        ];
        foreach ($exercises as $exercise) {
            $exerciseModel->insert($exercise);
        }

        echo "✅ Exercises created\n";
    }

    // 5. Farmers Data
    private function CreateFarmersData()
    {
        echo "👨‍🌾 Creating farmers data...\n";

        $farmerModel = new FarmerInformationModel();
        $farmers = [
            [
                'org_id' => 1,
                'farmer_code' => 'F001',
                'given_name' => 'Michael',
                'surname' => 'Temu',
                'date_of_birth' => '1975-05-15',
                'gender' => 'Male',
                'village' => 'Boboa',
                'ward_id' => 1,
                'llg_id' => 1,
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'phone' => '+************',
                'email' => '',
                'address' => 'Boboa Village, Daru',
                'marital_status' => 'Married',
                'highest_education_id' => 2,
                'course_taken' => '',
                'id_photo' => 'michael_temu.jpg',
                'created_by' => 1,
                'status' => 'active'
            ],
            [
                'org_id' => 1,
                'farmer_code' => 'F002',
                'given_name' => 'Sarah',
                'surname' => 'Kila',
                'date_of_birth' => '1982-08-22',
                'gender' => 'Female',
                'village' => 'Daru Town',
                'ward_id' => 2,
                'llg_id' => 1,
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'phone' => '+************',
                'email' => '<EMAIL>',
                'address' => 'Daru Town Ward 2',
                'marital_status' => 'Single',
                'highest_education_id' => 3,
                'course_taken' => 'Basic Agriculture',
                'id_photo' => 'sarah_kila.jpg',
                'created_by' => 2,
                'status' => 'active'
            ],
            [
                'org_id' => 1,
                'farmer_code' => 'F003',
                'given_name' => 'James',
                'surname' => 'Waigani',
                'date_of_birth' => '1968-12-10',
                'gender' => 'Male',
                'village' => 'Sigabadaru',
                'ward_id' => 3,
                'llg_id' => 2,
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'phone' => '+************',
                'email' => '',
                'address' => 'Sigabadaru Village',
                'marital_status' => 'Married',
                'highest_education_id' => 1,
                'course_taken' => '',
                'id_photo' => 'james_waigani.jpg',
                'created_by' => 1,
                'status' => 'active'
            ],
            [
                'org_id' => 2,
                'farmer_code' => 'F004',
                'given_name' => 'Grace',
                'surname' => 'Mendi',
                'date_of_birth' => '1978-03-18',
                'gender' => 'Female',
                'village' => 'Kiunga Central',
                'ward_id' => 4,
                'llg_id' => 3,
                'district_id' => 2,
                'province_id' => 1,
                'country_id' => 1,
                'phone' => '+************',
                'email' => '<EMAIL>',
                'address' => 'Kiunga Central Ward 1',
                'marital_status' => 'Married',
                'highest_education_id' => 4,
                'course_taken' => 'Agricultural Extension',
                'id_photo' => 'grace_mendi.jpg',
                'created_by' => 3,
                'status' => 'active'
            ],
            [
                'org_id' => 1,
                'farmer_code' => 'F005',
                'given_name' => 'Paul',
                'surname' => 'Highland',
                'date_of_birth' => '1985-07-25',
                'gender' => 'Male',
                'village' => 'Mendi Town',
                'ward_id' => 5,
                'llg_id' => 4,
                'district_id' => 3,
                'province_id' => 2,
                'country_id' => 1,
                'phone' => '+************',
                'email' => '<EMAIL>',
                'address' => 'Mendi Town Ward 1',
                'marital_status' => 'Single',
                'highest_education_id' => 5,
                'course_taken' => 'Coffee Production',
                'id_photo' => 'paul_highland.jpg',
                'created_by' => 2,
                'status' => 'active'
            ]
        ];
        
        foreach ($farmers as $farmer) {
            $farmerModel->insert($farmer);
        }

        echo "✅ Farmers data created\n";
    }

    // 6. Farm Blocks Data
    private function CreateFarmBlocksData()
    {
        echo "🏞️ Creating farm blocks data...\n";

        // Crop Farm Blocks
        $cropBlockModel = new CropsFarmBlockModel();
        $cropBlocks = [
            [
                'exercise_id' => 1,
                'farmer_id' => 1,
                'crop_id' => 1, // Sweet Potato
                'block_code' => 'CB001',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => 'Boboa',
                'block_site' => 'Boboa Garden Block A',
                'lon' => '143.2092',
                'lat' => '-9.0763',
                'remarks' => 'Main sweet potato garden',
                'status' => 'active',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'exercise_id' => 1,
                'farmer_id' => 1,
                'crop_id' => 2, // Taro
                'block_code' => 'CB002',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => 'Boboa',
                'block_site' => 'Boboa Wetland Block',
                'lon' => '143.2105',
                'lat' => '-9.0770',
                'remarks' => 'Taro grown in wetland area',
                'status' => 'active',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'exercise_id' => 1,
                'farmer_id' => 2,
                'crop_id' => 3, // Banana
                'block_code' => 'CB003',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 2,
                'village' => 'Daru Town',
                'block_site' => 'Urban Banana Grove',
                'lon' => '143.2115',
                'lat' => '-9.0750',
                'remarks' => 'Commercial banana production',
                'status' => 'active',
                'created_by' => 2,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'exercise_id' => 2,
                'farmer_id' => 5,
                'crop_id' => 5, // Coffee
                'block_code' => 'CB004',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 2,
                'district_id' => 3,
                'llg_id' => 4,
                'ward_id' => 5,
                'village' => 'Mendi Town',
                'block_site' => 'Highland Coffee Plantation',
                'lon' => '143.6565',
                'lat' => '-6.1478',
                'remarks' => 'Arabica coffee plantation',
                'status' => 'active',
                'created_by' => 2,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'exercise_id' => 3,
                'farmer_id' => 4,
                'crop_id' => 4, // Coconut
                'block_code' => 'CB005',
                'org_id' => 2,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 2,
                'llg_id' => 3,
                'ward_id' => 4,
                'village' => 'Kiunga Central',
                'block_site' => 'Riverside Coconut Grove',
                'lon' => '141.2957',
                'lat' => '-6.1214',
                'remarks' => 'Mixed age coconut palms',
                'status' => 'active',
                'created_by' => 3,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        foreach ($cropBlocks as $block) {
            $cropBlockModel->insert($block);
        }

        // Livestock Farm Blocks
        $livestockBlockModel = new LivestockFarmBlockModel();
        $livestockBlocks = [
            [
                'exercise_id' => 1,
                'farmer_id' => 1,
                'block_code' => 'LB001',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => 'Boboa',
                'block_site' => 'Boboa Pig Farm',
                'lon' => '143.2088',
                'lat' => '-9.0768',
                'remarks' => 'Small scale pig farming',
                'status' => 'active',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'exercise_id' => 1,
                'farmer_id' => 2,
                'block_code' => 'LB002',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 2,
                'village' => 'Daru Town',
                'block_site' => 'Urban Poultry House',
                'lon' => '143.2120',
                'lat' => '-9.0745',
                'remarks' => 'Chicken and duck farming',
                'status' => 'active',
                'created_by' => 2,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'exercise_id' => 3,
                'farmer_id' => 4,
                'block_code' => 'LB003',
                'org_id' => 2,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 2,
                'llg_id' => 3,
                'ward_id' => 4,
                'village' => 'Kiunga Central',
                'block_site' => 'Kiunga Cattle Station',
                'lon' => '141.2960',
                'lat' => '-6.1220',
                'remarks' => 'Small cattle grazing area',
                'status' => 'active',
                'created_by' => 3,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        foreach ($livestockBlocks as $block) {
            $livestockBlockModel->insert($block);
        }

        echo "✅ Farm blocks data created\n";
    }
}