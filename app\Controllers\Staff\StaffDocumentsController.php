<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\DocumentsFolderModel;
use App\Models\DocumentFilesModel;
use CodeIgniter\HTTP\ResponseInterface;

class StaffDocumentsController extends BaseController
{
    protected $documentsFolderModel;
    protected $documentFilesModel;
    protected $helpers = ['form', 'url', 'info'];

    public function __construct()
    {
        // Load helpers
        foreach ($this->helpers as $helper) {
            helper($helper);
        }

        // Initialize models
        $this->documentsFolderModel = new DocumentsFolderModel();
        $this->documentFilesModel = new DocumentFilesModel();

        // Verify staff role - be more flexible for testing
        if (ENVIRONMENT !== 'development' && (!session()->get('logged_in') || session()->get('role') != "user")) {
            throw new \Exception('Unauthorized access');
        }

        // Debug session data
        log_message('debug', 'Session data: ' . json_encode(session()->get()));
    }

    /**
     * Display a listing of document folders
     *
     * @return ResponseInterface
     */
    public function index()
    {
        $folders = $this->documentsFolderModel->getFoldersWithParent();

        $data = [
            'title' => 'Document Folders',
            'folders' => $folders
        ];

        return view('staff/staff_documents/staff_documents_index', $data);
    }

    /**
     * Show the form for creating a new folder
     *
     * @return ResponseInterface
     */
    public function create()
    {
        // Get all folders for parent folder dropdown
        $folders = $this->documentsFolderModel->findAll();

        $data = [
            'title' => 'Create Document Folder',
            'folders' => $folders
        ];

        return view('staff/staff_documents/staff_documents_folder_create', $data);
    }

    /**
     * Store a newly created folder in the database
     *
     * @return ResponseInterface
     */
    public function store()
    {
        $data = $this->request->getPost();

        // Add user ID as created_by - use emp_id which is the standard in this application
        // If emp_id is not available, try id, and if that's not available, use 1 as default
        $data['created_by'] = session()->get('emp_id') ?? session()->get('id') ?? 1;

        // Set location data from session or use defaults
        $data['country_id'] = session()->get('country_id') ?? 1;
        $data['province_id'] = session()->get('province_id') ?? 1;
        $data['district_id'] = session()->get('district_id') ?? null;
        $data['llg_id'] = session()->get('llg_id') ?? null;

        // Set status to active by default
        $data['status'] = 1;

        // Debug information
        log_message('debug', 'Creating folder with data: ' . json_encode($data));

        try {
            // Attempt to insert the data
            if (!$this->documentsFolderModel->insert($data)) {
                log_message('error', 'Validation errors: ' . json_encode($this->documentsFolderModel->errors()));
                return redirect()->back()->withInput()->with('error', 'Failed to create folder: ' . implode(', ', $this->documentsFolderModel->errors()));
            }

            return redirect()->to(base_url('staff/office/documents'))->with('success', 'Folder created successfully');
        } catch (\Exception $e) {
            log_message('error', 'Exception when creating folder: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to create folder: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified folder
     *
     * @param int $id
     * @return ResponseInterface
     */
    public function edit($id)
    {
        $folder = $this->documentsFolderModel->find($id);

        if (!$folder) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'Folder not found');
        }

        // Get all folders for parent folder dropdown (excluding the current folder)
        $folders = $this->documentsFolderModel->where('id !=', $id)->findAll();

        $data = [
            'title' => 'Edit Document Folder',
            'folder' => $folder,
            'folders' => $folders
        ];

        return view('staff/staff_documents/staff_documents_folder_edit', $data);
    }

    /**
     * Update the specified folder in the database
     *
     * @param int $id
     * @return ResponseInterface
     */
    public function update($id)
    {
        $folder = $this->documentsFolderModel->find($id);

        if (!$folder) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'Folder not found');
        }

        $data = $this->request->getPost();
        $data['updated_by'] = session()->get('emp_id') ?? session()->get('id') ?? 1;

        if ($this->documentsFolderModel->update($id, $data)) {
            return redirect()->to(base_url('staff/office/documents'))->with('success', 'Folder updated successfully');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update folder');
    }

    /**
     * Remove the specified folder from the database
     *
     * @param int $id
     * @return ResponseInterface
     */
    public function delete($id)
    {
        $folder = $this->documentsFolderModel->find($id);

        if (!$folder) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'Folder not found');
        }

        // Check if folder has files
        $files = $this->documentFilesModel->where('folder_id', $id)->findAll();
        if (!empty($files)) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'Cannot delete folder with files. Please delete files first.');
        }

        // Check if folder has subfolders
        $subfolders = $this->documentsFolderModel->where('parent_folder_id', $id)->findAll();
        if (!empty($subfolders)) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'Cannot delete folder with subfolders. Please delete subfolders first.');
        }

        // Set deleted_by field
        $this->documentsFolderModel->update($id, ['deleted_by' => session()->get('emp_id') ?? session()->get('id') ?? 1]);

        if ($this->documentsFolderModel->delete($id)) {
            return redirect()->to(base_url('staff/office/documents'))->with('success', 'Folder deleted successfully');
        }

        return redirect()->back()->with('error', 'Failed to delete folder');
    }

    /**
     * Display a listing of files in a folder
     *
     * @param int $folderId
     * @return ResponseInterface
     */
    public function files($folderId)
    {
        $folder = $this->documentsFolderModel->find($folderId);

        if (!$folder) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'Folder not found');
        }

        $files = $this->documentFilesModel->getFilesByFolder($folderId);

        $data = [
            'title' => 'Files in ' . $folder['folder_name'],
            'folder' => $folder,
            'files' => $files
        ];

        return view('staff/staff_documents/staff_documents_files_index', $data);
    }

    /**
     * Show the form for uploading a new file
     *
     * @param int $folderId
     * @return ResponseInterface
     */
    public function createFile($folderId)
    {
        $folder = $this->documentsFolderModel->find($folderId);

        if (!$folder) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'Folder not found');
        }

        $data = [
            'title' => 'Upload File to ' . $folder['folder_name'],
            'folder' => $folder
        ];

        return view('staff/staff_documents/staff_documents_files_create', $data);
    }

    /**
     * Store a newly uploaded file in the database
     *
     * @param int $folderId
     * @return ResponseInterface
     */
    public function storeFile($folderId)
    {
        $folder = $this->documentsFolderModel->find($folderId);

        if (!$folder) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'Folder not found');
        }

        // Increase PHP limits for large file uploads
        ini_set('upload_max_filesize', '500M');
        ini_set('post_max_size', '500M');
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', '300');
        ini_set('max_input_time', '300');

        $file = $this->request->getFile('document_file');

        if (!$file->isValid() || $file->hasMoved()) {
            $error = $file->getError();
            if ($error === UPLOAD_ERR_INI_SIZE || $error === UPLOAD_ERR_FORM_SIZE) {
                return redirect()->back()->withInput()->with('error', 'The file is too large. Maximum size is 500MB.');
            }
            return redirect()->back()->withInput()->with('error', 'Invalid file upload: ' . $file->getErrorString());
        }

        // Check file size (500MB = 524288000 bytes)
        if ($file->getSize() > 524288000) {
            return redirect()->back()->withInput()->with('error', 'The file exceeds the maximum size of 500MB.');
        }

        try {
            // Generate a new random name for the file
            $newName = $file->getRandomName();

            // Move the file to the uploads directory
            $file->move(FCPATH . 'public/uploads/documents', $newName);

            // Prepare data for database
            $data = [
                'folder_id' => $folderId,
                'file_name' => $this->request->getPost('file_name') ?: $file->getClientName(),
                'file_path' => 'public/uploads/documents/' . $newName,
                'file_type' => $file->getClientMimeType(),
                'file_size' => $file->getSize(),
                'description' => $this->request->getPost('description'),
                'status' => 1,
                'created_by' => session()->get('emp_id') ?? session()->get('id') ?? 1
            ];

            if ($this->documentFilesModel->insert($data)) {
                return redirect()->to(base_url('staff/office/documents/files/' . $folderId))->with('success', 'File uploaded successfully');
            }

            // If insertion fails, delete the uploaded file
            unlink(FCPATH . 'public/uploads/documents/' . $newName);

            return redirect()->back()->withInput()->with('error', 'Failed to upload file to database');
        } catch (\Exception $e) {
            log_message('error', 'File upload error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error uploading file: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing a file
     *
     * @param int $id
     * @return ResponseInterface
     */
    public function editFile($id)
    {
        $file = $this->documentFilesModel->find($id);

        if (!$file) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'File not found');
        }

        $folder = $this->documentsFolderModel->find($file['folder_id']);

        $data = [
            'title' => 'Edit File',
            'file' => $file,
            'folder' => $folder
        ];

        return view('staff/staff_documents/staff_documents_files_edit', $data);
    }

    /**
     * Update the specified file in the database
     *
     * @param int $id
     * @return ResponseInterface
     */
    public function updateFile($id)
    {
        $file = $this->documentFilesModel->find($id);

        if (!$file) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'File not found');
        }

        // Increase PHP limits for large file uploads
        ini_set('upload_max_filesize', '500M');
        ini_set('post_max_size', '500M');
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', '300');
        ini_set('max_input_time', '300');

        $data = [
            'file_name' => $this->request->getPost('file_name'),
            'description' => $this->request->getPost('description'),
            'updated_by' => session()->get('emp_id') ?? session()->get('id') ?? 1
        ];

        // Handle file replacement if a new file is uploaded
        $uploadedFile = $this->request->getFile('document_file');
        if ($uploadedFile && $uploadedFile->isValid() && !$uploadedFile->hasMoved()) {
            // Check file size (500MB = 524288000 bytes)
            if ($uploadedFile->getSize() > 524288000) {
                return redirect()->back()->withInput()->with('error', 'The file exceeds the maximum size of 500MB.');
            }

            try {
                // Delete the old file
                if (file_exists(FCPATH . $file['file_path'])) {
                    unlink(FCPATH . $file['file_path']);
                }

                // Generate a new random name for the file
                $newName = $uploadedFile->getRandomName();

                // Move the file to the uploads directory
                $uploadedFile->move(FCPATH . 'public/uploads/documents', $newName);

                // Update file data
                $data['file_path'] = 'public/uploads/documents/' . $newName;
                $data['file_type'] = $uploadedFile->getClientMimeType();
                $data['file_size'] = $uploadedFile->getSize();
            } catch (\Exception $e) {
                log_message('error', 'File update error: ' . $e->getMessage());
                return redirect()->back()->withInput()->with('error', 'Error updating file: ' . $e->getMessage());
            }
        }

        if ($this->documentFilesModel->update($id, $data)) {
            return redirect()->to(base_url('staff/office/documents/files/' . $file['folder_id']))->with('success', 'File updated successfully');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update file');
    }

    /**
     * Remove the specified file from the database
     *
     * @param int $id
     * @return ResponseInterface
     */
    public function deleteFile($id)
    {
        $file = $this->documentFilesModel->find($id);

        if (!$file) {
            return redirect()->to(base_url('staff/office/documents'))->with('error', 'File not found');
        }

        $folderId = $file['folder_id'];

        // Set deleted_by field
        $this->documentFilesModel->update($id, ['deleted_by' => session()->get('emp_id') ?? session()->get('id') ?? 1]);

        if ($this->documentFilesModel->delete($id)) {
            // Delete the physical file
            if (file_exists(FCPATH . $file['file_path'])) {
                unlink(FCPATH . $file['file_path']);
            }

            return redirect()->to(base_url('staff/office/documents/files/' . $folderId))->with('success', 'File deleted successfully');
        }

        return redirect()->back()->with('error', 'Failed to delete file');
    }
}
