<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>



<?php if ($farm_blocks): ?>
    <!-- Your existing farm blocks card -->
    <div class="card">
        <div class="card-header bg-white">
            <h5 class="mb-0 float-start">Farm Blocks </h5>
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addFarmBlockModal">
                <i class="fas fa-plus"></i> Add Farm Block
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="farmBlocksTable">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Block Code</th>
                            <th>Crop</th>
                            <th>Location</th>
                            <th>Coordinates</th>
                            <th>Farmer</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($farm_blocks as $index => $block): ?>
                            <tr class="align-middle">
                                <td><?= $index + 1 ?></td>
                                <td><?= esc($block['block_code']) ?></td>
                                <td>
                                    <?php
                                    foreach ($crops as $crop):
                                        if ($crop['value'] == $block['crop_id']):
                                            echo esc($crop['item']);
                                        endif;
                                    endforeach;
                                    ?>
                                </td>
                                <td><?= esc($block['village']) ?> - <?= esc($block['block_site']) ?></td>
                                <td>
                                    Lat: <?= esc($block['lat']) ?><br>
                                    Lon: <?= esc($block['lon']) ?>
                                </td>
                                <td>
                                    <?php
                                    foreach ($farmers as $farmer):
                                        if ($farmer['id'] == $block['farmer_id']):
                                            echo $farmer['given_name'] . ' ' . $farmer['surname'];
                                        endif;
                                    endforeach;
                                    ?>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-primary btn-sm edit-block" 
                                            data-id="<?= $block['id'] ?>"
                                            data-block-code="<?= $block['block_code'] ?>"
                                            data-crop-id="<?= $block['crop_id'] ?>"
                                            data-village="<?= $block['village'] ?>"
                                            data-block-site="<?= $block['block_site'] ?>"
                                            data-farmer-id="<?= $block['farmer_id'] ?>"
                                            data-lat="<?= $block['lat'] ?>"
                                            data-lon="<?= $block['lon'] ?>"
                                            data-remarks="<?= $block['remarks'] ?>"
                                            data-district-id="<?= $block['district_id'] ?>"
                                            data-llg-id="<?= $block['llg_id'] ?>"
                                            data-ward-id="<?= $block['ward_id'] ?>">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                </td>
                            </tr>


                           

                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="card">
        <div class="card-body text-center text-muted">
            <p>Please select a farmer to view their farm blocks.</p>
        </div>
    </div>
<?php endif; ?>

<!-- Add Farm Block Modal -->
<div class="modal fade" id="addFarmBlockModal" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus-circle"></i> Add Farm Block</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open_multipart('staff/farms/add_farm_block', ['id' => 'addFarmBlockForm']) ?>
            <div class="modal-body">
                <div class="row">
                    <!-- Left Column -->
                    <div class="col-md-6">

                        <div class="mb-3">
                            <label class="form-label">Crop</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-2">
                                        <label for="crop_id" class="form-label">Select Crop</label>
                                        <select class="form-select" id="crop_id" name="crop_id" required>
                                            <option value="">Select Crop</option>
                                            <?php foreach ($crops as $crop): ?>
                                                <option value="<?= $crop['value'] ?>"><?= esc($crop['item']) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                </div>
                            </div>
                        </div>


                        <div class="mb-3">
                            <label class="form-label">Location</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-2">
                                        <label for="district_id" class="form-label">District</label>
                                        <select class="form-select" id="district_id" name="district_id" required>
                                            <option value="">Select District</option>
                                            <?php foreach ($districts as $district): ?>
                                                <option value="<?= $district['id'] ?>"><?= esc($district['name']) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label for="llg_id" class="form-label">LLG</label>
                                        <select class="form-select" id="llg_id" name="llg_id" required>
                                            <option value="">Select LLG</option>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label for="ward_id" class="form-label">Ward</label>
                                        <select class="form-select" id="ward_id" name="ward_id" required>
                                            <option value="">Select Ward</option>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label for="village" class="form-label">Village</label>
                                        <input type="text" class="form-control" id="village" name="village" required>
                                    </div>
                                    <div class="mb-2">
                                        <label for="block_site" class="form-label">Block Site</label>
                                        <input type="text" class="form-control" id="block_site" name="block_site" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Farmer</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-2">
                                        <label for="farmer_id" class="form-label">Search Farmer</label>
                                        <select name="farmer_id" id="farmer_id" class="form-select select2-farmers" style="width: 100%;" required>
                                            <option value="">Select a farmer</option>
                                            <?php if (isset($farmers)):
                                                foreach ($farmers as $farmer): ?>
                                                    <option value="<?= $farmer['id'] ?>">
                                                        <?= esc($farmer['farmer_code']) ?> - <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?>
                                                    </option>
                                            <?php endforeach;
                                            endif; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Coordinates</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-2">
                                        <label for="lat" class="form-label">Latitude</label>
                                        <input type="number" step="any" class="form-control" id="lat" name="lat" required>
                                    </div>
                                    <div class="mb-2">
                                        <label for="lon" class="form-label">Longitude</label>
                                        <input type="number" step="any" class="form-control" id="lon" name="lon" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="4"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="btnAddFarmBlock">
                    <i class="fas fa-save"></i> Save Farm Block
                </button>
            </div>
            <?= form_close() ?>

            <script>
                $(document).ready(function() {
                    // Add keypress event listener to form inputs
                    $('#addFarmBlockForm input').keypress(function(e) {
                        if (e.which == 13) {
                            e.preventDefault();
                            $('#btnAddFarmBlock').click();
                        }
                    });

                    $('#btnAddFarmBlock').on('click', function() {
                        // Create FormData object
                        var formData = new FormData($('#addFarmBlockForm')[0]);

                        // Send AJAX request
                        $.ajax({
                            url: "<?= base_url('staff/farms/add_farm_block'); ?>",
                            type: 'POST',
                            data: formData,
                            contentType: false,
                            processData: false,
                            beforeSend: function() {
                                $('#btnAddFarmBlock').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');
                            },
                            success: function(response) {
                                console.log(response);
                                if (response.status === 'success') {
                                    toastr.success(response.message);
                                    setTimeout(function() {
                                        location.reload();
                                    }, 1000);
                                } else {
                                    toastr.error(response.message);
                                    setTimeout(function() {
                                        location.reload();
                                    }, 2000);
                                }
                            },
                            error: function(error) {
                                console.log(error.responseText);
                                toastr.error('An error occurred while saving the farm block');
                            }
                        });
                    });
                });
            </script>
        </div>
    </div>
</div>

<!-- Edit Farm Block Modal -->
<div class="modal fade" id="editFarmBlockModal" tabindex="-1" role="dialog" aria-labelledby="editModelTitleId" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Farm Block</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open_multipart('staff/farms/update_farm_block', ['id' => 'editFarmBlockForm']) ?>
            <input type="hidden" name="block_id" id="edit_block_id">
            <div class="modal-body">
                <!-- Copy the same form structure from Add Modal, but change IDs to edit_* -->
                <div class="row">
                    <!-- Left Column -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Crop</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-2">
                                        <label for="edit_crop_id" class="form-label">Select Crop</label>
                                        <select class="form-select" id="edit_crop_id" name="crop_id" required>
                                            <option value="">Select Crop</option>
                                            <?php foreach ($crops as $crop): ?>
                                                <option value="<?= $crop['value'] ?>"><?= esc($crop['item']) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Location</label>
                            <div class="card">
                                <div class="card-body">
                                    <!-- Location fields with edit_* IDs -->
                                    <div class="mb-2">
                                        <label for="edit_district_id" class="form-label">District</label>
                                        <select class="form-select" id="edit_district_id" name="district_id" required>
                                            <option value="">Select District</option>
                                            <?php foreach ($districts as $district): ?>
                                                <option value="<?= $district['id'] ?>"><?= esc($district['name']) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label for="edit_llg_id" class="form-label">LLG</label>
                                        <select class="form-select" id="edit_llg_id" name="llg_id" required>
                                            <option value="">Select LLG</option>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label for="edit_ward_id" class="form-label">Ward</label>
                                        <select class="form-select" id="edit_ward_id" name="ward_id" required>
                                            <option value="">Select Ward</option>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label for="edit_village" class="form-label">Village</label>
                                        <input type="text" class="form-control" id="edit_village" name="village" required>
                                    </div>
                                    <div class="mb-2">
                                        <label for="edit_block_site" class="form-label">Block Site</label>
                                        <input type="text" class="form-control" id="edit_block_site" name="block_site" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Farmer</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-2">
                                        <label for="edit_farmer_id" class="form-label">Search Farmer</label>
                                        <select name="farmer_id" id="edit_farmer_id" class="form-select select2-farmers" required>
                                            <option value="">Select a farmer</option>
                                            <?php foreach ($farmers as $farmer): ?>
                                                <option value="<?= $farmer['id'] ?>">
                                                    <?= esc($farmer['farmer_code']) ?> - <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Coordinates</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-2">
                                        <label for="edit_lat" class="form-label">Latitude</label>
                                        <input type="number" step="any" class="form-control" id="edit_lat" name="lat" required>
                                    </div>
                                    <div class="mb-2">
                                        <label for="edit_lon" class="form-label">Longitude</label>
                                        <input type="number" step="any" class="form-control" id="edit_lon" name="lon" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="edit_remarks" name="remarks" rows="4"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="btnUpdateFarmBlock">
                    <i class="fas fa-save"></i> Update Farm Block
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>




<?= $this->endSection() ?>


<?= $this->section('scripts') ?>

<script>
    $(document).ready(function() {

        // Initialize Select2 for the farmer selection inside the modal
        $('.select2-farmers').select2({
            theme: 'bootstrap-5',
            placeholder: 'Select a farmer',
            allowClear: true,
            dropdownParent: $('#addFarmBlockModal') // Attach dropdown to modal
        });

        // Initialize Select2 for the edit modal
        $('#edit_farmer_id').select2({
            theme: 'bootstrap-5',
            placeholder: 'Select a farmer',
            allowClear: true,
            dropdownParent: $('#editFarmBlockModal')
        });

        // Handle edit button click
        $('.edit-block').on('click', function() {
            const data = $(this).data();
            
            // Populate the edit form
            $('#edit_block_id').val(data.id);
            $('#edit_crop_id').val(data.cropId);
            $('#edit_village').val(data.village);
            $('#edit_block_site').val(data.blockSite);
            $('#edit_farmer_id').val(data.farmerId).trigger('change');
            $('#edit_lat').val(data.lat);
            $('#edit_lon').val(data.lon);
            $('#edit_remarks').val(data.remarks);
            
            // Handle location dropdowns
            $('#edit_district_id').val(data.districtId).trigger('change');
            
            // Load LLGs and set value
            $.ajax({
                url: '<?= base_url('staff/farms/get_llgs') ?>',
                type: 'POST',
                data: { district_id: data.districtId },
                success: function(response) {
                    if (response.success) {
                        let options = '<option value="">Select LLG</option>';
                        response.llgs.forEach(function(llg) {
                            options += `<option value="${llg.id}">${llg.name}</option>`;
                        });
                        $('#edit_llg_id').html(options);
                        
                        // Set LLG value after options are loaded
                        $('#edit_llg_id').val(data.llgId).trigger('change');
                        
                        // Load Wards after LLG is set
                        $.ajax({
                            url: '<?= base_url('staff/farms/get_wards') ?>',
                            type: 'POST',
                            data: { llg_id: data.llgId },
                            success: function(response) {
                                if (response.success) {
                                    let options = '<option value="">Select Ward</option>';
                                    response.wards.forEach(function(ward) {
                                        options += `<option value="${ward.id}">${ward.name}</option>`;
                                    });
                                    $('#edit_ward_id').html(options);
                                    
                                    // Set Ward value after options are loaded
                                    $('#edit_ward_id').val(data.wardId);
                                }
                            }
                        });
                    }
                }
            });

            // Show the modal
            $('#editFarmBlockModal').modal('show');
        });

        // Handle update button click
        $('#btnUpdateFarmBlock').on('click', function() {
            var formData = new FormData($('#editFarmBlockForm')[0]);

            $.ajax({
                url: "<?= base_url('staff/farms/update_farm_block'); ?>",
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                beforeSend: function() {
                    $('#btnUpdateFarmBlock').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Updating...');
                },
                success: function(response) {
                    if (response.status === 'success') {
                        toastr.success(response.message);
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(error) {
                    console.log(error.responseText);
                    toastr.error('An error occurred while updating the farm block');
                },
                complete: function() {
                    $('#btnUpdateFarmBlock').prop('disabled', false).html('<i class="fas fa-save"></i> Update Farm Block');
                }
            });
        });

        // Handle location dropdowns in edit modal
        $('#edit_district_id').on('change', function() {
            // Similar to add modal district change handler
            const districtId = $(this).val();
            if (districtId) {
                $.ajax({
                    url: '<?= base_url('staff/farms/get_llgs') ?>',
                    type: 'POST',
                    data: { district_id: districtId },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            let options = '<option value="">Select LLG</option>';
                            response.llgs.forEach(function(llg) {
                                options += `<option value="${llg.id}">${llg.name}</option>`;
                            });
                            $('#edit_llg_id').html(options);
                            $('#edit_ward_id').html('<option value="">Select Ward</option>');
                        }
                    }
                });
            }
        });

        $('#edit_llg_id').on('change', function() {
            // Similar to add modal LLG change handler
            const llgId = $(this).val();
            if (llgId) {
                $.ajax({
                    url: '<?= base_url('staff/farms/get_wards') ?>',
                    type: 'POST',
                    data: { llg_id: llgId },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            let options = '<option value="">Select Ward</option>';
                            response.wards.forEach(function(ward) {
                                options += `<option value="${ward.id}">${ward.name}</option>`;
                            });
                            $('#edit_ward_id').html(options);
                        }
                    }
                });
            }
        });
    });
</script>


<script>
    $(document).ready(function() {
        // Initialize Select2 for farmer selection


        // Your existing DataTable initialization
        try {
            const table = $('#farmBlocksTable').DataTable({
                "responsive": true,
                "processing": true,
                "pageLength": 10,

                "language": {
                    "lengthMenu": "Show _MENU_ entries",
                    "zeroRecords": "No farm blocks found",
                    "info": "Showing _START_ to _END_ of _TOTAL_ farm blocks",
                    "infoEmpty": "Showing 0 to 0 of 0 farm blocks",
                    "infoFiltered": "(filtered from _MAX_ total farm blocks)"
                },
                "columnDefs": [{
                    "orderable": false,
                    "targets": -1
                }]
            });
        } catch (error) {
            console.error('Error initializing DataTable:', error);
        }

        // Load LLGs when district is selected
        $('#district_id').on('change', function() {
            const districtId = $(this).val();
            console.log('Selected district ID:', districtId); // Debug log

            if (districtId) {
                $.ajax({
                    url: '<?= base_url('staff/farms/get_llgs') ?>',
                    type: 'POST',
                    data: {
                        district_id: districtId
                    },
                    dataType: 'json',
                    beforeSend: function() {
                        $('#llg_id').html('<option value="">Loading...</option>');
                        $('#ward_id').html('<option value="">Select Ward</option>');
                    },
                    success: function(response) {
                        console.log('LLGs response:', response); // Debug log
                        if (response.success) {
                            let options = '<option value="">Select LLG</option>';
                            response.llgs.forEach(function(llg) {
                                options += `<option value="${llg.id}">${llg.name}</option>`;
                            });
                            $('#llg_id').html(options);
                        } else {
                            toastr.error(response.message || 'Failed to load LLGs');
                            console.error('LLGs error:', response.message); // Debug log
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Ajax error:', error); // Debug log
                        toastr.error('Failed to load LLGs');
                        $('#llg_id').html('<option value="">Select LLG</option>');
                    }
                });
            } else {
                $('#llg_id').html('<option value="">Select LLG</option>');
                $('#ward_id').html('<option value="">Select Ward</option>');
            }
        });

        // Load Wards when LLG is selected
        $('#llg_id').on('change', function() {
            const llgId = $(this).val();
            console.log('Selected LLG ID:', llgId); // Debug log

            if (llgId) {
                $.ajax({
                    url: '<?= base_url('staff/farms/get_wards') ?>',
                    type: 'POST',
                    data: {
                        llg_id: llgId
                    },
                    dataType: 'json',
                    beforeSend: function() {
                        $('#ward_id').html('<option value="">Loading...</option>');
                    },
                    success: function(response) {
                        console.log('Wards response:', response); // Debug log
                        if (response.success) {
                            let options = '<option value="">Select Ward</option>';
                            response.wards.forEach(function(ward) {
                                options += `<option value="${ward.id}">${ward.name}</option>`;
                            });
                            $('#ward_id').html(options);
                        } else {
                            toastr.error(response.message || 'Failed to load Wards');
                            console.error('Wards error:', response.message); // Debug log
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Ajax error:', error); // Debug log
                        toastr.error('Failed to load Wards');
                        $('#ward_id').html('<option value="">Select Ward</option>');
                    }
                });
            } else {
                $('#ward_id').html('<option value="">Select Ward</option>');
            }
        });


    });
</script>
<?= $this->endSection() ?>