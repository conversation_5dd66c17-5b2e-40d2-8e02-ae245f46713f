<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\{
    districtModel,
    FarmerInformationModel,
    CropsFarmBlockModel,
    CropsFarmCropsDataModel,
    llgModel,
    provinceModel,
    CropsModel,
    dakoiiUsersModel,
    wardModel,
    LivestockFarmBlockModel,
    CropsFarmFertilizerDataModel,
    CropsFarmPesticidesDataModel,
    CropsFarmHarvestDataModel,
    CropsFarmMarketingDataModel,
    CropBuyersModel,
    PesticidesModel,
    FertilizersModel,
    CropsFarmDiseaseDataModel,
    InfectionsModel
};

class Staff_FarmsController extends BaseController
{
    protected $models = [];
    protected $farmersModel;
    protected $farmBlockModel;
    protected $livestockFarmBlockModel;
    protected $farmCropsDataModel;
    protected $provincesModel;
    protected $districtsModel;
    protected $llgsModel;
    protected $wardsModel;
    protected $cropsModel;
    protected $farmFertilizerDataModel;
    protected $farmPesticidesDataModel;
    protected $farmHarvestDataModel;
    protected $farmMarketingDataModel;
    protected $cropBuyersModel;
    protected $pesticidesModel;
    protected $fertilizersModel;
    protected $farmDiseaseDataModel;
    protected $infectionsModel;

    public function __construct()
    {
        helper(['url', 'form', 'info', 'weather']);
        
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        // Initialize models
        $this->models = [
            'users' => new dakoiiUsersModel(),
            'farmers' => new FarmerInformationModel(),
            'farmBlock' => new CropsFarmBlockModel(),
            'livestockFarmBlock' => new LivestockFarmBlockModel(),
            'farmCropsData' => new CropsFarmCropsDataModel(),
            'provinces' => new provinceModel(),
            'districts' => new districtModel(),
            'llgs' => new llgModel(),
            'wards' => new wardModel(),
            'crops' => new CropsModel(),
            'farmFertilizer' => new CropsFarmFertilizerDataModel(),
            'farmPesticides' => new CropsFarmPesticidesDataModel(),
            'farmHarvest' => new CropsFarmHarvestDataModel(),
            'farmMarketing' => new CropsFarmMarketingDataModel(),
            'cropBuyers' => new CropBuyersModel(),
            'pesticides' => new PesticidesModel(),
            'fertilizers' => new FertilizersModel(),
            'farmDisease' => new CropsFarmDiseaseDataModel(),
            'infections' => new InfectionsModel()
        ];

        // Set model properties for backward compatibility
        $this->farmersModel = $this->models['farmers'];
        $this->farmBlockModel = $this->models['farmBlock'];
        $this->livestockFarmBlockModel = $this->models['livestockFarmBlock'];
        $this->farmCropsDataModel = $this->models['farmCropsData'];
        $this->provincesModel = $this->models['provinces'];
        $this->districtsModel = $this->models['districts'];
        $this->llgsModel = $this->models['llgs'];
        $this->wardsModel = $this->models['wards'];
        $this->cropsModel = $this->models['crops'];
        $this->farmFertilizerDataModel = $this->models['farmFertilizer'];
        $this->farmPesticidesDataModel = $this->models['farmPesticides'];
        $this->farmHarvestDataModel = $this->models['farmHarvest'];
        $this->farmMarketingDataModel = $this->models['farmMarketing'];
        $this->cropBuyersModel = $this->models['cropBuyers'];
        $this->pesticidesModel = $this->models['pesticides'];
        $this->fertilizersModel = $this->models['fertilizers'];
        $this->farmDiseaseDataModel = $this->models['farmDisease'];
        $this->infectionsModel = $this->models['infections'];
    }

    protected function verifyDistrictAccess($districtId) 
    {
        return $districtId == session()->get('district_id');
    }

    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    protected function validateNumericInput($value, $fieldName) 
    {
        if (!is_numeric($value)) {
            throw new \Exception("The {$fieldName} must be a numeric value.");
        }
    }

    protected function validateCoordinates($lon, $lat) 
    {
        if (!is_numeric($lon) || !is_numeric($lat)) {
            throw new \Exception('Coordinates must be numeric values.');
        }
        if ($lon < -180 || $lon > 180) {
            throw new \Exception('Longitude must be between -180 and 180.');
        }
        if ($lat < -90 || $lat > 90) {
            throw new \Exception('Latitude must be between -90 and 90.');
        }
    }

    public function view()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';
        
        $llgs = $this->llgsModel->where('district_id', session()->get('district_id'))->findAll();

        $data = [
            'title' => 'Farm Blocks',
            'page_header' => 'Farm Blocks',
            'farmers' => $this->farmersModel->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $this->farmBlockModel->select("
                crops_farm_blocks.*,
                CONCAT(farmer_information.given_name, ' ', farmer_information.surname) AS farmer_name,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ")
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'llgs' => $llgs,
            'crops' => $this->cropsModel->findAll(),
        ];

        return view('staff/farms/farm_blocks', $data);
    }

    public function farm_blocks($farmer_id = null)
    {
        $data = [
            'title' => 'Farm Blocks',
            'page_header' => 'Farm Blocks',
            'farmer' => $farmer_id ? $this->farmersModel->find($farmer_id) : null,
            'farmers' => $this->farmersModel->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $farmer_id ? $this->farmBlockModel->where('farmer_id', $farmer_id)
                ->where('district_id', session()->get('district_id'))
                ->orderBy('id', 'asc')
                ->findAll() : null,
            'districts' => $this->districtsModel->where('province_id', session()->get('orgprovince_id'))
                ->findAll(),
        ];

        return view('staff/farms/farm_blocks', $data);
    }

    public function add_farm_block()
    {
        try {
            $farmer_id = $this->request->getPost('farmer_id');
            $lon = $this->request->getPost('lon');
            $lat = $this->request->getPost('lat');

            // Validate coordinates if provided
            if ($lon !== '' && $lat !== '') {
                $this->validateCoordinates($lon, $lat);
            }

            $latest_block_code = $this->farmBlockModel->where('farmer_id', $farmer_id)
                ->orderBy('id', 'DESC')
                ->first();
            $farmer_code = $this->farmersModel->find($farmer_id)['farmer_code'];

            if ($latest_block_code) {
                $current_number = (int)substr($latest_block_code['block_code'], strpos($latest_block_code['block_code'], '-') + 1);
                $next_block_code = $farmer_code . '-' . sprintf('%03d', $current_number + 1);
            } else {
                $next_block_code = $farmer_code . '-001';
            }

            $data = [
                'farmer_id' => $farmer_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'block_code' => $next_block_code,
                'org_id' => session()->get('org_id'),
                'country_id' => session()->get('orgcountry_id'),
                'province_id' => session()->get('orgprovince_id'),
                'district_id' => session()->get('district_id'),
                'llg_id' => $this->request->getPost('llg_id'),
                'ward_id' => $this->request->getPost('ward_id'),
                'village' => $this->request->getPost('village'),
                'block_site' => $this->request->getPost('block_site'),
                'lon' => $lon,
                'lat' => $lat,
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['farmer_id', 'crop_id', 'llg_id', 'ward_id'];
            $this->validateInput($data, $required);

            $this->farmBlockModel->save($data);
            return $this->response->setJSON([
                'status' => 'success', 
                'message' => 'Farm block added successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Farm Block] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error', 
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_farm_block()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            $existing_block = $this->farmBlockModel->where('id', $block_id)->where('district_id', session()->get('district_id'))->first();

            if (!$existing_block) {
                throw new \Exception('Farm block not found or access denied');
            }

            $farmer_id = $this->request->getPost('farmer_id');
            $farmer_code = $this->farmersModel->find($farmer_id)['farmer_code'];

            // Regenerate block code if farmer changed
            if (substr($existing_block['block_code'], 0, strpos($existing_block['block_code'], '-')) !== $farmer_code) {
                $latest_block_code = $this->farmBlockModel->where('farmer_id', $farmer_id)->orderBy('id', 'DESC')->first();
                if ($latest_block_code) {
                    $current_number = (int)substr($latest_block_code['block_code'], strpos($latest_block_code['block_code'], '-') + 1);
                    $next_block_code = $farmer_code . '-' . sprintf('%03d', $current_number + 1);
                } else {
                    $next_block_code = $farmer_code . '-001';
                }
            } else {
                $next_block_code = $existing_block['block_code'];
            }

            $data = [
                'block_code' => $next_block_code,
                'farmer_id' => $farmer_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'llg_id' => $this->request->getPost('llg_id'),
                'ward_id' => $this->request->getPost('ward_id'),
                'village' => $this->request->getPost('village'),
                'block_site' => $this->request->getPost('block_site'),
                'lon' => $this->request->getPost('lon'),
                'lat' => $this->request->getPost('lat'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmBlockModel->update($block_id, $data);
            return $this->response->setJSON(['status' => 'success', 'message' => 'Farm block updated successfully!']);
        } catch (\Exception $e) {
            log_message('error', '[Update Farm Block] ' . $e->getMessage());
            return $this->response->setJSON(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    public function delete_farm_block($id)
    {
        try {
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $id)
                ->where('district_id', session()->get('district_id'))
                ->first();
            
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmBlockModel->update($id, $data);

            return redirect()->back()->with('success', 'Farm block deleted successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Delete Farm Block] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while deleting the farm block');
        }
    }

    public function get_llgs()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $districtId = $this->request->getPost('district_id');
        if (!$districtId) {
            return $this->response->setJSON(['success' => false, 'message' => 'District ID is required']);
        }

        try {
            $llgs = $this->llgsModel->where('district_id', $districtId)->findAll();
            return $this->response->setJSON(['success' => true, 'llgs' => $llgs]);
        } catch (\Exception $e) {
            log_message('error', 'Error in get_llgs: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Database error occurred']);
        }
    }

    public function get_wards()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $llg_id = $this->request->getPost('llg_id');
        if (!$llg_id) {
            return $this->response->setJSON(['success' => false, 'message' => 'LLG ID is required']);
        }

        try {
            $wards = $this->wardsModel->where('llg_id', $llg_id)->findAll();
            return $this->response->setJSON(['success' => true, 'wards' => $wards]);
        } catch (\Exception $e) {
            log_message('error', 'Error in get_wards: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Database error occurred']);
        }
    }

    public function maps()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Field Maps',
            'page_header' => 'Field Maps',
            'farm_blocks' => $this->farmBlockModel->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'crops' => $this->cropsModel->findAll()
        ];

        return view('staff/farms/maps', $data);
    }

   
} 