<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Add Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<!-- Add Toastr CSS -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">

<div class="row">
    <!-- Farm Blocks Table -->
    <div class="col-md-12">
    <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Farm Blocks</h5>
            </div>
            <div class="card-body">
                <!-- Add <PERSON> Block button -->
                <div class="mb-3">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFarmBlockModal">
                        <i class="bi bi-plus-circle me-1"></i> Add Farm Block
            </button>
        </div>
                <!-- Farm blocks table -->
            <div class="table-responsive">
                    <table class="table table-hover" id="farmBlocksTable">
                    <thead>
                            <tr>
                            <th>Block Code</th>
                                <th>Farmer</th>
                            <th>Crop</th>
                            <th>Location</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                            <?php foreach ($farm_blocks as $block): ?>
                                <tr>
                                <td><?= esc($block['block_code']) ?></td>
                                    <td><?= esc($block['farmer_name']) ?></td>
                                    <td><?= esc($block['crop_name']) ?></td>
                                    <td><?= esc($block['village']) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-warning edit-block" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editFarmBlockModal"
                                                data-block-id="<?= $block['id'] ?>"
                                                data-farmer-id="<?= $block['farmer_id'] ?>"
                                            data-crop-id="<?= $block['crop_id'] ?>"
                                            data-llg-id="<?= $block['llg_id'] ?>"
                                                data-ward-id="<?= $block['ward_id'] ?>"
                                                data-village="<?= esc($block['village']) ?>"
                                                data-block-site="<?= esc($block['block_site']) ?>"
                                                data-lon="<?= esc($block['lon']) ?>"
                                                data-lat="<?= esc($block['lat']) ?>"
                                                data-remarks="<?= esc($block['remarks']) ?>">
                                            <i class="fas fa-edit"></i>
                                    </button>
                                       <!--  <a href="<?= base_url('staff/farms/view-crops-data/' . $block['id']) ?>" 
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View Crops Data
                                        </a> -->
                                        <a href="javascript:void(0)" class="btn btn-sm btn-info upload-files" data-id="<?= $block['id'] ?>" data-toggle="tooltip" title="Upload Files">
                                            <i class="fas fa-upload"></i>
                                        </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>

<!-- Add Farm Block Modal -->
<div class="modal fade" id="addFarmBlockModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Farm Block</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addFarmBlockForm">
            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                <label for="farmer_id" class="form-label">Farmer *</label>
                                <select class="form-select" id="farmer_id" name="farmer_id" required>
                                    <option value="">Select Farmer</option>
                                    <?php foreach ($farmers as $farmer): ?>
                                        <option value="<?= $farmer['id'] ?>"> <?= $farmer['farmer_code'] ?> | <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></option>
                                    <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                <label for="crop_id" class="form-label">Crop *</label>
                                            <select class="form-select" id="crop_id" name="crop_id" required>
                                                <option value="">Select Crop</option>
                                                <?php foreach ($crops as $crop): ?>
                                                    <option value="<?= $crop['id'] ?>"><?= esc($crop['crop_name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                            </div>
                        </div>
                    </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="district_id" class="form-label">District</label>
                                <input type="text" class="form-control" value="<?= esc($district_name) ?>" readonly>
                                <input type="hidden" name="district_id" value="<?= session()->get('district_id') ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                <label for="llg_id" class="form-label">LLG *</label>
                                            <select class="form-select" id="llg_id" name="llg_id" required>
                                                <option value="">Select LLG</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= $llg['id'] ?>"><?= esc($llg['name']) ?></option>
                                    <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                <label for="ward_id" class="form-label">Ward *</label>
                                <select class="form-select" id="ward_id" name="ward_id" required disabled>
                                                <option value="">Select Ward</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="village" class="form-label">Village</label>
                                <input type="text" class="form-control" id="village" name="village">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="block_site" class="form-label">Block Site</label>
                                <input type="text" class="form-control" id="block_site" name="block_site">
                            </div>
                        </div>
                    </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="lon" class="form-label">Longitude</label>
                                <input type="text" class="form-control" id="lon" name="lon">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                <label for="lat" class="form-label">Latitude</label>
                                <input type="text" class="form-control" id="lat" name="lat">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save Block</button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Farm Block Modal -->
<div class="modal fade" id="editFarmBlockModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Farm Block</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editFarmBlockForm">
                <input type="hidden" id="edit_block_id" name="block_id">
            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                <label for="edit_farmer_id" class="form-label">Farmer *</label>
                                <select class="form-select" id="edit_farmer_id" name="farmer_id" required>
                                    <option value="">Select Farmer</option>
                                                <?php foreach ($farmers as $farmer): ?>
                                        <option value="<?= $farmer['id'] ?>"> <?= $farmer['farmer_code'] ?> | <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                <label for="edit_crop_id" class="form-label">Crop *</label>
                                            <select class="form-select" id="edit_crop_id" name="crop_id" required>
                                                <option value="">Select Crop</option>
                                                <?php foreach ($crops as $crop): ?>
                                                    <option value="<?= $crop['id'] ?>"><?= esc($crop['crop_name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                            </div>
                        </div>
                    </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="edit_district_id" class="form-label">District</label>
                                <input type="text" class="form-control" value="<?= esc($district_name) ?>" readonly>
                                <input type="hidden" name="district_id" value="<?= session()->get('district_id') ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                <label for="edit_llg_id" class="form-label">LLG *</label>
                                            <select class="form-select" id="edit_llg_id" name="llg_id" required>
                                                <option value="">Select LLG</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= $llg['id'] ?>"><?= esc($llg['name']) ?></option>
                                    <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                <label for="edit_ward_id" class="form-label">Ward *</label>
                                <select class="form-select" id="edit_ward_id" name="ward_id" required disabled>
                                                <option value="">Select Ward</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_village" class="form-label">Village</label>
                                <input type="text" class="form-control" id="edit_village" name="village">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_block_site" class="form-label">Block Site</label>
                                <input type="text" class="form-control" id="edit_block_site" name="block_site">
                            </div>
                        </div>
                    </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_lon" class="form-label">Longitude</label>
                                <input type="text" class="form-control" id="edit_lon" name="lon">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                <label for="edit_lat" class="form-label">Latitude</label>
                                <input type="text" class="form-control" id="edit_lat" name="lat">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="edit_remarks" name="remarks" rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update Block</button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- File Upload Modal -->
<div class="modal fade" id="fileUploadModal" tabindex="-1" role="dialog" aria-labelledby="fileUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fileUploadModalLabel">Farm Block Files</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Upload Form -->
                <form id="fileUploadForm" enctype="multipart/form-data">
                    <input type="hidden" id="farm_block_id" name="farm_block_id">
                    <div class="form-group">
                        <label for="file_caption">File Caption</label>
                        <input type="text" class="form-control" id="file_caption" name="file_caption" required>
                    </div>
                    <div class="form-group">
                        <label for="file">Select File</label>
                        <input type="file" class="form-control-file" id="file" name="file" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </form>
                
                <hr>
                
                <!-- Files List -->
                <h5>Uploaded Files</h5>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="filesTable">
                        <thead>
                            <tr>
                                <th>Caption</th>
                                <th>File</th>
                                <th>Uploaded At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Files will be loaded here via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Caption Modal -->
<div class="modal fade" id="editCaptionModal" tabindex="-1" role="dialog" aria-labelledby="editCaptionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCaptionModalLabel">Edit File Caption</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editCaptionForm">
                    <input type="hidden" id="edit_file_id" name="file_id">
                    <div class="form-group">
                        <label for="edit_file_caption">File Caption</label>
                        <input type="text" class="form-control" id="edit_file_caption" name="file_caption" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCaption">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Add Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- Add Toastr JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script>
    $(document).ready(function() {
    // Configure Toastr
    toastr.options = {
        "closeButton": true,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "timeOut": "3000"
    };

    // Initialize DataTable
    $('#farmBlocksTable').DataTable({
        "responsive": true,
        "pageLength": 10
    });

    // Initialize Select2 for farmer search
    $('#farmer_search').select2({
        theme: 'bootstrap-5',
        placeholder: 'Search for a farmer...'
    });

    // Initialize Select2 for farmer dropdown
    $('#farmer_id').select2({
            theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Select Farmer',
            allowClear: true,
            dropdownParent: $('#addFarmBlockModal')
        });

    // Initialize Select2 for crop dropdown
    $('#crop_id').select2({
            theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Select Crop',
            allowClear: true,
        dropdownParent: $('#addFarmBlockModal')
    });

    // Clear Select2 on modal close
    $('#addFarmBlockModal').on('hidden.bs.modal', function () {
        $('#farmer_id').val('').trigger('change');
        $('#crop_id').val('').trigger('change');
    });

    // LLG change event for Add form
    $('#llg_id').on('change', function() {
        const llgId = $(this).val();
        const wardSelect = $('#ward_id');
        
        if (llgId) {
            wardSelect.prop('disabled', false);
            
            $.ajax({
                url: '<?= base_url('staff/farmers/getWardsByLlg') ?>/' + llgId,
                method: 'GET',
                success: function(response) {
                    wardSelect.empty().append('<option value="">Select Ward</option>');
                    response.forEach(function(ward) {
                        wardSelect.append(`<option value="${ward.id}">${ward.name}</option>`);
                    });
                },
                error: function() {
                    wardSelect.empty().append('<option value="">Error loading wards</option>');
                    wardSelect.prop('disabled', true);
                }
            });
        } else {
            wardSelect.prop('disabled', true).empty().append('<option value="">Select Ward</option>');
        }
    });

    // Add similar LLG change event for Edit form
    $('#edit_llg_id').on('change', function() {
        // Similar code as above but with edit_ prefixed IDs
    });

    // Form submission handlers
    $('#addFarmBlockForm').on('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = $(this).serialize();
        
        // Disable submit button to prevent double submission
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        
        // Send AJAX request
                $.ajax({
            url: '<?= base_url('staff/farms/add_farm_block') ?>',
                    type: 'POST',
            data: formData,
            dataType: 'json',
                    success: function(response) {
                if (response.status === 'success') {
                    // Show success message
                    toastr.success(response.message || 'Farm block added successfully');
                    // Close modal
                    $('#addFarmBlockModal').modal('hide');
                    // Reload the page after a short delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                        } else {
                    // Show error message
                    toastr.error(response.message || 'An error occurred while saving the farm block');
                }
            },
            error: function(xhr, status, error) {
                // Show error message
                toastr.error('An error occurred while saving the farm block');
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false);
            }
        });
    });

    $('#editFarmBlockForm').on('submit', function(e) {
        e.preventDefault();
        // Add your form submission code here
    });

    // Initialize Select2 for edit form dropdowns
    $('#edit_farmer_id').select2({
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Select Farmer',
        allowClear: true,
        dropdownParent: $('#editFarmBlockModal')
    });

    $('#edit_crop_id').select2({
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Select Crop',
        allowClear: true,
        dropdownParent: $('#editFarmBlockModal')
    });

    // Handle edit button click
    $('.edit-block').on('click', function() {
        const button = $(this);
        
        // Get data from button attributes
        const blockId = button.data('block-id');
        const farmerId = button.data('farmer-id');
        const cropId = button.data('crop-id');
        const llgId = button.data('llg-id');
        const wardId = button.data('ward-id');
        const village = button.data('village');
        const blockSite = button.data('block-site');
        const lon = button.data('lon');
        const lat = button.data('lat');
        const remarks = button.data('remarks');
        
        // Populate form fields
        $('#edit_block_id').val(blockId);
        $('#edit_farmer_id').val(farmerId).trigger('change');
        $('#edit_crop_id').val(cropId).trigger('change');
        $('#edit_llg_id').val(llgId).trigger('change');
        $('#edit_village').val(village || '');
        $('#edit_block_site').val(blockSite || '');
        $('#edit_lon').val(lon || '');
        $('#edit_lat').val(lat || '');
        $('#edit_remarks').val(remarks || '');
        
        // Handle ward selection after LLG is loaded
        if (wardId) {
            setTimeout(function() {
                $('#edit_ward_id').prop('disabled', false).val(wardId).trigger('change');
            }, 1000);
        }
    });

    // LLG change event for Edit form
    $('#edit_llg_id').on('change', function() {
            const llgId = $(this).val();
        const wardSelect = $('#edit_ward_id');

            if (llgId) {
            wardSelect.prop('disabled', false);
            
                $.ajax({
                url: '<?= base_url('staff/farmers/getWardsByLlg') ?>/' + llgId,
                method: 'GET',
                    success: function(response) {
                    wardSelect.empty().append('<option value="">Select Ward</option>');
                    response.forEach(function(ward) {
                        wardSelect.append(`<option value="${ward.id}">${ward.name}</option>`);
                    });
                },
                error: function() {
                    wardSelect.empty().append('<option value="">Error loading wards</option>');
                    wardSelect.prop('disabled', true);
                    }
                });
            } else {
            wardSelect.prop('disabled', true).empty().append('<option value="">Select Ward</option>');
        }
    });

    // Edit form submission
    $('#editFarmBlockForm').on('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = $(this).serialize();
        
        // Disable submit button to prevent double submission
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);

            // Send AJAX request
            $.ajax({
            url: '<?= base_url('staff/farms/update_farm_block') ?>',
                type: 'POST',
                data: formData,
            dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                    // Show success message
                    toastr.success(response.message || 'Farm block updated successfully');
                    // Close modal
                    $('#editFarmBlockModal').modal('hide');
                    // Reload the page after a short delay
                        setTimeout(function() {
                        window.location.reload();
                        }, 1000);
                    } else {
                    // Show error message
                    toastr.error(response.message || 'An error occurred while updating the farm block');
                    }
                },
                error: function(xhr, status, error) {
                // Show error message
                    toastr.error('An error occurred while updating the farm block');
                },
                complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false);
                }
        });
    });

    // Open file upload modal
    $('.upload-files').on('click', function() {
        const blockId = $(this).data('id');
        $('#farm_block_id').val(blockId);
        loadFiles(blockId);
        $('#fileUploadModal').modal('show');
    });
    
    // Handle file upload
    $('#fileUploadForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        $.ajax({
            url: '<?= site_url('staff/farms/upload_farm_block_file') ?>',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert(response.message);
                    
                    // Reset form
                    $('#fileUploadForm')[0].reset();
                    
                    // Reload files list
                    loadFiles($('#farm_block_id').val());
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    });
    
    // Load files for a farm block
    function loadFiles(blockId) {
        $.ajax({
            url: '<?= site_url('staff/farms/get_farm_block_files') ?>/' + blockId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Clear table
                    $('#filesTable tbody').empty();
                    
                    // Add files to table
                    if (response.files.length > 0) {
                        $.each(response.files, function(index, file) {
                            $('#filesTable tbody').append(`
                                <tr>
                                    <td>${file.file_caption}</td>
                                    <td>
                                        <a href="<?= base_url() ?>/${file.file_path}" target="_blank">
                                            View File
                                        </a>
                                    </td>
                                    <td>${file.uploaded_at}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary edit-caption" data-id="${file.id}" data-caption="${file.file_caption}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger delete-file" data-id="${file.id}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            `);
                        });
                    } else {
                        $('#filesTable tbody').append('<tr><td colspan="4" class="text-center">No files uploaded yet</td></tr>');
                    }
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    }
    
    // Delete file
    $(document).on('click', '.delete-file', function() {
        if (confirm('Are you sure you want to delete this file?')) {
            const fileId = $(this).data('id');
            
            $.ajax({
                url: '<?= site_url('staff/farms/delete_farm_block_file') ?>/' + fileId,
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        loadFiles($('#farm_block_id').val());
                    } else {
                        alert(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    alert('An error occurred: ' + error);
                }
            });
        }
    });
    
    // Open edit caption modal
    $(document).on('click', '.edit-caption', function() {
        const fileId = $(this).data('id');
        const caption = $(this).data('caption');
        
        $('#edit_file_id').val(fileId);
        $('#edit_file_caption').val(caption);
        
        $('#editCaptionModal').modal('show');
    });
    
    // Save caption changes
    $('#saveCaption').on('click', function() {
        const fileId = $('#edit_file_id').val();
        const caption = $('#edit_file_caption').val();
        
        if (!caption) {
            alert('Caption is required');
            return;
        }
        
        $.ajax({
            url: '<?= site_url('staff/farms/update_farm_block_file') ?>',
            type: 'POST',
            data: {
                file_id: fileId,
                file_caption: caption
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    $('#editCaptionModal').modal('hide');
                    loadFiles($('#farm_block_id').val());
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            }
        });
    });
    });
</script>
<?= $this->endSection() ?>