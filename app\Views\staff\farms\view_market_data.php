<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/farms/marketing_data') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Marketing Data
        </a>
        <div>
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addMarketDataModal">
                <i class="fas fa-plus-circle"></i> Add Marketing Data
            </button>
        </div>
    </div>
</div>

<!-- Farmer Details Card -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-user"></i> Farmer Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Farmer Code:</strong> <?= esc($farmer['farmer_code']) ?></p>
                <p><strong>Name:</strong> <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></p>
                <p><strong>Gender:</strong> <?= esc($farmer['gender']) ?></p>
                <p><strong>Contact:</strong> <?= esc($farmer['phone']) ?: 'N/A' ?></p>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Location Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Village:</strong> <?= esc($farmer['village']) ?></p>
                <p><strong>Address:</strong> <?= esc($farmer['address']) ?: 'N/A' ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Marketing Data Table -->
<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-history"></i> Marketing History</h5>
    </div>
    <div class="card-body table-responsive">
        
            <table class="table table-bordered text-nowrap table-striped" id="marketDataTable">
                <thead>
                    <tr>
                        <th>Crop</th>
                        <th>Date</th>
                        <th>Product</th>
                        <th>Stage</th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Quantity</th>
                        <th>Unit</th>
                        <th>UoM</th>
                        <th>Price/Unit</th>
                        <th>Buyer</th>
                        <th>Remarks</th>
                        <th>Recorded By</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($marketing_data as $data): ?>
                        <tr class="clickable-row" data-href="<?= base_url('staff/farms/edit-market-data/' . $data['id']) ?>" style="cursor: pointer;">
                            <td>
                                <?php
                                foreach ($crops as $crop) {
                                    if ($crop['id'] === $data['crop_id']) {
                                        echo esc($crop['crop_name']);
                                        break;
                                    }
                                }
                                ?>
                            </td>
                            <td><?= date('d M Y', strtotime($data['market_date'])) ?></td>
                            <td><?= esc($data['product']) ?></td>
                            <td><?= esc($data['market_stage']) ?></td>
                            <td><?= esc($data['product_type']) ?></td>
                            <td><?= esc($data['description']) ?></td>
                            <td class="text-end"><?= number_format($data['quantity'], 2) ?></td>
                            <td class="text-end"><?= number_format($data['unit'], 2) ?></td>
                            <td><?= esc($data['unit_of_measure']) ?></td>
                            <td class="text-end"><?= number_format($data['market_price_per_unit'], 2) ?></td>
                            <td><?php
                                foreach ($buyers as $buyer) {
                                    if ($buyer['id'] === $data['buyer_id']) {
                                        echo esc($buyer['name']);
                                        break;
                                    }
                                }
                                ?></td>
                            <td><?= esc($data['remarks']) ?></td>
                            <td><?php
                                foreach ($users as $user) {
                                    if ($user['id'] === $data['created_by']) {
                                        echo esc($user['name']);
                                        break;
                                    }
                                }
                                ?></td>
                            <td>
                                <a href="<?= base_url('staff/farms/edit-market-data/' . $data['id']) ?>" 
                                   class="btn btn-sm btn-primary edit-btn">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        
    </div>
</div>

<!-- Add Marketing Data Modal -->
<div class="modal fade" id="addMarketDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add Marketing Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/farms/add-market-data', ['id' => 'addMarketForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="farmer_id" value="<?= $farmer['id'] ?>">

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="crop_id" class="form-label">Crop *</label>
                            <select name="crop_id" id="crop_id" class="form-select" required>
                                <option value="">Select Crop</option>
                                <?php foreach ($crops as $crop): ?>
                                    <option value="<?= $crop['id'] ?>"><?= esc($crop['crop_name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="block_id" class="form-label">Farm Block</label>
                            <select name="block_id" id="block_id" class="form-control select2bs4" style="width: 100%;" data-placeholder="Select Farm Block">
                                <option value=""></option>
                                <?php foreach ($farm_blocks as $block): ?>
                                    <option value="<?= $block['id'] ?>"><?= esc($block['block_code']) ?> - <?= esc($block['block_site']) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Optional. Select the farm block where this product came from.</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="product" class="form-label">Product Name *</label>
                            <input type="text" class="form-control" id="product" name="product" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="market_stage" class="form-label">Market Stage *</label>
                            <select class="form-select" id="market_stage" name="market_stage" required>
                                <option value="">Select Stage</option>
                                <option value="harvest">Harvest</option>
                                <option value="primary">Primary</option>
                                <option value="secondary">Secondary</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="product_type" class="form-label">Product Type *</label>
                            <input type="text" class="form-control" id="product_type" name="product_type" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="unit" class="form-label">Unit *</label>
                            <input type="number" step="0.01" class="form-control" id="unit" name="unit" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="unit_of_measure" class="form-label">Unit of Measure *</label>
                            <select name="unit_of_measure" id="unit_of_measure" class="form-select" required>
                                <option value="">Select Unit of Measure</option>
                                <option value="kg">Kilograms</option>
                                <option value="g">Grams</option>
                                <option value="l">Liters</option>
                                <option value="ml">Milliliters</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity *</label>
                            <input type="number" step="0.01" class="form-control" id="quantity" name="quantity" required>
                        </div>
                    </div>

                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="market_price_per_unit" class="form-label">Price per Unit *</label>
                            <input type="number" step="0.01" class="form-control" id="market_price_per_unit" name="market_price_per_unit" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="market_date" class="form-label">Market Date *</label>
                            <input type="date" class="form-control" id="market_date" name="market_date" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="total_freight_cost" class="form-label">Total Freight Cost</label>
                            <div class="input-group">
                                <span class="input-group-text">K</span>
                                <input type="number" step="0.01" min="0" class="form-control" id="total_freight_cost" name="total_freight_cost" placeholder="Enter freight cost">
                            </div>
                            <div class="form-text">Optional. Enter the total cost of transportation.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="selling_location" class="form-label">Selling Location</label>
                            <input type="text" class="form-control" id="selling_location" name="selling_location" placeholder="Enter specific selling location">
                            <div class="form-text">Optional. Enter the specific location where the produce was sold.</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="province_id" class="form-label">Province *</label>
                            <select name="province_id" id="province_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select Province" required>
                                <option value=""></option>
                                <?php foreach ($provinces as $province): ?>
                                    <option value="<?= $province['id'] ?>"><?= esc($province['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="district_id" class="form-label">District *</label>
                            <select name="district_id" id="district_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select District" required>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="llg_id" class="form-label">LLG *</label>
                            <select name="llg_id" id="llg_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select LLG" required>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="buyer_id" class="form-label">Buyer *</label>
                    <select name="buyer_id" id="buyer_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select Buyer" required>
                        <option value=""></option>
                        <?php foreach ($buyers as $buyer): ?>
                            <option value="<?= $buyer['id'] ?>"><?= esc($buyer['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                

                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Enter any additional remarks"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Save Data</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Marketing Data Modal -->
<div class="modal fade" id="editMarketDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Marketing Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/farms/update-market-data', ['id' => 'editMarketForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_id">
                <input type="hidden" name="farmer_id" value="<?= $farmer['id'] ?>">

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_crop_id" class="form-label">Crop *</label>
                            <select name="crop_id" id="edit_crop_id" class="form-select" required>
                                <option value="">Select Crop</option>
                                <?php foreach ($crops as $crop): ?>
                                    <option value="<?= $crop['id'] ?>"><?= esc($crop['crop_name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_block_id" class="form-label">Farm Block</label>
                            <select name="block_id" id="edit_block_id" class="form-control select2bs4" style="width: 100%;" data-placeholder="Select Farm Block">
                                <option value=""></option>
                                <?php foreach ($farm_blocks as $block): ?>
                                    <option value="<?= $block['id'] ?>"><?= esc($block['block_code']) ?> - <?= esc($block['block_site']) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Optional. Select the farm block where this product came from.</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_product" class="form-label">Product Name *</label>
                            <input type="text" class="form-control" id="edit_product" name="product" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_market_stage" class="form-label">Market Stage *</label>
                            <select class="form-select" id="edit_market_stage" name="market_stage" required>
                                <option value="">Select Stage</option>
                                <option value="harvest">Harvest</option>
                                <option value="primary">Primary</option>
                                <option value="secondary">Secondary</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_product_type" class="form-label">Product Type *</label>
                            <input type="text" class="form-control" id="edit_product_type" name="product_type" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="2"></textarea>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_unit" class="form-label">Unit *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_unit" name="unit" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_unit_of_measure" class="form-label">Unit of Measure *</label>
                            <select name="unit_of_measure" id="edit_unit_of_measure" class="form-select" required>
                                <option value="">Select Unit of Measure</option>
                                <option value="kg">Kilograms</option>
                                <option value="g">Grams</option>
                                <option value="l">Liters</option>
                                <option value="ml">Milliliters</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_quantity" class="form-label">Quantity *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_quantity" name="quantity" required>
                        </div>
                    </div>

                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_market_price_per_unit" class="form-label">Price per Unit *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_market_price_per_unit" name="market_price_per_unit" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_market_date" class="form-label">Market Date *</label>
                            <input type="date" class="form-control" id="edit_market_date" name="market_date" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_total_freight_cost" class="form-label">Total Freight Cost</label>
                            <input type="number" step="0.01" class="form-control" id="edit_total_freight_cost" name="total_freight_cost">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_selling_location" class="form-label">Selling Location</label>
                            <input type="text" class="form-control" id="edit_selling_location" name="selling_location">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_province_id" class="form-label">Province *</label>
                            <select name="province_id" id="edit_province_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select Province" required>
                                <option value=""></option>
                                <?php foreach ($provinces as $province): ?>
                                    <option value="<?= $province['id'] ?>"><?= esc($province['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_district_id" class="form-label">District *</label>
                            <select name="district_id" id="edit_district_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select District" required>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_llg_id" class="form-label">LLG *</label>
                            <select name="llg_id" id="edit_llg_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select LLG" required>
                                <option value=""></option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="edit_buyer_id" class="form-label">Buyer *</label>
                    <select name="buyer_id" id="edit_buyer_id" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select Buyer" required>
                        <option value=""></option>
                        <?php foreach ($buyers as $buyer): ?>
                            <option value="<?= $buyer['id'] ?>"><?= esc($buyer['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="edit_item" class="form-label">Item *</label>
                    <select name="item" id="edit_item" class="form-select select2bs4" style="width: 100%;" data-placeholder="Select Item" required>
                        <option value=""></option>
                        <?php foreach ($crops as $crop): ?>
                            <option value="<?= $crop['crop_name'] ?>"><?= esc($crop['crop_name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="edit_remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Update Data</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Basic Select2 initialization
    $('.select2bs4').select2({
        theme: 'bootstrap-5',
        width: '100%',
        allowClear: true,
        dropdownParent: $('body'),
        minimumResultsForSearch: 0,
        placeholder: function() {
            return $(this).data('placeholder');
        }
    });

    // Function to load districts
    function loadDistricts(provinceId, targetSelect) {
        if (!provinceId) {
            targetSelect.html('<option value="">Select District</option>').trigger('change');
            return;
        }

        $.ajax({
            url: '<?= base_url('api/get_districts') ?>',
            type: 'POST',
            data: { province_id: provinceId },
            dataType: 'json',
            beforeSend: function() {
                targetSelect.prop('disabled', true);
            },
            success: function(response) {
                var options = '<option value="">Select District</option>';
                if (Array.isArray(response)) {
                    response.forEach(function(district) {
                        options += `<option value="${district.id}">${district.name}</option>`;
                    });
                }
                targetSelect.html(options);
            },
            error: function(xhr, status, error) {
                console.error('Error loading districts:', error);
                toastr.error('Failed to load districts. Please try again.');
            },
            complete: function() {
                targetSelect.prop('disabled', false).trigger('change');
            }
        });
    }

    // Function to load LLGs
    function loadLLGs(districtId, targetSelect) {
        if (!districtId) {
            targetSelect.html('<option value="">Select LLG</option>').trigger('change');
            return;
        }

        $.ajax({
            url: '<?= base_url('api/get_llgs') ?>',
            type: 'POST',
            data: { district_id: districtId },
            dataType: 'json',
            beforeSend: function() {
                targetSelect.prop('disabled', true);
            },
            success: function(response) {
                var options = '<option value="">Select LLG</option>';
                if (Array.isArray(response)) {
                    response.forEach(function(llg) {
                        options += `<option value="${llg.id}">${llg.name}</option>`;
                    });
                }
                targetSelect.html(options);
            },
            error: function(xhr, status, error) {
                console.error('Error loading LLGs:', error);
                toastr.error('Failed to load LLGs. Please try again.');
            },
            complete: function() {
                targetSelect.prop('disabled', false).trigger('change');
            }
        });
    }

    // Handle province change for both add and edit forms
    $(document).on('change', '#province_id, #edit_province_id', function() {
        var provinceId = $(this).val();
        var isEdit = $(this).attr('id') === 'edit_province_id';
        var districtSelect = isEdit ? $('#edit_district_id') : $('#district_id');
        var llgSelect = isEdit ? $('#edit_llg_id') : $('#llg_id');

        // Reset and load districts
        loadDistricts(provinceId, districtSelect);
        
        // Reset LLG dropdown
        llgSelect.html('<option value="">Select LLG</option>').trigger('change');
    });

    // Handle district change for both add and edit forms
    $(document).on('change', '#district_id, #edit_district_id', function() {
        var districtId = $(this).val();
        var isEdit = $(this).attr('id') === 'edit_district_id';
        var llgSelect = isEdit ? $('#edit_llg_id') : $('#llg_id');

        // Load LLGs
        loadLLGs(districtId, llgSelect);
    });

    // Handle modal show event to reset dropdowns
    $('.modal').on('show.bs.modal', function() {
        var form = $(this).find('form');
        if (form.length) {
            form[0].reset();
            form.find('select').trigger('change');
        }
    });
});
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add click event listener to all rows with clickable-row class
        document.querySelectorAll('.clickable-row').forEach(row => {
            row.addEventListener('click', function(e) {
                // Don't trigger row click if clicking the edit button
                if (!e.target.closest('.edit-btn')) {
                    window.location.href = this.dataset.href;
                }
            });
        });

        // Add hover effect
        document.querySelectorAll('.clickable-row').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f5f5f5';
            });
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    });
</script>

<?= $this->endSection() ?>
