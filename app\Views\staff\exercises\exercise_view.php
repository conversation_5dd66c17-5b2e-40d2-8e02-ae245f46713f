<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Back Button and Title -->
<div class="row mb-4">
    <div class="col-12">
        <a href="<?= base_url('exercises') ?>" class="btn btn-outline-secondary mb-3">
            <i class="fas fa-arrow-left me-2"></i>Back to Exercises
        </a>
        <h2>
            <i class="fas fa-clipboard-list me-2"></i><?= esc($exercise['title']) ?>
            <span class="badge bg-<?= $exercise['status'] == 'active' ? 'success' : ($exercise['status'] == 'draft' ? 'warning' : ($exercise['status'] == 'cancelled' ? 'danger' : ($exercise['status'] == 'submitted' ? 'info' : 'primary'))) ?>">
                <?= ucfirst(esc($exercise['status'])) ?>
            </span>
        </h2>
        <p class="text-muted"><?= esc($exercise['description']) ?></p>
    </div>
</div>

<!-- Exercise Details -->
<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>Exercise Details</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <th style="width: 40%;">Period:</th>
                        <td><?= date('M d, Y', strtotime($exercise['date_from'])) ?> - <?= date('M d, Y', strtotime($exercise['date_to'])) ?></td>
                    </tr>
                    <tr>
                        <th>District:</th>
                        <td><?= esc($exercise['district_name'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Responsible Officer:</th>
                        <td><?= esc($exercise['officer_name'] ?? 'N/A') ?></td>
                    </tr>
                    <tr>
                        <th>Status:</th>
                        <td>
                            <span class="badge bg-<?= $exercise['status'] == 'active' ? 'success' : ($exercise['status'] == 'draft' ? 'warning' : ($exercise['status'] == 'cancelled' ? 'danger' : ($exercise['status'] == 'submitted' ? 'info' : 'primary'))) ?>">
                                <?= ucfirst(esc($exercise['status'])) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>Status Updated On:</th>
                        <td><?= !empty($exercise['status_at']) ? date('M d, Y H:i', strtotime($exercise['status_at'])) : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Status Updated By:</th>
                        <td><?= !empty($exercise['status_by_name']) ? esc($exercise['status_by_name']) : 'N/A' ?></td>
                    </tr>
                    <tr>
                        <th>Status Remarks:</th>
                        <td><?= !empty($exercise['status_remarks']) ? esc($exercise['status_remarks']) : 'No remarks' ?></td>
                    </tr>
                    <tr>
                        <th>Created On:</th>
                        <td><?= date('M d, Y', strtotime($exercise['created_at'])) ?></td>
                    </tr>
                </table>
            </div>
            <div class="card-footer bg-white">
                <div class="d-flex justify-content-between">
                    <a href="<?= base_url('exercises/edit/' . $exercise['id']) ?>" class="btn btn-primary w-100">
                        <i class="fas fa-edit me-1"></i> Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8 mb-4">
        <!-- Actions/Quick Access Section -->
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0"><i class="fas fa-database me-2"></i>Data Management</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6 col-md-3">
                        <a href="<?= base_url('staff/farms') ?>" class="btn btn-agri w-100 p-3 h-100">
                            <i class="fas fa-tractor fa-2x mb-2"></i>
                            <span class="d-block">Farm Blocks</span>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="<?= base_url('staff/farms/crops-blocks') ?>" class="btn btn-agri w-100 p-3 h-100">
                            <i class="fas fa-seedling fa-2x mb-2"></i>
                            <span class="d-block">Crops Data</span>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="<?= base_url('staff/farms/diseases_data') ?>" class="btn btn-agri w-100 p-3 h-100">
                            <i class="fas fa-bug fa-2x mb-2"></i>
                            <span class="d-block">Diseases Data</span>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="<?= base_url('staff/farms/fertilizer_data') ?>" class="btn btn-agri w-100 p-3 h-100">
                            <i class="fas fa-flask fa-2x mb-2"></i>
                            <span class="d-block">Fertilizer Data</span>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="<?= base_url('staff/farms/pesticides_data') ?>" class="btn btn-agri w-100 p-3 h-100">
                            <i class="fas fa-spray-can fa-2x mb-2"></i>
                            <span class="d-block">Pesticides Data</span>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="<?= base_url('staff/farms/harvest_data') ?>" class="btn btn-agri w-100 p-3 h-100">
                            <i class="fas fa-apple-alt fa-2x mb-2"></i>
                            <span class="d-block">Harvest Data</span>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="<?= base_url('staff/farms/marketing_data') ?>" class="btn btn-agri w-100 p-3 h-100">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <span class="d-block">Marketing Data</span>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="<?= base_url('staff/farms/maps') ?>" class="btn btn-agri w-100 p-3 h-100">
                            <i class="fas fa-map-marked-alt fa-2x mb-2"></i>
                            <span class="d-block">Field Maps</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assigned Officers -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i>Assigned Officers</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Officer ID</th>
                                <th>Officer Name</th>
                                <th>Date Assigned</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($officers)) : ?>
                                <?php foreach ($officers as $officer) : ?>
                                    <tr>
                                        <td><?= esc($officer['user_id']) ?></td>
                                        <td><?= esc($officer['user_name'] ?? 'N/A') ?></td>
                                        <td><?= date('M d, Y', strtotime($officer['created_at'])) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <tr>
                                    <td colspan="3" class="text-center">No officers assigned to this exercise.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusUpdateModal" tabindex="-1" aria-labelledby="statusUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusUpdateModalLabel">Update Exercise Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="statusUpdateForm">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="draft">Draft</option>
                            <option value="active">Active</option>
                            <option value="submitted">Submitted</option>
                            <option value="approved">Approved</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmStatusUpdate">Update Status</button>
            </div>
        </div>
    </div>
</div>

<!-- Custom Styles -->
<style>
.btn-agri {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #2e7d32;
    transition: all 0.3s ease;
}

.btn-agri:hover {
    background-color: #2e7d32;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .btn-agri {
        padding: 0.5rem !important;
    }
    
    .btn-agri i {
        font-size: 1.5rem !important;
    }
    
    .btn-agri span {
        font-size: 0.8rem;
    }
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Handle status update button click
    $('.btn-status-update').on('click', function() {
        $('#statusUpdateModal').modal('show');
    });

    // Handle confirm status update
    $('#confirmStatusUpdate').on('click', function() {
        const status = $('#status').val();
        const remarks = $('#remarks').val();
        
        if (!status) {
            toastr.error('Please select a status');
            return;
        }
        
        // Send AJAX request to update status
        $.ajax({
            url: '<?= base_url('exercises/updateStatus/' . $exercise['id']) ?>',
            type: 'POST',
            data: {
                status: status,
                remarks: remarks
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message);
                }
                $('#statusUpdateModal').modal('hide');
            },
            error: function() {
                toastr.error('An error occurred while updating status.');
                $('#statusUpdateModal').modal('hide');
            }
        });
    });
});
</script>
<?= $this->endSection() ?> 