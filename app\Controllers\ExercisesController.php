<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\ExerciseOfficerModel;
use App\Models\usersModel;
use App\Models\orgModel;
use App\Models\countryModel;
use App\Models\provinceModel;
use App\Models\districtModel;

class ExercisesController extends BaseController
{
    protected $exerciseModel;
    protected $exerciseOfficerModel;
    protected $usersModel;
    protected $orgModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;

    public function __construct()
    {
        // Load the info helper to make imgcheck() and other helper functions available
        helper('info');
        
        $this->exerciseModel = new ExerciseModel();
        $this->exerciseOfficerModel = new ExerciseOfficerModel();
        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
    }

    /**
     * Display list of exercises
     */
    public function index()
    {
        // Get exercises with district name
        $exercises = $this->exerciseModel->select('exercises.*, adx_district.name as district_name')
            ->join('adx_district', 'adx_district.id = exercises.district_id', 'left')
            ->findAll();
        
        $data = [
            'title' => 'Exercises',
            'exercises' => $exercises
        ];

        return view('staff/exercises/exercise_list', $data);
    }

    /**
     * Display create exercise form
     */
    public function create()
    {
        // Pre-select values from user session - using correct session names from Home.php
        $data = [
            'title' => 'Create Exercise',
            'users' => $this->usersModel->findAll(),
            'orgs' => $this->orgModel->findAll(),
            'countries' => $this->countryModel->findAll(),
            'provinces' => $this->provinceModel->findAll(),
            'districts' => $this->districtModel->findAll(),
            // Add session data for pre-selection with correct session names
            'selected_org_id' => session()->get('org_id'),
            'selected_country_id' => session()->get('orgcountry_id'),
            'selected_province_id' => session()->get('orgprovince_id'),
            'selected_district_id' => session()->get('district_id')
        ];

        return view('staff/exercises/exercise_create', $data);
    }

    /**
     * Save new exercise
     */
    public function store()
    {
        // Get form data for validation
        $postData = $this->request->getPost();
        
        // Explicitly set status as draft for new exercises (this ensures validation passes)
        $postData['status'] = 'draft';
        
        // Get organization and location from session with correct session names
        $postData['org_id'] = session()->get('org_id');
        $postData['country_id'] = session()->get('orgcountry_id');
        $postData['province_id'] = session()->get('orgprovince_id');
        $postData['district_id'] = session()->get('district_id');
        
        // Validate data
        $rules = $this->exerciseModel->getValidationRules();
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare exercise data with all required fields
        $exerciseData = [
            'org_id' => session()->get('org_id'),
            'country_id' => session()->get('orgcountry_id'),
            'province_id' => session()->get('orgprovince_id'),
            'district_id' => session()->get('district_id'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'officer_responsible_id' => $this->request->getPost('officer_responsible_id'),
            'status' => 'draft', // Explicitly set status to draft
            'status_at' => date('Y-m-d H:i:s'),
            'status_by' => session()->get('emp_id'),
            'created_by' => session()->get('emp_id')
        ];

        // Save exercise
        $exerciseId = $this->exerciseModel->insert($exerciseData);

        if (!$exerciseId) {
            return redirect()->back()->withInput()->with('error', 'Failed to create exercise');
        }

        // Handle officers assignment if any
        $officers = $this->request->getPost('officers');
        if (!empty($officers) && is_array($officers)) {
            foreach ($officers as $userId) {
                $this->exerciseOfficerModel->insert([
                    'exercise_id' => $exerciseId,
                    'user_id' => $userId,
                    'created_by' => session()->get('emp_id')
                ]);
            }
        }

        return redirect()->to('exercises')->with('success', 'Exercise created successfully');
    }

    /**
     * Display exercise details
     */
    public function view($id)
    {
        // Get exercise with district name and officer name
        $exercise = $this->exerciseModel->select('exercises.*, adx_district.name as district_name, users.name as officer_name, status_users.name as status_by_name')
            ->join('adx_district', 'adx_district.id = exercises.district_id', 'left')
            ->join('users', 'users.id = exercises.officer_responsible_id', 'left')
            ->join('users as status_users', 'status_users.id = exercises.status_by', 'left')
            ->find($id);
        
        if (!$exercise) {
            return redirect()->to('exercises')->with('error', 'Exercise not found');
        }

        // Get assigned officers with their names
        $officers = $this->exerciseOfficerModel->select('exercise_officers.*, users.name as user_name')
            ->join('users', 'users.id = exercise_officers.user_id', 'left')
            ->where('exercise_id', $id)
            ->findAll();

        $data = [
            'title' => 'View Exercise',
            'exercise' => $exercise,
            'officers' => $officers
        ];

        return view('staff/exercises/exercise_view', $data);
    }

    /**
     * Display edit exercise form
     */
    public function edit($id)
    {
        // Get exercise with district name and officer name
        $exercise = $this->exerciseModel->select('exercises.*, adx_district.name as district_name, users.name as officer_name')
            ->join('adx_district', 'adx_district.id = exercises.district_id', 'left')
            ->join('users', 'users.id = exercises.officer_responsible_id', 'left')
            ->find($id);
        
        if (!$exercise) {
            return redirect()->to('exercises')->with('error', 'Exercise not found');
        }

        // Get assigned officers with their names
        $officers = $this->exerciseOfficerModel->select('exercise_officers.*, users.name as user_name')
            ->join('users', 'users.id = exercise_officers.user_id', 'left')
            ->where('exercise_id', $id)
            ->findAll();

        $data = [
            'title' => 'Edit Exercise',
            'exercise' => $exercise,
            'users' => $this->usersModel->findAll(),
            'orgs' => $this->orgModel->findAll(),
            'countries' => $this->countryModel->findAll(),
            'provinces' => $this->provinceModel->findAll(),
            'districts' => $this->districtModel->findAll(),
            'officers' => $officers
        ];

        return view('staff/exercises/exercise_edit', $data);
    }

    /**
     * Update exercise
     */
    public function update($id)
    {
        $exercise = $this->exerciseModel->find($id);
        
        if (!$exercise) {
            return redirect()->to('exercises')->with('error', 'Exercise not found');
        }

        // Get validation rules but exclude status field for the update method
        $rules = $this->exerciseModel->getValidationRules();
        unset($rules['status']); // Remove status validation requirement
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Get organization and location from session
        $orgId = session()->get('org_id');
        $countryId = session()->get('orgcountry_id');
        $provinceId = session()->get('orgprovince_id');
        $districtId = session()->get('district_id');

        // Prepare exercise data
        $exerciseData = [
            'org_id' => $orgId,
            'country_id' => $countryId,
            'province_id' => $provinceId,
            'district_id' => $districtId,
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'officer_responsible_id' => $this->request->getPost('officer_responsible_id'),
            'updated_by' => session()->get('emp_id')
        ];

        // Update exercise
        $this->exerciseModel->update($id, $exerciseData);

        // Handle officers assignment
        // First, remove existing officers
        $this->exerciseOfficerModel->where('exercise_id', $id)->delete();
        
        // Then add selected officers
        $officers = $this->request->getPost('officers');
        if (!empty($officers) && is_array($officers)) {
            foreach ($officers as $userId) {
                $this->exerciseOfficerModel->insert([
                    'exercise_id' => $id,
                    'user_id' => $userId,
                    'created_by' => session()->get('emp_id')
                ]);
            }
        }

        return redirect()->to('exercises')->with('success', 'Exercise updated successfully');
    }

    /**
     * Update exercise status
     */
    public function updateStatus($id)
    {
        $exercise = $this->exerciseModel->find($id);
        
        if (!$exercise) {
            return $this->response->setJSON(['success' => false, 'message' => 'Exercise not found']);
        }

        $status = $this->request->getPost('status');
        $remarks = $this->request->getPost('remarks');

        if (!in_array($status, ['draft', 'active', 'submitted', 'approved', 'cancelled'])) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid status']);
        }

        $data = [
            'status' => $status,
            'status_at' => date('Y-m-d H:i:s'),
            'status_by' => session()->get('emp_id'),
            'status_remarks' => $remarks,
            'updated_by' => session()->get('emp_id')
        ];

        $this->exerciseModel->update($id, $data);

        return $this->response->setJSON([
            'success' => true, 
            'message' => 'Exercise status updated successfully'
        ]);
    }
} 