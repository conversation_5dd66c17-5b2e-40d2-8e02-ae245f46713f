<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Crops Diseases Dashboard</h1>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <!-- Total Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="totalBlocksCount">0</h3>
                        <p>Total Affected Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <!-- Total Plants Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3 id="totalPlantsAffected">0</h3>
                        <p>Total Plants Affected</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
            <!-- Total Diseases Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3 id="totalDiseases">0</h3>
                        <p>Total Diseases</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-virus"></i>
                    </div>
                </div>
            </div>
            <!-- Total Farmers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3 id="totalFarmers">0</h3>
                        <p>Total Farmers Affected</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Diseases Data Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Diseases Data</h3>
                    </div>
                    <div class="card-body">
                        <!-- Location Filters -->
                        <div id="filterContainer" class="mb-3 d-flex flex-wrap gap-2">
                            <!-- Filters will be inserted here by JavaScript -->
                        </div>
                        <div class="table-responsive">
                            <table id="diseasesDataTable" class="table table-bordered text-nowrap table-striped">
                                <thead>
                                    <tr>
                                        <th>Block Code</th>
                                        <th>Farmer Name</th>
                                        <th>Crop</th>
                                        <th>Disease</th>
                                        <th>Plants Affected</th>
                                        <th>Province</th>
                                        <th>District</th>
                                        <th>LLG</th>
                                        <th>Ward</th>
                                        <th>Village</th>
                                        <th>Latitude</th>
                                        <th>Longitude</th>
                                        <th>Latest Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($diseases as $disease): ?>
                                    <tr>
                                        <td><?= $disease['block_code'] ?></td>
                                        <td><?= trim($disease['given_name'] . ' ' . $disease['surname']) ?></td>
                                        <td><?= $disease['crop_name'] ?></td>
                                        <td><?= $disease['disease_name'] ?></td>
                                        <td><?= number_format($disease['total_plants_affected']) ?></td>
                                        <td><?= $disease['province_name'] ?></td>
                                        <td><?= $disease['district_name'] ?></td>
                                        <td><?= $disease['llg_name'] ?></td>
                                        <td><?= $disease['ward_name'] ?></td>
                                        <td><?= $disease['block_site'] ?></td>
                                        <td><?= $disease['lat'] ?? 'N/A' ?></td>
                                        <td><?= $disease['lon'] ?? 'N/A' ?></td>
                                        <td><?= date('d/m/Y', strtotime($disease['latest_action_date'])) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Distribution -->
        <div class="row">
            <!-- Disease Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Disease Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="diseaseDistributionChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Crop Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Affected Crops Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Location -->
        <div class="row">
            <!-- Location Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Location Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="locationChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Monthly Trend -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Monthly Disease Trend</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Map Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Disease Locations Map</h3>
                    </div>
                    <div class="card-body">
                        <div id="diseaseMap" style="height: 600px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
class FilterableTable {
    constructor(tableId) {
        this.table = document.getElementById(tableId);
        this.tableId = tableId;
        this.dataTable = $(`#${tableId}`).DataTable();
        this.filterContainer = document.getElementById('filterContainer');
        this.filters = {
            crop: [],
            disease: [],
            province: [],
            district: [],
            llg: [],
            ward: []
        };
        this.map = null;
        this.markers = L.markerClusterGroup();
        this.init();

        // Add drawCallback after initialization
        this.dataTable.on('draw', () => {
            this.updateSummaryCards();
            this.initializeCharts();
            this.updateMap();
            
            // Update dependent filters based on current selection
            const filterOrder = ['crop', 'disease', 'province', 'district', 'llg', 'ward'];
            filterOrder.forEach((filterId, index) => {
                if (index > 0 && this.filters[filterOrder[index - 1]].length > 0) {
                    this.updateFilterDropdown(filterId);
                }
            });
        });
    }

    init() {
        this.createLocationFilters();
        this.updateSummaryCards();
        this.initializeCharts();
        this.initializeMap();
    }

    createLocationFilters() {
        const locations = [
            { id: 'crop', label: 'Crop', column: 2 },
            { id: 'disease', label: 'Disease', column: 3 },
            { id: 'province', label: 'Province', column: 5 },
            { id: 'district', label: 'District', column: 6 },
            { id: 'llg', label: 'LLG', column: 7 },
            { id: 'ward', label: 'Ward', column: 8 }
        ];

        locations.forEach(location => {
            const filterGroup = this.createFilterDropdown(location);
            this.filterContainer.appendChild(filterGroup);
        });
    }

    createFilterDropdown(location) {
        const container = document.createElement('div');
        container.className = 'dropdown mr-2';
        container.innerHTML = `
            <button class="btn btn-default dropdown-toggle" type="button" 
                    data-toggle="dropdown" data-filter="${location.id}" aria-haspopup="true" aria-expanded="false">
                ${location.label} <span class="badge badge-light"></span>
            </button>
            <div class="dropdown-menu p-2" style="min-width: 250px; max-height: 300px; overflow-y: auto;" data-boundary="viewport">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" 
                           id="selectAll_${location.id}" data-filter="${location.id}">
                    <label class="custom-control-label" for="selectAll_${location.id}">Select All</label>
                </div>
                <hr class="my-2">
                <div id="options_${location.id}"></div>
            </div>
        `;

        // Prevent dropdown from closing when clicking inside
        container.querySelector('.dropdown-menu').addEventListener('click', (e) => {
            e.stopPropagation();
        });

        this.populateFilterOptions(location, container);
        return container;
    }

    populateFilterOptions(location, container) {
        const optionsContainer = container.querySelector(`#options_${location.id}`);
        const uniqueValues = new Set();
        
        // Get unique values from the column
        const columnData = this.dataTable
            .rows()
            .data()
            .toArray()
            .map(row => row[location.column])
            .filter(value => value); // Filter out empty values
        
        // Add unique values to Set
        columnData.forEach(value => uniqueValues.add(value));

        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${location.id}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${location.id}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        this.setupFilterEvents(container, location.id);
    }

    setupFilterEvents(container, filterId) {
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        const checkboxes = container.querySelectorAll(`input[type="checkbox"][data-filter="${filterId}"]:not(#selectAll_${filterId})`);
        const button = container.querySelector('button');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (event) => {
                this.handleFilterChange(checkbox, filterId);
                this.updateBadgeCount(button, filterId);
                selectAll.checked = Array.from(checkboxes).every(cb => cb.checked);
                selectAll.indeterminate = Array.from(checkboxes).some(cb => cb.checked) && !selectAll.checked;
            });
        });

        selectAll.addEventListener('change', (event) => {
            checkboxes.forEach(cb => {
                if (!cb.disabled) {
                    cb.checked = selectAll.checked;
                    this.handleFilterChange(cb, filterId);
                }
            });
            this.updateBadgeCount(button, filterId);
            event.stopPropagation();
        });

        this.updateBadgeCount(button, filterId);
    }

    updateBadgeCount(button, filterId) {
        if (!button) return;
        
        const count = this.filters[filterId].length;
        const badge = button.querySelector('.badge');
        if (badge) {
            badge.textContent = count > 0 ? count : '';
            badge.style.display = count > 0 ? 'inline-block' : 'none';
        }
    }

    handleFilterChange(checkbox, filterId) {
        const value = checkbox.value;
        
        // Update filters array
        if (checkbox.checked) {
            if (!this.filters[filterId].includes(value)) {
                this.filters[filterId].push(value);
            }
        } else {
            this.filters[filterId] = this.filters[filterId].filter(v => v !== value);
        }

        // Reset dependent filters
        const filterOrder = ['crop', 'disease', 'province', 'district', 'llg', 'ward'];
        const currentIndex = filterOrder.indexOf(filterId);
        
        // Only reset filters that come after the current one
        if (currentIndex !== -1) {
            filterOrder.slice(currentIndex + 1).forEach(dependentFilterId => {
                this.filters[dependentFilterId] = [];
                this.resetFilterDropdown(dependentFilterId);
                this.updateFilterDropdown(dependentFilterId);
            });
        }

        // Apply filters to DataTable
        this.applyFilters();
    }

    resetFilterDropdown(filterId) {
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        if (!container) return;

        // Reset checkboxes
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = false;
            cb.disabled = false;
        });

        // Reset select all checkbox
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        if (selectAll) {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        }

        // Reset badge count
        const button = container.querySelector('button');
        this.updateBadgeCount(button, filterId);
    }

    updateFilterDropdown(filterId) {
        const filterOrder = ['crop', 'disease', 'province', 'district', 'llg', 'ward'];
        const columnIndices = { 
            crop: 2,
            disease: 3,
            province: 5,
            district: 6,
            llg: 7,
            ward: 8
        };
        
        // Get all visible rows that match current filters
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data().toArray();
        
        // Get the current filter's container
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        const optionsContainer = container.querySelector(`#options_${filterId}`);
        
        // Clear existing options
        optionsContainer.innerHTML = '';
        
        // Get unique values for this filter that match parent filters
        const uniqueValues = new Set();
        
        visibleRows.forEach(row => {
            // Check if this row matches all parent filters
            const matchesParentFilters = filterOrder.slice(0, filterOrder.indexOf(filterId)).every(parentId => {
                const parentValues = this.filters[parentId];
                return parentValues.length === 0 || parentValues.includes(row[columnIndices[parentId]]);
            });
            
            if (matchesParentFilters) {
                const value = row[columnIndices[filterId]];
                if (value) uniqueValues.add(value);
            }
        });

        // Create new options
        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${filterId}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${filterId}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        // Reattach event listeners
        this.setupFilterEvents(container, filterId);
    }

    applyFilters() {
        // Clear existing search functions
        $.fn.dataTable.ext.search = [];

        // Add filter function
        $.fn.dataTable.ext.search.push((settings, searchData) => {
            if (settings.nTable.id !== this.tableId) return true;

            return Object.entries(this.filters).every(([key, values]) => {
                if (values.length === 0) return true;
                
                const columnIndex = {
                    crop: 2,
                    disease: 3,
                    province: 5,
                    district: 6,
                    llg: 7,
                    ward: 8
                }[key];

                return values.includes(searchData[columnIndex]);
            });
        });

        // Redraw table and update UI
        this.dataTable.draw();
        this.updateSummaryCards();
        this.initializeCharts();
        this.updateMap();
    }

    calculateSummaryData() {
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data();
        let totalPlantsAffected = 0;
        const uniqueBlocks = new Set();
        const uniqueFarmers = new Set();
        const uniqueDiseases = new Set();

        visibleRows.each(row => {
            const plantsAffected = parseInt(row[4].replace(/[^\d.-]/g, '')) || 0;
            totalPlantsAffected += plantsAffected;
            uniqueBlocks.add(row[0]); // Block Code
            uniqueFarmers.add(row[1]); // Farmer Name
            uniqueDiseases.add(row[3]); // Disease
        });

        return {
            totalBlocks: uniqueBlocks.size,
            totalPlantsAffected,
            totalDiseases: uniqueDiseases.size,
            totalFarmers: uniqueFarmers.size
        };
    }

    updateSummaryCards() {
        const summaryData = this.calculateSummaryData();
        
        const animateCounter = (element, value, decimals = 0) => {
            if (!element) return;
            
            const duration = 1000;
            const startValue = parseFloat(element.textContent.replace(/[^\d.-]/g, '')) || 0;
            const startTime = performance.now();
            
            const update = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentValue = startValue + (value - startValue) * progress;
                element.textContent = currentValue.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            };
            
            requestAnimationFrame(update);
        };

        animateCounter(document.getElementById('totalBlocksCount'), summaryData.totalBlocks);
        animateCounter(document.getElementById('totalPlantsAffected'), summaryData.totalPlantsAffected);
        animateCounter(document.getElementById('totalDiseases'), summaryData.totalDiseases);
        animateCounter(document.getElementById('totalFarmers'), summaryData.totalFarmers);
    }

    initializeCharts() {
        const visibleData = this.dataTable.rows({ search: 'applied' }).data();
        const chartData = {
            diseases: {},
            crops: {},
            locations: {},
            monthlyTrend: {}
        };

        visibleData.each(row => {
            const disease = row[3];
            const crop = row[2];
            const district = row[6];
            const plantsAffected = parseInt(row[4].replace(/[^\d.-]/g, '')) || 0;
            const date = row[12];
            const month = new Date(date.split('/').reverse().join('-')).toLocaleString('default', { month: 'long', year: 'numeric' });

            // Disease distribution
            chartData.diseases[disease] = (chartData.diseases[disease] || 0) + plantsAffected;

            // Crop distribution
            chartData.crops[crop] = (chartData.crops[crop] || 0) + plantsAffected;

            // Location distribution
            chartData.locations[district] = (chartData.locations[district] || 0) + plantsAffected;

            // Monthly trend
            chartData.monthlyTrend[month] = (chartData.monthlyTrend[month] || 0) + plantsAffected;
        });

        this.updateChart('diseaseDistributionChart', {
            type: 'pie',
            data: chartData.diseases,
            title: 'Disease Distribution by Affected Plants'
        });

        this.updateChart('cropDistributionChart', {
            type: 'pie',
            data: chartData.crops,
            title: 'Affected Crops Distribution'
        });

        this.updateChart('locationChart', {
            type: 'bar',
            data: chartData.locations,
            title: 'Location Distribution',
            yAxisLabel: 'Plants Affected'
        });

        // Sort monthly data chronologically
        const sortedMonthlyData = Object.fromEntries(
            Object.entries(chartData.monthlyTrend).sort((a, b) => {
                return new Date(a[0]) - new Date(b[0]);
            })
        );

        this.updateChart('monthlyTrendChart', {
            type: 'line',
            data: sortedMonthlyData,
            title: 'Monthly Disease Trend',
            yAxisLabel: 'Plants Affected'
        });
    }

    updateChart(chartId, config) {
        const ctx = document.getElementById(chartId).getContext('2d');
        const chartInstance = Chart.getChart(ctx);
        if (chartInstance) {
            chartInstance.destroy();
        }

        const colors = [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 206, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
            'rgba(153, 102, 255, 0.2)',
            'rgba(255, 159, 64, 0.2)'
        ];
        
        const borderColors = colors.map(color => color.replace('0.2', '1'));

        new Chart(ctx, {
            type: config.type,
            data: {
                labels: Object.keys(config.data),
                datasets: [{
                    data: Object.values(config.data),
                    backgroundColor: config.type === 'pie' ? colors : colors[0],
                    borderColor: config.type === 'pie' ? borderColors : borderColors[0],
                    borderWidth: 1,
                    tension: config.type === 'line' ? 0.4 : 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: config.type === 'pie' ? 'right' : 'top'
                    },
                    title: {
                        display: true,
                        text: config.title
                    }
                },
                scales: (config.type === 'bar' || config.type === 'line') ? {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: config.yAxisLabel
                        }
                    }
                } : undefined
            }
        });
    }

    initializeMap() {
        // Initialize the map centered on Papua New Guinea
        this.map = L.map('diseaseMap').setView([-6.314993, 143.95555], 6);
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(this.map);

        // Add empty marker cluster group
        this.markers.addTo(this.map);

        // Initial map update
        this.updateMap();
    }

    updateMap() {
        // Clear existing markers
        this.markers.clearLayers();

        // Get visible rows after filtering
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data();

        // Add markers for each visible row
        visibleRows.each(row => {
            const lat = parseFloat(row[10]); // Latitude column
            const lon = parseFloat(row[11]); // Longitude column
            
            if (!isNaN(lat) && !isNaN(lon)) {
                const marker = L.marker([lat, lon]);
                
                // Create popup content
                const popupContent = `
                    <strong>Block:</strong> ${row[0]}<br>
                    <strong>Farmer:</strong> ${row[1]}<br>
                    <strong>Crop:</strong> ${row[2]}<br>
                    <strong>Disease:</strong> ${row[3]}<br>
                    <strong>Plants Affected:</strong> ${row[4]}<br>
                    <strong>Location:</strong> ${row[5]}, ${row[6]}, ${row[7]}<br>
                    <strong>Latest Action:</strong> ${row[12]}
                `;
                
                marker.bindPopup(popupContent);
                this.markers.addLayer(marker);
            }
        });

        // Adjust map bounds if there are markers
        if (this.markers.getLayers().length > 0) {
            this.map.fitBounds(this.markers.getBounds());
        }
    }
}

$(document).ready(function() {
    // Initialize DataTable with all options
    const dataTable = $('#diseasesDataTable').DataTable({
        responsive: false,
        lengthChange: true,
        autoWidth: false,
        buttons: ["copy", "excel", "pdf", "print"],
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'All']],
        order: [[12, 'desc']] // Sort by latest action date by default
    });

    // Move buttons to the correct container
    dataTable.buttons().container().appendTo('#diseasesDataTable_wrapper .col-md-6:eq(0)');

    // Initialize FilterableTable
    new FilterableTable('diseasesDataTable');
});
</script>

<style>
.card-body {
    min-height: 400px;
}
.dropdown {
    display: inline-block;
    margin-right: 1rem;
    margin-bottom: 1rem;
}
.dropdown-menu {
    padding: 10px;
}
.badge {
    margin-left: 5px;
}
.custom-control {
    margin-bottom: 5px;
}
.custom-control-input:disabled ~ .custom-control-label {
    opacity: 0.5;
}
.table-responsive {
    min-height: 400px;
}
.dt-buttons {
    margin-bottom: 1rem;
}
#diseaseMap {
    width: 100%;
    height: 600px;
    border-radius: 4px;
}
</style>
<?= $this->endSection() ?>
