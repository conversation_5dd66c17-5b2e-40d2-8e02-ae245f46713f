<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Location Dashboard</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">Home</a></li>
                    <li class="breadcrumb-item active">Location Dashboard</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Error Alert -->
<div class="alert alert-danger alert-dismissible" id="error-alert" style="display: none;">
    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
    <h5><i class="icon fas fa-ban"></i> Error!</h5>
    <span id="error-message"></span>
</div>

<!-- Loading Indicator -->
<div class="overlay" id="loading-indicator" style="display: none;">
    <i class="fas fa-2x fa-sync-alt fa-spin"></i>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Location Filters -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Location Filters</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Province Dropdown -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="province">Province</label>
                            <select class="form-control select2bs4" id="province" name="province" style="width: 100%;">
                                <option value="">Select Province</option>
                                <option value="all">All Provinces</option>
                                <?php foreach ($provinces as $province) : ?>
                                    <option value="<?= $province['id'] ?>"><?= $province['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <!-- District Dropdown -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="district">District</label>
                            <select class="form-control select2bs4" id="district" name="district" style="width: 100%;" disabled>
                                <option value="">Select District</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- LLG Dropdown -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="llg">LLG</label>
                            <select class="form-control select2bs4" id="llg" name="llg" style="width: 100%;" disabled>
                                <option value="">Select LLG</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Ward Dropdown -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="ward">Ward</label>
                            <select class="form-control select2bs4" id="ward" name="ward" style="width: 100%;" disabled>
                                <option value="">Select Ward</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div class="row">
            <!-- Farmers Information -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Farmers Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Farmers</span>
                                        <span class="info-box-number" id="total-farmers">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">Gender Distribution</h3>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="gender-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">Age Distribution</h3>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="age-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">Education Level Distribution</h3>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="education-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Crop Blocks Information -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Blocks Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="fas fa-seedling"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Blocks</span>
                                        <span class="info-box-number" id="total-crop-blocks">0</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-box">
                                    <span class="info-box-icon bg-warning"><i class="fas fa-chart-area"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Hectares</span>
                                        <span class="info-box-number" id="total-hectares">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">Crop Types Distribution</h3>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="crop-types-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-box">
                                    <span class="info-box-icon bg-danger"><i class="fas fa-bug"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Diseased Hectares</span>
                                        <span class="info-box-number" id="diseased-hectares">0</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="fas fa-balance-scale"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Balance Hectares</span>
                                        <span class="info-box-number" id="balance-hectares">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Livestock Information -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Livestock Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-cow"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Farm Blocks</span>
                                <span class="info-box-number" id="total-livestock-blocks">0</span>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Livestock Distribution</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="livestock-chart"></canvas>
                                <div class="table-responsive mt-3">
                                    <table class="table table-bordered table-sm" id="livestock-summary-table">
                                        <thead>
                                            <tr>
                                                <th>Livestock Type</th>
                                                <th>Total Count</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Market Information -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Market Information</h3>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="marketTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="crops-tab" data-toggle="tab" href="#crops" role="tab">Crops</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="livestock-tab" data-toggle="tab" href="#livestock" role="tab">Livestock</a>
                            </li>
                        </ul>
                        <div class="tab-content" id="marketTabContent">
                            <div class="tab-pane fade show active" id="crops" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="crops-market-table">
                                        <thead>
                                            <tr>
                                                <th>Crop</th>
                                                <th>Min Cost</th>
                                                <th>Max Cost</th>
                                                <th>Avg Cost</th>
                                                <th>Min Price</th>
                                                <th>Max Price</th>
                                                <th>Avg Price</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="livestock" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="livestock-market-table">
                                        <thead>
                                            <tr>
                                                <th>Livestock</th>
                                                <th>Min Cost</th>
                                                <th>Max Cost</th>
                                                <th>Avg Cost</th>
                                                <th>Min Price</th>
                                                <th>Max Price</th>
                                                <th>Avg Price</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Fertilizers and Pesticides -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Agricultural Inputs</h3>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="inputsTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="fertilizers-tab" data-toggle="tab" href="#fertilizers" role="tab">Fertilizers</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pesticides-tab" data-toggle="tab" href="#pesticides" role="tab">Pesticides</a>
                            </li>
                        </ul>
                        <div class="tab-content" id="inputsTabContent">
                            <div class="tab-pane fade show active" id="fertilizers" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="fertilizers-table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Total Quantity</th>
                                                <th>Unit</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pesticides" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="pesticides-table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Total Quantity</th>
                                                <th>Unit</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for Charts and Data Loading -->
<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2 with Bootstrap 4 theme
    $('.select2bs4').select2({
        theme: 'bootstrap4',
        width: '100%',
        placeholder: 'Select an option',
        allowClear: true
    });

    // Initialize Charts
    let genderChart = new Chart($('#gender-chart'), {
        type: 'pie',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: ['#007bff', '#dc3545', '#ffc107']
            }]
        }
    });

    let ageChart = new Chart($('#age-chart'), {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Age Distribution',
                data: [],
                backgroundColor: '#17a2b8'
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    let educationChart = new Chart($('#education-chart'), {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Education Level',
                data: [],
                backgroundColor: '#28a745'
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    let cropTypesChart = new Chart($('#crop-types-chart'), {
        type: 'pie',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: []
            }]
        },
        options: {
            plugins: {
                legend: {
                    display: true,
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            let value = context.raw || 0;
                            return label + ': ' + value + ' blocks';
                        }
                    }
                }
            }
        }
    });

    let livestockChart = new Chart($('#livestock-chart'), {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Female',
                data: [],
                backgroundColor: [],
                stack: 'Stack 0'
            }, {
                label: 'Male',
                data: [],
                backgroundColor: [],
                stack: 'Stack 0'
            }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            let value = context.raw || 0;
                            return label + ': ' + value;
                        }
                    }
                }
            }
        }
    });

    // Function to show error message
    function showError(message) {
        $('#error-message').text(message);
        $('#error-alert').fadeIn();
    }

    // Function to hide error message
    function hideError() {
        $('#error-alert').fadeOut();
    }

    // Function to show loading indicator
    function showLoading() {
        $('#loading-indicator').show();
    }

    // Function to hide loading indicator
    function hideLoading() {
        $('#loading-indicator').hide();
    }

    // Function to load dashboard data
    function loadDashboardData() {
        hideError();
        showLoading();
        
        const params = {
            province_id: $('#province').val(),
            district_id: $('#district').val(),
            llg_id: $('#llg').val(),
            ward_id: $('#ward').val(),
            '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
        };
        
        // Debug info
        console.log('Loading dashboard data with params:', params);
        
        // Load Farmers Data
        $.ajax({
            url: '<?= base_url('location/dashboard/get-dashboard-farmers-data') ?>',
            type: 'POST',
            dataType: 'json',
            data: params,
            success: function(response) {
                if (!response.error && response.data) {
                    updateFarmersInfo(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Farmers Data Error:', error);
            }
        });

        // Load Crops Data
        $.ajax({
            url: '<?= base_url('location/dashboard/get-dashboard-crops-data') ?>',
            type: 'POST',
            dataType: 'json',
            data: params,
            success: function(response) {
                if (!response.error && response.data) {
                    updateCropsInfo(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Crops Data Error:', error);
            }
        });

        // Load Livestock Data
        $.ajax({
            url: '<?= base_url('location/dashboard/get-dashboard-livestock-data') ?>',
            type: 'POST',
            dataType: 'json',
            data: params,
            success: function(response) {
                if (!response.error && response.data) {
                    updateLivestockInfo(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Livestock Data Error:', error);
            }
        });

        // Load Market Data
        $.ajax({
            url: '<?= base_url('location/dashboard/get-dashboard-market-data') ?>',
            type: 'POST',
            dataType: 'json',
            data: params,
            success: function(response) {
                if (!response.error && response.data) {
                    updateMarketInfo(response.data, []);  // Update crops market data
                }
            },
            error: function(xhr, status, error) {
                console.error('Market Data Error:', error);
            }
        });

        // Load Livestock Market Data
        $.ajax({
            url: '<?= base_url('location/dashboard/get-dashboard-livestock-market-data') ?>',
            type: 'POST',
            dataType: 'json',
            data: params,
            success: function(response) {
                if (!response.error && response.data) {
                    updateMarketInfo([], response.data);  // Update livestock market data
                }
            },
            error: function(xhr, status, error) {
                console.error('Livestock Market Data Error:', error);
            }
        });

        // Load Fertilizer Data
        $.ajax({
            url: '<?= base_url('location/dashboard/get-dashboard-fertilizer-data') ?>',
            type: 'POST',
            dataType: 'json',
            data: params,
            success: function(response) {
                if (!response.error && response.data) {
                    updateInputsInfo(response.data, []);  // Update fertilizers data
                }
            },
            error: function(xhr, status, error) {
                console.error('Fertilizer Data Error:', error);
            }
        });

        // Load Pesticide Data
        $.ajax({
            url: '<?= base_url('location/dashboard/get-dashboard-pesticide-data') ?>',
            type: 'POST',
            dataType: 'json',
            data: params,
            success: function(response) {
                if (!response.error && response.data) {
                    updateInputsInfo([], response.data);  // Update pesticides data
                }
            },
            error: function(xhr, status, error) {
                console.error('Pesticide Data Error:', error);
            },
            complete: function() {
                hideLoading();
            }
        });
    }

    // Update Farmers Information
    function updateFarmersInfo(data) {
        $('#total-farmers').text(data.total);

        // Update Gender Chart
        genderChart.data.labels = data.gender_distribution.map(item => item.gender);
        genderChart.data.datasets[0].data = data.gender_distribution.map(item => item.count);
        genderChart.update();

        // Update Age Chart
        ageChart.data.labels = data.age_distribution.map(item => item.age_group);
        ageChart.data.datasets[0].data = data.age_distribution.map(item => item.count);
        ageChart.update();

        // Update Education Chart
        educationChart.data.labels = data.education_distribution.map(item => item.highest_education_id);
        educationChart.data.datasets[0].data = data.education_distribution.map(item => item.count);
        educationChart.update();
    }

    // Update Crops Information
    function updateCropsInfo(data) {
        $('#total-crop-blocks').text(data.total_blocks);
        $('#total-hectares').text(data.hectares_info.total_hectares);
        $('#diseased-hectares').text(data.disease_info.diseased_hectares);
        $('#balance-hectares').text(
            data.hectares_info.total_hectares - 
            data.hectares_info.removed_hectares - 
            data.disease_info.diseased_hectares
        );

        // Update Crop Types Chart with names and colors
        cropTypesChart.data.labels = data.crop_types.map(item => item.crop_name);
        cropTypesChart.data.datasets[0].data = data.crop_types.map(item => item.count);
        cropTypesChart.data.datasets[0].backgroundColor = data.crop_types.map(item => item.crop_color_code || '#' + Math.floor(Math.random()*16777215).toString(16));
        cropTypesChart.update();
    }

    // Update Livestock Information
    function updateLivestockInfo(data) {
        $('#total-livestock-blocks').text(data.total_blocks);

        // Update Livestock Chart
        livestockChart.data.labels = data.livestock_data.map(item => item.livestock_name);
        
        // Update datasets with color codes
        livestockChart.data.datasets[0].data = data.livestock_data.map(item => item.female_count);
        livestockChart.data.datasets[0].backgroundColor = data.livestock_data.map(item => 
            item.livestock_color_code ? adjustColor(item.livestock_color_code, 20) : '#dc3545'
        );
        
        livestockChart.data.datasets[1].data = data.livestock_data.map(item => item.male_count);
        livestockChart.data.datasets[1].backgroundColor = data.livestock_data.map(item => 
            item.livestock_color_code || '#007bff'
        );
        
        livestockChart.update();

        // Update Summary Table
        let summaryHtml = '';
        data.livestock_data.forEach(item => {
            const total = parseInt(item.male_count || 0) + parseInt(item.female_count || 0);
            summaryHtml += `
                <tr>
                    <td>${item.livestock_name}</td>
                    <td>${total.toLocaleString()}</td>
                </tr>
            `;
        });
        $('#livestock-summary-table tbody').html(summaryHtml);
    }

    // Helper function to adjust color brightness for female bars
    function adjustColor(hex, percent) {
        // Remove the # if present
        hex = hex.replace('#', '');
        
        // Convert to RGB
        let r = parseInt(hex.substring(0, 2), 16);
        let g = parseInt(hex.substring(2, 4), 16);
        let b = parseInt(hex.substring(4, 6), 16);
        
        // Make lighter
        r = Math.min(255, Math.floor(r * (1 + percent/100)));
        g = Math.min(255, Math.floor(g * (1 + percent/100)));
        b = Math.min(255, Math.floor(b * (1 + percent/100)));
        
        // Convert back to hex
        const rr = r.toString(16).padStart(2, '0');
        const gg = g.toString(16).padStart(2, '0');
        const bb = b.toString(16).padStart(2, '0');
        
        return `#${rr}${gg}${bb}`;
    }

    // Update Market Information
    function updateMarketInfo(cropsData, livestockData) {
        // Update Crops Market Table
        let cropsHtml = '';
        cropsData.forEach(item => {
            cropsHtml += `
                <tr>
                    <td>${item.crop_name}</td>
                    <td>${item.min_logistic_cost}</td>
                    <td>${item.max_logistic_cost}</td>
                    <td>${item.avg_logistic_cost}</td>
                    <td>${item.min_selling_price}</td>
                    <td>${item.max_selling_price}</td>
                    <td>${item.avg_selling_price}</td>
                </tr>
            `;
        });
        $('#crops-market-table tbody').html(cropsHtml);

        // Update Livestock Market Table
        let livestockHtml = '';
        livestockData.forEach(item => {
            livestockHtml += `
                <tr>
                    <td>${item.livestock_name}</td>
                    <td>${item.min_cost}</td>
                    <td>${item.max_cost}</td>
                    <td>${item.avg_cost}</td>
                    <td>${item.min_selling_price}</td>
                    <td>${item.max_selling_price}</td>
                    <td>${item.avg_selling_price}</td>
                </tr>
            `;
        });
        $('#livestock-market-table tbody').html(livestockHtml);
    }

    // Update Inputs Information
    function updateInputsInfo(fertilizers, pesticides) {
        // Update Fertilizers Table
        let fertilizersHtml = '';
        fertilizers.forEach(item => {
            fertilizersHtml += `
                <tr>
                    <td>${item.name}</td>
                    <td>${item.total_quantity}</td>
                    <td>${item.unit_of_measure}</td>
                </tr>
            `;
        });
        $('#fertilizers-table tbody').html(fertilizersHtml);

        // Update Pesticides Table
        let pesticidesHtml = '';
        pesticides.forEach(item => {
            pesticidesHtml += `
                <tr>
                    <td>${item.name}</td>
                    <td>${item.total_quantity}</td>
                    <td>${item.unit_of_measure}</td>
                </tr>
            `;
        });
        $('#pesticides-table tbody').html(pesticidesHtml);
    }

    // Location Dropdowns Change Events with error handling
    $('#province').change(function() {
        var provinceId = $(this).val();
        hideError();
        
        console.log('Province changed:', provinceId);
        
        if(provinceId && provinceId !== 'all') {
            showLoading();
            $.ajax({
                url: '<?= base_url('location/dashboard/get-districts') ?>',
                type: 'POST',
                dataType: 'json',
                data: {
                    province_id: provinceId,
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                success: function(data) {
                    hideLoading();
                    console.log('Districts data:', data);
                    
                    $('#district').prop('disabled', false).empty().append('<option value="">Select District</option>');
                    $.each(data, function(key, value) {
                        $('#district').append('<option value="'+ value.id +'">'+ value.name +'</option>');
                    });
                    $('#llg').prop('disabled', true).empty().append('<option value="">Select LLG</option>');
                    $('#ward').prop('disabled', true).empty().append('<option value="">Select Ward</option>');
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    console.error('AJAX Error:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    showError('Failed to load districts. Error: ' + error);
                }
            });
        } else {
            $('#district').prop('disabled', true).empty().append('<option value="">Select District</option>');
            $('#llg').prop('disabled', true).empty().append('<option value="">Select LLG</option>');
            $('#ward').prop('disabled', true).empty().append('<option value="">Select Ward</option>');
        }
        loadDashboardData();
    });

    $('#district').change(function() {
        var districtId = $(this).val();
        if(districtId) {
            $.ajax({
                url: '<?= base_url('location/dashboard/get-llgs') ?>',
                type: 'POST',
                data: {
                    district_id: districtId,
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                success: function(data) {
                    $('#llg').prop('disabled', false).empty().append('<option value="">Select LLG</option>');
                    $.each(data, function(key, value) {
                        $('#llg').append('<option value="'+ value.id +'">'+ value.name +'</option>');
                    });
                    $('#ward').prop('disabled', true).empty().append('<option value="">Select Ward</option>');
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.log('Response:', xhr.responseText);
                }
            });
        } else {
            $('#llg').prop('disabled', true).empty().append('<option value="">Select LLG</option>');
            $('#ward').prop('disabled', true).empty().append('<option value="">Select Ward</option>');
        }
        loadDashboardData();
    });

    $('#llg').change(function() {
        var llgId = $(this).val();
        if(llgId) {
            $.ajax({
                url: '<?= base_url('location/dashboard/get-wards') ?>',
                type: 'POST',
                data: {
                    llg_id: llgId,
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                },
                success: function(data) {
                    $('#ward').prop('disabled', false).empty().append('<option value="">Select Ward</option>');
                    $.each(data, function(key, value) {
                        $('#ward').append('<option value="'+ value.id +'">'+ value.name +'</option>');
                    });
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.log('Response:', xhr.responseText);
                }
            });
        } else {
            $('#ward').prop('disabled', true).empty().append('<option value="">Select Ward</option>');
        }
        loadDashboardData();
    });

    $('#ward').change(function() {
        loadDashboardData();
    });

    // Initial load
    loadDashboardData();
});
</script>

<?= $this->endSection() ?>
