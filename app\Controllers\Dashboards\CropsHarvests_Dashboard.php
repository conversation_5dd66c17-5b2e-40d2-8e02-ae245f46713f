<?php namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\CropsFarmHarvestDataModel;

class CropsHarvests_Dashboard extends BaseController
{
    protected $session;
    protected $harvestDataModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = \Config\Services::session();
        $this->harvestDataModel = new CropsFarmHarvestDataModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Crops Harvests Dashboard',
            'menu' => 'crops-harvests',
            'harvestData' => $this->harvestDataModel
                ->select('
                    crops_farm_blocks.block_code,
                    crops_farm_blocks.block_site,
                    farmer_information.given_name,
                    farmer_information.surname,
                    adx_crops.crop_name,
                    adx_province.name as province_name,
                    adx_district.name as district_name,
                    adx_llg.name as llg_name,
                    adx_ward.name as ward_name,
                    crops_farm_harvest_data.item,
                    crops_farm_harvest_data.unit,
                    crops_farm_harvest_data.unit_of_measure,
                    SUM(crops_farm_harvest_data.unit * crops_farm_harvest_data.quantity) as total_quantity,
                    MAX(crops_farm_harvest_data.harvest_date) as latest_harvest_date,
                    GROUP_CONCAT(DISTINCT crops_farm_harvest_data.remarks) as remarks
                ')
                ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_harvest_data.block_id')
                ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
                ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
                ->join('adx_province', 'adx_province.id = crops_farm_blocks.province_id')
                ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
                ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
                ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
                ->where('crops_farm_harvest_data.status', 'active')
                ->where('crops_farm_blocks.status', 'active')
                ->groupBy('
                    crops_farm_blocks.block_code,
                    crops_farm_blocks.block_site,
                    farmer_information.given_name,
                    farmer_information.surname,
                    adx_crops.crop_name,
                    adx_province.name,
                    adx_district.name,
                    adx_llg.name,
                    adx_ward.name,
                    crops_farm_harvest_data.item,
                    crops_farm_harvest_data.unit,
                    crops_farm_harvest_data.unit_of_measure
                ')
                ->orderBy('latest_harvest_date', 'DESC')
                ->findAll()
        ];

        return view('dashboard_reports/dashboard_crops_harvests', $data);
    }
}
