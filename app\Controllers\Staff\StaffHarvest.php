<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmHarvestDataModel;
use App\Models\CropsFarmBlockModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsModel;
use App\Models\districtModel;
use App\Models\provinceModel;
use App\Models\llgModel;
use App\Models\wardModel;
use App\Models\usersModel;

class StaffHarvest extends BaseController
{
    private $models = [];
    protected $helpers = ['url', 'form', 'info'];

    public function __construct()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        foreach ($this->helpers as $helper) {
            helper($helper);
        }
    }

    protected function getModel($modelName)
    {
        if (!isset($this->models[$modelName])) {
            switch ($modelName) {
                case 'farmHarvestData':
                    $this->models[$modelName] = new CropsFarmHarvestDataModel();
                    break;
                case 'farmBlocks':
                    $this->models[$modelName] = new CropsFarmBlockModel();
                    break;
                case 'farmers':
                    $this->models[$modelName] = new FarmerInformationModel();
                    break;
                case 'crops':
                    $this->models[$modelName] = new CropsModel();
                    break;
                case 'districts':
                    $this->models[$modelName] = new districtModel();
                    break;
                case 'provinces':
                    $this->models[$modelName] = new provinceModel();
                    break;
                case 'llgs':
                    $this->models[$modelName] = new llgModel();
                    break;
                case 'wards':
                    $this->models[$modelName] = new wardModel();
                    break;
                case 'users':
                    $this->models[$modelName] = new usersModel();
                    break;
            }
        }
        return $this->models[$modelName];
    }

    // Helper methods to get specific models
    protected function getFarmHarvestDataModel() { return $this->getModel('farmHarvestData'); }
    protected function getFarmBlockModel() { return $this->getModel('farmBlocks'); }
    protected function getFarmersModel() { return $this->getModel('farmers'); }
    protected function getCropsModel() { return $this->getModel('crops'); }
    protected function getDistrictModel() { return $this->getModel('districts'); }
    protected function getProvinceModel() { return $this->getModel('provinces'); }
    protected function getLlgModel() { return $this->getModel('llgs'); }
    protected function getWardModel() { return $this->getModel('wards'); }
    protected function getUsersModel() { return $this->getModel('users'); }

    protected function verifyDistrictAccess($districtId)
    {
        return $districtId == session()->get('district_id');
    }

    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    /**
     * Validate numeric fields for harvest data
     * @param array $data The data array containing fields to validate
     * @throws \Exception If validation fails
     */
    private function validateNumericFields(array $data): void
    {
        $numericFields = [
            'unit' => 'Unit value',
            'quantity' => 'Quantity'
        ];

        foreach ($numericFields as $field => $label) {
            if (!isset($data[$field])) continue;

            if (!is_numeric($data[$field])) {
                throw new \Exception("{$label} must be a number.");
            }

            if ($data[$field] <= 0) {
                throw new \Exception("{$label} must be a positive number.");
            }
        }
    }

    public function harvest_data()
    {
        $district = $this->getDistrictModel()->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Harvest Data',
            'page_header' => 'Harvest Data',
            'farm_blocks' => $this->getFarmBlockModel()->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where([
                'crops_farm_blocks.district_id' => session()->get('district_id'),
                'crops_farm_blocks.status' => 'active'
            ])
            ->findAll(),
            'district_name' => $districtName
        ];

        return view('staff/farms/harvest_data', $data);
    }

    public function view_harvest_data($block_id)
    {
        try {
            // Fetch block with district access verification
            $block = $this->getFarmBlockModel()->where([
                'id' => $block_id,
                'district_id' => session()->get('district_id')
            ])->first();

            if (!$block) {
                return redirect()->back()->with('error', 'Block not found or access denied');
            }

            // Fetch crop information
            $crop = $this->getCropsModel()->find($block['crop_id']);
            if (!$crop) {
                return redirect()->back()->with('error', 'Crop information not found');
            }

            // Fetch harvest data with proper error handling
            $harvest_data = $this->getFarmHarvestDataModel()
                ->where('block_id', $block_id)
                ->where('status', 'active')
                ->orderBy('harvest_date', 'DESC')
                ->findAll();

            if ($harvest_data === null) {
                log_message('error', '[View Harvest Data] Database error fetching harvest data');
                throw new \Exception('Error retrieving harvest data');
            }

            // Fetch farmer information
            $farmer = $this->getFarmersModel()->find($block['farmer_id']);
            if (!$farmer) {
                log_message('error', '[View Harvest Data] Farmer not found: ' . $block['farmer_id']);
                return redirect()->back()->with('error', 'Farmer information not found');
            }

            // Get users list
            $users = $this->getUsersModel()
                ->where('org_id', session()->get('org_id'))
                ->findAll();

            $data = [
                'title' => 'Block Harvest Data',
                'page_header' => 'Block Harvest Data',
                'block' => $block,
                'harvest_data' => $harvest_data,
                'farmer' => $farmer,
                'province' => $this->getProvinceModel()->find($block['province_id']),
                'district' => $this->getDistrictModel()->find($block['district_id']),
                'llg' => $this->getLlgModel()->find($block['llg_id']),
                'ward' => $this->getWardModel()->find($block['ward_id']),
                'crop' => $crop,
                'users' => $users
            ];

            return view('staff/farms/view_harvest_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[View Harvest Data] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    public function add_harvest_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'block_id' => $block_id,
                'item' => $this->request->getPost('item'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'unit' => $this->request->getPost('unit'),
                'quantity' => $this->request->getPost('quantity'),
                'harvest_date' => $this->request->getPost('harvest_date'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['item', 'unit_of_measure', 'unit', 'quantity', 'harvest_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields with proper error messages
            $this->validateNumericFields($data);

            // Save the data
            $this->getFarmHarvestDataModel()->save($data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Harvest data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_harvest_data()
    {
        try {
            $id = $this->request->getPost('id');
            $harvest_data = $this->getFarmHarvestDataModel()->find($id);

            if (!$harvest_data) {
                throw new \Exception('Record not found');
            }

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $harvest_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'item' => $this->request->getPost('item'),
                'unit_of_measure' => $this->request->getPost('unit_of_measure'),
                'unit' => $this->request->getPost('unit'),
                'quantity' => $this->request->getPost('quantity'),
                'harvest_date' => $this->request->getPost('harvest_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['item', 'unit_of_measure', 'unit', 'quantity', 'harvest_date'];
            $this->validateInput($data, $required);

            // Validate numeric fields with proper error messages
            $this->validateNumericFields($data);

            // Update the record
            $this->getFarmHarvestDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Harvest data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_harvest_data($id)
    {
        try {
            $harvest_data = $this->getFarmHarvestDataModel()->find($id);

            if (!$harvest_data) {
                throw new \Exception('Record not found');
            }

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $harvest_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmHarvestDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Harvest Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
}
