<?php namespace App\Controllers;

use App\Models\GroupingsModel;

class Groups extends BaseController
{
    protected $groupingsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url','info']);
        $this->groupingsModel = new GroupingsModel();
        $this->session = session();
    }

    public function index()
    {
        $data = [
            'title' => 'Groups Management',
            'menu' => 'groups',
            'groups' => $this->groupingsModel->where('org_id', session('org_id'))->findAll()
        ];

        return view('admindash/groups', $data);
    }

    public function add_group()
    {
        $validation = \Config\Services::validation();
        
        $rules = [
            'name' => 'required|min_length[3]',
            'description' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('error', $validation->listErrors());
        }

        $data = [
            'org_id' => session('org_id'),
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'parent_id' => $this->request->getPost('parent_id') ?: NULL,
            'created_by' => session('name')
        ];

        try {
            $this->groupingsModel->insert($data);
            return redirect()->back()->with('success', 'Group added successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to add group');
        }
    }

    public function update_group()
    {
        $validation = \Config\Services::validation();
        
        $rules = [
            'name' => 'required|min_length[3]',
            'description' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('error', $validation->listErrors());
        }

        $id = $this->request->getPost('id');
        $data = [
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'parent_id' => $this->request->getPost('parent_id') ?: NULL,
            'updated_by' => session('name')
        ];

        try {
            $this->groupingsModel->update($id, $data);
            return redirect()->back()->with('success', 'Group updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update group');
        }
    }

    public function delete_group($id)
    {
        // Check if group has children
        if ($this->groupingsModel->hasChildren($id)) {
            return redirect()->back()->with('error', 'Cannot delete group with sub-groups. Please delete sub-groups first.');
        }

        try {
            $this->groupingsModel->delete($id);
            return redirect()->back()->with('success', 'Group deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete group');
        }
    }

    // API method to get group hierarchy for dropdowns/trees
    public function get_groups()
    {
        $groups = $this->groupingsModel->getGroupHierarchy($this->session->get('org_id'));
        return $this->response->setJSON($groups);
    }

    // Get group details for editing
    public function get_group($id)
    {
        $group = $this->groupingsModel->find($id);
        if ($group) {
            return $this->response->setJSON($group);
        }
        return $this->response->setJSON(['error' => 'Group not found'])->setStatusCode(404);
    }

    // Get group path (breadcrumb)
    public function get_group_path($id)
    {
        $path = $this->groupingsModel->getGroupPath($id);
        return $this->response->setJSON($path);
    }
} 