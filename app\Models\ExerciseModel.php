<?php

namespace App\Models;

use CodeIgniter\Model;

class ExerciseModel extends Model
{
    protected $table      = 'exercises';
    protected $primaryKey = 'id';
    
    protected $returnType     = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'org_id',
        'country_id',
        'province_id',
        'district_id',
        'title',
        'description',
        'date_from',
        'date_to',
        'officer_responsible_id',
        'status',
        'status_at',
        'status_by',
        'status_remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules = [
        'org_id'                 => 'required|integer',
        'country_id'             => 'required|integer',
        'province_id'            => 'required|integer',
        'district_id'            => 'required|integer',
        'title'                  => 'required|min_length[3]|max_length[255]',
        'date_from'              => 'required|valid_date',
        'date_to'                => 'required|valid_date',
        'officer_responsible_id' => 'required|integer',
        'status'                 => 'required|in_list[draft,active,submitted,approved,cancelled]'
    ];

    /**
     * Get active exercises
     *
     * @return array
     */
    public function getActive()
    {
        return $this->where('status', 'active')->findAll();
    }

    /**
     * Get exercises by status
     *
     * @param string $status
     * @return array
     */
    public function getByStatus($status)
    {
        return $this->where('status', $status)->findAll();
    }
} 