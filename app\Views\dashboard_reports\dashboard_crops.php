<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <!-- Total Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="totalBlocksCount">0</h3>
                        <p>Total Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <!-- Total Plants Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3 id="totalPlants">0</h3>
                        <p>Total Active Plants</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
            <!-- Total Area Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3 id="totalArea">0</h3>
                        <p>Total Area (Ha)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                </div>
            </div>
            <!-- Total Farmers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3 id="totalFarmers">0</h3>
                        <p>Total Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crops Data Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crops Data</h3>
                    </div>
                    <div class="card-body">
                        <!-- Location Filters -->
                        <div id="filterContainer" class="mb-3 d-flex flex-wrap gap-2">
                            <!-- Filters will be inserted here by JavaScript -->
                        </div>
                        <div class="table-responsive">
                            <table id="cropsDataTable" class="table table-bordered text-nowrap table-striped">
                                <thead>
                                    <tr>
                                        <th>Block Code</th>
                                        <th>Farmer</th>
                                        <th>Crop Type</th>
                                        <th>Crop Breed</th>
                                        <th>Province</th>
                                        <th>District</th>
                                        <th>LLG</th>
                                        <th>Ward</th>
                                        <th>Village</th>
                                        <th>Added Plants</th>
                                        <th>Added Area (Ha)</th>
                                        <th>Removed Plants</th>
                                        <th>Removed Area (Ha)</th>
                                        <th>Latest Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($crops_data as $row): ?>
                                    <tr style="cursor: pointer;" data-href="<?= base_url('dashboards/crops/view/' . $row['block_id']) ?>">
                                        <td><?= $row['block_code'] ?></td>
                                        <td><?= trim($row['given_name'] . ' ' . $row['surname']) ?></td>
                                        <td><?= $row['crop_name'] ?></td>
                                        <td><?= $row['breed'] ?? 'N/A' ?></td>
                                        <td><?= $row['province_name'] ?></td>
                                        <td><?= $row['district_name'] ?></td>
                                        <td><?= $row['llg_name'] ?></td>
                                        <td><?= $row['ward_name'] ?></td>
                                        <td><?= $row['block_site'] ?></td>
                                        <td><?= number_format($row['total_plants_added'] ?? 0) ?></td>
                                        <td><?= number_format($row['total_hectares_added'] ?? 0, 2) ?></td>
                                        <td><?= number_format($row['total_plants_removed'] ?? 0) ?></td>
                                        <td><?= number_format($row['total_hectares_removed'] ?? 0, 2) ?></td>
                                        <td><?= date('d/m/Y', strtotime($row['latest_action_date'])) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Distribution -->
        <div class="row">
            <!-- Crop Type Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Type Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropTypeChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Area Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Area Distribution by Province</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="areaDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Location -->
        <div class="row">
            <!-- Location Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Location Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="locationChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Plant Count Trends -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Plant Count by District</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="plantDistrictChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
class FilterableTable {
    constructor(tableId) {
        this.table = document.getElementById(tableId);
        this.tableId = tableId;
        this.dataTable = $(`#${tableId}`).DataTable();
        this.filterContainer = document.getElementById('filterContainer');
        this.filters = {
            crop_type: [],
            breed: [],
            province: [],
            district: [],
            llg: [],
            ward: []
        };
        this.init();

        // Add drawCallback after initialization
        this.dataTable.on('draw', () => {
            this.updateSummaryCards();
            this.initializeCharts();
            
            // Update dependent filters based on current selection
            const filterOrder = ['crop_type', 'breed', 'province', 'district', 'llg', 'ward'];
            filterOrder.forEach((filterId, index) => {
                if (index > 0 && this.filters[filterOrder[index - 1]].length > 0) {
                    this.updateFilterDropdown(filterId);
                }
            });
        });
    }

    init() {
        this.createLocationFilters();
        this.updateSummaryCards();
        this.initializeCharts();
    }

    createLocationFilters() {
        const locations = [
            { id: 'crop_type', label: 'Crop Type', column: 2 },
            { id: 'breed', label: 'Breed', column: 3 },
            { id: 'province', label: 'Province', column: 4 },
            { id: 'district', label: 'District', column: 5 },
            { id: 'llg', label: 'LLG', column: 6 },
            { id: 'ward', label: 'Ward', column: 7 }
        ];

        locations.forEach(location => {
            const filterGroup = this.createFilterDropdown(location);
            this.filterContainer.appendChild(filterGroup);
        });
    }

    createFilterDropdown(location) {
        const container = document.createElement('div');
        container.className = 'dropdown mr-2';
        container.innerHTML = `
            <button class="btn btn-default dropdown-toggle" type="button" 
                    data-toggle="dropdown" data-filter="${location.id}" aria-haspopup="true" aria-expanded="false">
                ${location.label} <span class="badge badge-light"></span>
            </button>
            <div class="dropdown-menu p-2" style="min-width: 250px; max-height: 300px; overflow-y: auto;" data-boundary="viewport">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" 
                           id="selectAll_${location.id}" data-filter="${location.id}">
                    <label class="custom-control-label" for="selectAll_${location.id}">Select All</label>
                </div>
                <hr class="my-2">
                <div id="options_${location.id}"></div>
            </div>
        `;

        // Prevent dropdown from closing when clicking inside
        container.querySelector('.dropdown-menu').addEventListener('click', (e) => {
            e.stopPropagation();
        });

        this.populateFilterOptions(location, container);
        return container;
    }

    populateFilterOptions(location, container) {
        const optionsContainer = container.querySelector(`#options_${location.id}`);
        const uniqueValues = new Set();
        
        // Get unique values from the column
        const columnData = this.dataTable
            .rows()
            .data()
            .toArray()
            .map(row => row[location.column])
            .filter(value => value); // Filter out empty values
        
        // Add unique values to Set
        columnData.forEach(value => uniqueValues.add(value));

        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${location.id}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${location.id}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        this.setupFilterEvents(container, location.id);
    }

    setupFilterEvents(container, filterId) {
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        const checkboxes = container.querySelectorAll(`input[type="checkbox"][data-filter="${filterId}"]:not(#selectAll_${filterId})`);
        const button = container.querySelector('button');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (event) => {
                this.handleFilterChange(checkbox, filterId);
                this.updateBadgeCount(button, filterId);
                selectAll.checked = Array.from(checkboxes).every(cb => cb.checked);
                selectAll.indeterminate = Array.from(checkboxes).some(cb => cb.checked) && !selectAll.checked;
            });
        });

        selectAll.addEventListener('change', (event) => {
            checkboxes.forEach(cb => {
                if (!cb.disabled) {
                    cb.checked = selectAll.checked;
                    this.handleFilterChange(cb, filterId);
                }
            });
            this.updateBadgeCount(button, filterId);
            event.stopPropagation();
        });

        this.updateBadgeCount(button, filterId);
    }

    updateBadgeCount(button, filterId) {
        if (!button) return;
        
        const count = this.filters[filterId].length;
        const badge = button.querySelector('.badge');
        if (badge) {
            badge.textContent = count > 0 ? count : '';
            badge.style.display = count > 0 ? 'inline-block' : 'none';
        }
    }

    handleFilterChange(checkbox, filterId) {
        const value = checkbox.value;
        
        // Update filters array
        if (checkbox.checked) {
            if (!this.filters[filterId].includes(value)) {
                this.filters[filterId].push(value);
            }
        } else {
            this.filters[filterId] = this.filters[filterId].filter(v => v !== value);
        }

        // Reset dependent filters
        const filterOrder = ['crop_type', 'breed', 'province', 'district', 'llg', 'ward'];
        const currentIndex = filterOrder.indexOf(filterId);
        
        // Only reset filters that come after the current one
        if (currentIndex !== -1) {
            filterOrder.slice(currentIndex + 1).forEach(dependentFilterId => {
                this.filters[dependentFilterId] = [];
                this.resetFilterDropdown(dependentFilterId);
                this.updateFilterDropdown(dependentFilterId);
            });
        }

        // Apply filters to DataTable
        this.applyFilters();
    }

    resetFilterDropdown(filterId) {
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        if (!container) return;

        // Reset checkboxes
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = false;
            cb.disabled = false;
        });

        // Reset select all checkbox
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        if (selectAll) {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        }

        // Reset badge count
        const button = container.querySelector('button');
        this.updateBadgeCount(button, filterId);
    }

    updateFilterDropdown(filterId) {
        const filterOrder = ['crop_type', 'breed', 'province', 'district', 'llg', 'ward'];
        const columnIndices = { 
            crop_type: 2,
            breed: 3,
            province: 4,
            district: 5,
            llg: 6,
            ward: 7
        };
        
        // Get all visible rows that match current filters
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data().toArray();
        
        // Get the current filter's container
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        const optionsContainer = container.querySelector(`#options_${filterId}`);
        
        // Clear existing options
        optionsContainer.innerHTML = '';
        
        // Get unique values for this filter that match parent filters
        const uniqueValues = new Set();
        
        visibleRows.forEach(row => {
            // Check if this row matches all parent filters
            const matchesParentFilters = filterOrder.slice(0, filterOrder.indexOf(filterId)).every(parentId => {
                const parentValues = this.filters[parentId];
                return parentValues.length === 0 || parentValues.includes(row[columnIndices[parentId]]);
            });
            
            if (matchesParentFilters) {
                const value = row[columnIndices[filterId]];
            if (value) uniqueValues.add(value);
            }
        });

        // Create new options
        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${filterId}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${filterId}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        // Reattach event listeners
        this.setupFilterEvents(container, filterId);
    }

    applyFilters() {
        // Clear existing search functions
        $.fn.dataTable.ext.search = [];

        // Add filter function
        $.fn.dataTable.ext.search.push((settings, searchData) => {
            if (settings.nTable.id !== this.tableId) return true;

            return Object.entries(this.filters).every(([key, values]) => {
                if (values.length === 0) return true;
                
                const columnIndex = {
                    crop_type: 2,
                    breed: 3,
                    province: 4,
                    district: 5,
                    llg: 6,
                    ward: 7
                }[key];

                return values.includes(searchData[columnIndex]);
            });
        });

        // Redraw table and update UI
        this.dataTable.draw();
        this.updateSummaryCards();
        this.initializeCharts();
    }

    calculateSummaryData() {
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data();
        let totalPlants = 0;
        let totalArea = 0;
        const uniqueFarmers = new Set();
        const totalBlocks = visibleRows.length;

        visibleRows.each(row => {
            const addedPlants = parseInt(row[9].replace(/[^\d.-]/g, '')) || 0;
            const removedPlants = parseInt(row[11].replace(/[^\d.-]/g, '')) || 0;
            const addedArea = parseFloat(row[10].replace(/[^\d.-]/g, '')) || 0;
            const removedArea = parseFloat(row[12].replace(/[^\d.-]/g, '')) || 0;
            
            totalPlants += (addedPlants - removedPlants);
            totalArea += (addedArea - removedArea);
            uniqueFarmers.add(row[1]);
        });

        return {
            totalBlocks,
            totalPlants,
            totalArea,
            totalFarmers: uniqueFarmers.size
        };
    }

    updateSummaryCards() {
        const summaryData = this.calculateSummaryData();
        
        const animateCounter = (element, value, decimals = 0) => {
            if (!element) return;
            
            const duration = 1000;
            const startValue = parseFloat(element.textContent.replace(/[^\d.-]/g, '')) || 0;
            const startTime = performance.now();
            
            const update = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentValue = startValue + (value - startValue) * progress;
                element.textContent = currentValue.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            };
            
            requestAnimationFrame(update);
        };

        animateCounter(document.getElementById('totalBlocksCount'), summaryData.totalBlocks);
        animateCounter(document.getElementById('totalPlants'), summaryData.totalPlants);
        animateCounter(document.getElementById('totalArea'), summaryData.totalArea, 2);
        animateCounter(document.getElementById('totalFarmers'), summaryData.totalFarmers);
    }

    initializeCharts() {
        const visibleData = this.dataTable.rows({ search: 'applied' }).data();
        const chartData = {
            cropTypes: {},
            areas: {},
            locations: {},
            plantsByDistrict: {}
        };

        visibleData.each(row => {
            const cropType = row[2];
            const province = row[4];
            const district = row[5];
            const addedPlants = parseInt(row[9].replace(/[^\d.-]/g, '')) || 0;
            const removedPlants = parseInt(row[11].replace(/[^\d.-]/g, '')) || 0;
            const addedArea = parseFloat(row[10].replace(/[^\d.-]/g, '')) || 0;
            const removedArea = parseFloat(row[12].replace(/[^\d.-]/g, '')) || 0;

            // Crop type distribution
            chartData.cropTypes[cropType] = (chartData.cropTypes[cropType] || 0) + 1;

            // Area distribution by province
            chartData.areas[province] = (chartData.areas[province] || 0) + (addedArea - removedArea);

            // Location distribution
            chartData.locations[province] = (chartData.locations[province] || 0) + 1;

            // Plants by district
            chartData.plantsByDistrict[district] = (chartData.plantsByDistrict[district] || 0) + 
                (addedPlants - removedPlants);
        });

        this.updateChart('cropTypeChart', {
            type: 'pie',
            data: chartData.cropTypes,
            title: 'Crop Type Distribution'
        });

        this.updateChart('areaDistributionChart', {
            type: 'bar',
            data: chartData.areas,
            title: 'Area Distribution by Province (Ha)',
            yAxisLabel: 'Area (Ha)'
        });

        this.updateChart('locationChart', {
            type: 'pie',
            data: chartData.locations,
            title: 'Location Distribution'
        });

        this.updateChart('plantDistrictChart', {
            type: 'bar',
            data: chartData.plantsByDistrict,
            title: 'Plant Count by District',
            yAxisLabel: 'Number of Plants'
        });
    }

    updateChart(chartId, config) {
        const ctx = document.getElementById(chartId).getContext('2d');
        const chartInstance = Chart.getChart(ctx);
        if (chartInstance) {
            chartInstance.destroy();
        }

        const colors = [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 206, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
            'rgba(153, 102, 255, 0.2)',
            'rgba(255, 159, 64, 0.2)'
        ];
        
        const borderColors = colors.map(color => color.replace('0.2', '1'));

        new Chart(ctx, {
            type: config.type,
            data: {
                labels: Object.keys(config.data),
                datasets: [{
                    data: Object.values(config.data),
                    backgroundColor: config.type === 'pie' ? colors : colors[0],
                    borderColor: config.type === 'pie' ? borderColors : borderColors[0],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: config.type === 'pie' ? 'right' : 'top'
                    },
                    title: {
                        display: true,
                        text: config.title
                    }
                },
                scales: config.type === 'bar' ? {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: config.yAxisLabel
                        }
                    }
                } : undefined
            }
        });
    }
}

$(document).ready(function() {
    // Initialize DataTable with all options
    const dataTable = $('#cropsDataTable').DataTable({
        responsive: false,
        lengthChange: true,
        autoWidth: false,
        buttons: ["copy", "excel", "pdf", "print"],
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'All']],
        order: [[0, 'asc']]
    });

    // Move buttons to the correct container
    dataTable.buttons().container().appendTo('#cropsDataTable_wrapper .col-md-6:eq(0)');

    // Initialize FilterableTable
    new FilterableTable('cropsDataTable');

    // Add click handler for table rows
    $('#cropsDataTable tbody').on('click', 'tr', function() {
        window.location.href = $(this).data('href');
    });
});
</script>

<style>
.card-body {
    min-height: 400px;
}
.dropdown {
    display: inline-block;
    margin-right: 1rem;
    margin-bottom: 1rem;
}
.dropdown-menu {
    padding: 10px;
}
.badge {
    margin-left: 5px;
}
.custom-control {
    margin-bottom: 5px;
}
.custom-control-input:disabled ~ .custom-control-label {
    opacity: 0.5;
}
.table-responsive {
    min-height: 400px;
}
.dt-buttons {
    margin-bottom: 1rem;
}
</style>
<?= $this->endSection() ?> 