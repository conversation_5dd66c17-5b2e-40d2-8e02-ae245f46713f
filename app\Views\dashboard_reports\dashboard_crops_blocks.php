<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <!-- Total Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="totalBlocksCount">0</h3>
                        <p>Total Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <!-- Total Area Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3 id="totalArea">0</h3>
                        <p>Total Area (Ha)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                </div>
            </div>
            <!-- Total Farmers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3 id="totalFarmers">0</h3>
                        <p>Total Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            <!-- Total Crops Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3 id="totalCrops">0</h3>
                        <p>Total Crops</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blocks Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">All Crop Blocks</h3>
                    </div>
                    <div class="card-body">
                        <!-- Location Filters -->
                        <div id="filterContainer" class="mb-3 d-flex flex-wrap gap-2">
                            <!-- Filters will be inserted here by JavaScript -->
                        </div>
                        <div class="table-responsive">
                            <table id="blocksTable" class="table table-bordered text-nowrap table-striped">
                                <thead>
                                    <tr>
                                        <th>Block Code</th>
                                        <th>Farmer</th>
                                        <th>Crop Type</th>
                                        <th>Province</th>
                                        <th>District</th>
                                        <th>LLG</th>
                                        <th>Ward</th>
                                        <th>Area (Ha)</th>
                                        <th>Plants</th>
                                        <th>Planting Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($crop_blocks as $block): ?>
                                    <tr style="cursor: pointer;" data-href="<?= base_url('dashboards/crops-block/view/' . $block['id']) ?>">
                                        <td><?= $block['block_code'] ?></td>
                                        <td><?= trim($block['given_name'] . ' ' . $block['surname']) ?></td>
                                        <td><?= $block['crop_name'] ?></td>
                                        <td><?= $block['province_name'] ?></td>
                                        <td><?= $block['district_name'] ?></td>
                                        <td><?= $block['llg_name'] ?></td>
                                        <td><?= $block['ward_name'] ?></td>
                                        <td><?= isset($block['area']) ? number_format($block['area'], 2) : '0.00' ?></td>
                                        <td><?= isset($block['total_plants']) ? number_format($block['total_plants']) : '0' ?></td>
                                        <td><?= !empty($block['planting_date']) ? date('d/m/Y', strtotime($block['planting_date'])) : 'N/A' ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Provinces -->
        <div class="row">
            <!-- Crop Distribution by Province -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Distribution by Province</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropProvinceChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Area Distribution by Province -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Area Distribution by Province</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="areaProvinceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Districts -->
        <div class="row">
            <!-- Crop Distribution by District -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Distribution by District</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropDistrictChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Area Distribution by District -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Area Distribution by District</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="areaDistrictChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - LLGs -->
        <div class="row">
            <!-- Crop Distribution by LLG -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Distribution by LLG</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropLLGChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Area Distribution by LLG -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Area Distribution by LLG</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="areaLLGChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
class FilterableTable {
    constructor(tableId) {
        this.table = document.getElementById(tableId);
        this.tableId = tableId;
        this.dataTable = $(`#${tableId}`).DataTable();
        this.filterContainer = document.getElementById('filterContainer');
        this.filters = {
            crop: [],
            province: [],
            district: [],
            llg: [],
            ward: []
        };
        this.init();

        // Add drawCallback after initialization
        this.dataTable.on('draw', () => {
            this.updateSummaryCards();
            this.initializeCharts();
            
            // Update dependent filters based on current selection
            const filterOrder = ['crop', 'province', 'district', 'llg', 'ward'];
            filterOrder.forEach((filterId, index) => {
                if (index > 0 && this.filters[filterOrder[index - 1]].length > 0) {
                    this.updateFilterDropdown(filterId);
                }
            });
        });
    }

    init() {
        this.createLocationFilters();
        this.updateSummaryCards();
        this.initializeCharts();
    }

    createLocationFilters() {
        const locations = [
            { id: 'crop', label: 'Crop Type', column: 2 },
            { id: 'province', label: 'Province', column: 3 },
            { id: 'district', label: 'District', column: 4 },
            { id: 'llg', label: 'LLG', column: 5 },
            { id: 'ward', label: 'Ward', column: 6 }
        ];

        locations.forEach(location => {
            const filterGroup = this.createFilterDropdown(location);
            this.filterContainer.appendChild(filterGroup);
        });
    }

    createFilterDropdown(location) {
        const container = document.createElement('div');
        container.className = 'dropdown mr-2';
        container.innerHTML = `
            <button class="btn btn-default dropdown-toggle" type="button" 
                    data-toggle="dropdown" data-filter="${location.id}" aria-haspopup="true" aria-expanded="false">
                ${location.label} <span class="badge badge-light"></span>
            </button>
            <div class="dropdown-menu p-2" style="min-width: 250px; max-height: 300px; overflow-y: auto;" data-boundary="viewport">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" 
                           id="selectAll_${location.id}" data-filter="${location.id}">
                    <label class="custom-control-label" for="selectAll_${location.id}">Select All</label>
                </div>
                <hr class="my-2">
                <div id="options_${location.id}"></div>
            </div>
        `;

        // Prevent dropdown from closing when clicking inside
        container.querySelector('.dropdown-menu').addEventListener('click', (e) => {
            e.stopPropagation();
        });

        this.populateFilterOptions(location, container);
        return container;
    }

    populateFilterOptions(location, container) {
        const optionsContainer = container.querySelector(`#options_${location.id}`);
        const uniqueValues = new Set();
        
        // Get unique values from the column
        const columnData = this.dataTable
            .rows()
            .data()
            .toArray()
            .map(row => row[location.column])
            .filter(value => value); // Filter out empty values
        
        // Add unique values to Set
        columnData.forEach(value => uniqueValues.add(value));

        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${location.id}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${location.id}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        this.setupFilterEvents(container, location.id);
    }

    setupFilterEvents(container, filterId) {
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        const checkboxes = container.querySelectorAll(`input[type="checkbox"][data-filter="${filterId}"]:not(#selectAll_${filterId})`);
        const button = container.querySelector('button');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (event) => {
                this.handleFilterChange(checkbox, filterId);
                this.updateBadgeCount(button, filterId);
                selectAll.checked = Array.from(checkboxes).every(cb => cb.checked);
                selectAll.indeterminate = Array.from(checkboxes).some(cb => cb.checked) && !selectAll.checked;
            });
        });

        selectAll.addEventListener('change', (event) => {
            checkboxes.forEach(cb => {
                if (!cb.disabled) {
                    cb.checked = selectAll.checked;
                    this.handleFilterChange(cb, filterId);
                }
            });
            this.updateBadgeCount(button, filterId);
            event.stopPropagation();
        });

        this.updateBadgeCount(button, filterId);
    }

    updateBadgeCount(button, filterId) {
        if (!button) return;
        
        const count = this.filters[filterId].length;
        const badge = button.querySelector('.badge');
        if (badge) {
            badge.textContent = count > 0 ? count : '';
            badge.style.display = count > 0 ? 'inline-block' : 'none';
        }
    }

    handleFilterChange(checkbox, filterId) {
        const value = checkbox.value;
        
        // Update filters array
        if (checkbox.checked) {
            if (!this.filters[filterId].includes(value)) {
                this.filters[filterId].push(value);
            }
        } else {
            this.filters[filterId] = this.filters[filterId].filter(v => v !== value);
        }

        // Reset dependent filters
        const filterOrder = ['crop', 'province', 'district', 'llg', 'ward'];
        const currentIndex = filterOrder.indexOf(filterId);
        
        // Only reset filters that come after the current one
        if (currentIndex !== -1) {
            filterOrder.slice(currentIndex + 1).forEach(dependentFilterId => {
                this.filters[dependentFilterId] = [];
                this.resetFilterDropdown(dependentFilterId);
                this.updateFilterDropdown(dependentFilterId);
            });
        }

        // Apply filters to DataTable
        this.applyFilters();
    }

    resetFilterDropdown(filterId) {
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        if (!container) return;

        // Reset checkboxes
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = false;
            cb.disabled = false;
        });

        // Reset select all checkbox
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        if (selectAll) {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        }

        // Reset badge count
        const button = container.querySelector('button');
        this.updateBadgeCount(button, filterId);
    }

    updateFilterDropdown(filterId) {
        const filterOrder = ['crop', 'province', 'district', 'llg', 'ward'];
        const columnIndices = { 
            crop: 2,
            province: 3, 
            district: 4, 
            llg: 5,
            ward: 6
        };
        
        // Get all visible rows that match current filters
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data().toArray();
        
        // Get the current filter's container
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        const optionsContainer = container.querySelector(`#options_${filterId}`);
        
        // Clear existing options
        optionsContainer.innerHTML = '';
        
        // Get unique values for this filter that match parent filters
        const uniqueValues = new Set();
        
        visibleRows.forEach(row => {
            // Check if this row matches all parent filters
            const matchesParentFilters = filterOrder.slice(0, filterOrder.indexOf(filterId)).every(parentId => {
                const parentValues = this.filters[parentId];
                return parentValues.length === 0 || parentValues.includes(row[columnIndices[parentId]]);
            });
            
            if (matchesParentFilters) {
                const value = row[columnIndices[filterId]];
                if (value) uniqueValues.add(value);
            }
        });

        // Create new options
        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${filterId}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${filterId}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        // Reattach event listeners
        this.setupFilterEvents(container, filterId);
    }

    applyFilters() {
        // Clear existing search functions
        $.fn.dataTable.ext.search = [];

        // Add filter function
        $.fn.dataTable.ext.search.push((settings, searchData) => {
            if (settings.nTable.id !== this.tableId) return true;

            return Object.entries(this.filters).every(([key, values]) => {
                if (values.length === 0) return true;
                
                const columnIndex = {
                    crop: 2,
                    province: 3,
                    district: 4,
                    llg: 5,
                    ward: 6
                }[key];

                return values.includes(searchData[columnIndex]);
            });
        });

        // Redraw table and update UI
        this.dataTable.draw();
        this.updateSummaryCards();
        this.initializeCharts();
    }

    calculateSummaryData() {
        // Get all visible rows after filtering
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data();
        
        // Initialize counters
        let totalArea = 0;
        const uniqueFarmers = new Set();
        const uniqueCrops = new Set();
        const totalBlocks = visibleRows.length; // Direct count of visible rows

        // Calculate other summaries
        visibleRows.each(row => {
            const area = parseFloat(row[7].replace(/[^\d.-]/g, '')) || 0;
            const farmer = row[1]; // Farmer name column
            const crop = row[2];   // Crop type column
            
            totalArea += area;
            if (farmer) uniqueFarmers.add(farmer);
            if (crop) uniqueCrops.add(crop);
        });

        return {
            totalBlocks: totalBlocks,
            totalArea: totalArea,
            totalFarmers: uniqueFarmers.size,
            totalCrops: uniqueCrops.size
        };
    }

    updateSummaryCards() {
        const summaryData = this.calculateSummaryData();
        
        // Animate the counter updates with proper formatting
        const animateCounter = (element, value, decimals = 0) => {
            if (!element) return; // Guard against null elements
            
            const duration = 1000;
            const startValue = parseFloat(element.textContent.replace(/[^\d.-]/g, '')) || 0;
            const startTime = performance.now();
            
            const update = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentValue = startValue + (value - startValue) * progress;
                const formattedValue = currentValue.toFixed(decimals);
                element.textContent = parseInt(formattedValue).toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            };
            
            requestAnimationFrame(update);
        };

        // Update each summary card with proper error checking
        const totalBlocksElement = document.getElementById('totalBlocksCount');
        const totalAreaElement = document.getElementById('totalArea');
        const totalFarmersElement = document.getElementById('totalFarmers');
        const totalCropsElement = document.getElementById('totalCrops');

        if (totalBlocksElement) animateCounter(totalBlocksElement, summaryData.totalBlocks);
        if (totalAreaElement) animateCounter(totalAreaElement, summaryData.totalArea, 2);
        if (totalFarmersElement) animateCounter(totalFarmersElement, summaryData.totalFarmers);
        if (totalCropsElement) animateCounter(totalCropsElement, summaryData.totalCrops);
    }

    initializeCharts() {
        const visibleData = this.dataTable.rows({ search: 'applied' }).data();
        const distributions = {
            district: { counts: {}, areas: {} },
            province: { counts: {}, areas: {} },
            llg: { counts: {}, areas: {} }
        };

        visibleData.each(row => {
            const province = row[3];
            const district = row[4];
            const llg = row[5];
            const area = parseFloat(row[7].replace(/[^\d.-]/g, '')) || 0;
            
            // Update province distributions
            if (province) {
                distributions.province.counts[province] = (distributions.province.counts[province] || 0) + 1;
                distributions.province.areas[province] = (distributions.province.areas[province] || 0) + area;
            }
            
            // Update district distributions
            if (district) {
                distributions.district.counts[district] = (distributions.district.counts[district] || 0) + 1;
                distributions.district.areas[district] = (distributions.district.areas[district] || 0) + area;
            }
            
            // Update LLG distributions
            if (llg) {
                distributions.llg.counts[llg] = (distributions.llg.counts[llg] || 0) + 1;
                distributions.llg.areas[llg] = (distributions.llg.areas[llg] || 0) + area;
            }
        });

        // Update province charts
        this.updateChart('cropProvinceChart', {
            labels: Object.keys(distributions.province.counts),
            data: Object.values(distributions.province.counts),
            type: 'bar',
            title: 'Crop Blocks Distribution by Province',
            yAxisLabel: 'Number of Blocks'
        });

        this.updateChart('areaProvinceChart', {
            labels: Object.keys(distributions.province.areas),
            data: Object.values(distributions.province.areas),
            type: 'pie',
            title: 'Area Distribution by Province (Ha)'
        });

        // Update district charts
        this.updateChart('cropDistrictChart', {
            labels: Object.keys(distributions.district.counts),
            data: Object.values(distributions.district.counts),
            type: 'bar',
            title: 'Crop Blocks Distribution by District',
            yAxisLabel: 'Number of Blocks'
        });

        this.updateChart('areaDistrictChart', {
            labels: Object.keys(distributions.district.areas),
            data: Object.values(distributions.district.areas),
            type: 'pie',
            title: 'Area Distribution by District (Ha)'
        });

        // Update LLG charts
        this.updateChart('cropLLGChart', {
            labels: Object.keys(distributions.llg.counts),
            data: Object.values(distributions.llg.counts),
            type: 'bar',
            title: 'Crop Blocks Distribution by LLG',
            yAxisLabel: 'Number of Blocks'
        });

        this.updateChart('areaLLGChart', {
            labels: Object.keys(distributions.llg.areas),
            data: Object.values(distributions.llg.areas),
            type: 'pie',
            title: 'Area Distribution by LLG (Ha)'
        });
    }

    updateChart(chartId, config) {
        const ctx = document.getElementById(chartId).getContext('2d');
        const chartInstance = Chart.getChart(ctx);
        if (chartInstance) {
            chartInstance.destroy();
        }

        const colors = [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 206, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
            'rgba(153, 102, 255, 0.2)'
        ];
        
        const borderColors = colors.map(color => color.replace('0.2', '1'));

        const datasets = [{
            label: config.type === 'bar' ? 'Number of Blocks' : 'Area (Ha)',
            data: config.data,
            backgroundColor: config.type === 'bar' ? 'rgba(75, 192, 192, 0.2)' : colors,
            borderColor: config.type === 'bar' ? 'rgba(75, 192, 192, 1)' : borderColors,
            borderWidth: 1
        }];

        // Add secondary dataset if provided
        if (config.secondaryData) {
            datasets.push({
                label: config.secondaryData.label,
                data: config.secondaryData.data,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1,
                type: 'line',
                yAxisID: 'y1'
            });
        }

        const chartConfig = {
            type: config.type === 'horizontalBar' ? 'bar' : config.type,
            data: {
                labels: config.labels,
                datasets: datasets
            },
            options: {
                indexAxis: config.type === 'horizontalBar' ? 'y' : 'x',
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                },
                scales: config.type !== 'pie' ? {
                    [config.type === 'horizontalBar' ? 'x' : 'y']: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: config.type === 'horizontalBar' ? config.xAxisLabel : config.yAxisLabel || ''
                        }
                    },
                    ...(config.secondaryData ? {
                        y1: {
                            beginAtZero: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false
                            },
                            title: {
                                display: true,
                                text: 'Area (Ha)'
                            }
                        }
                    } : {})
                } : undefined,
                plugins: {
                    legend: {
                        position: config.type === 'pie' ? 'right' : 'top'
                    },
                    title: {
                        display: true,
                        text: config.title || ''
                    },
                    tooltip: config.type === 'pie' ? {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                return `${label}: ${value.toFixed(2)} Ha`;
                            }
                        }
                    } : undefined
                }
            }
        };

        new Chart(ctx, chartConfig);
    }
}

$(document).ready(function() {
    // Initialize DataTable with all options
    const dataTable = $('#blocksTable').DataTable({
        responsive: false,
        lengthChange: true,
        autoWidth: false,
        buttons: ["copy", "excel", "pdf", "print"],
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'All']],
        order: [[0, 'asc']],
        search: { smart: true },
        initComplete: function() {
            // Initialize FilterableTable after DataTable
            new FilterableTable('blocksTable');
        }
    });

    // Move buttons to the correct container
    dataTable.buttons().container().appendTo('#blocksTable_wrapper .col-md-6:eq(0)');

    // Add click handler for table rows
    $('#blocksTable tbody').on('click', 'tr', function() {
        window.location.href = $(this).data('href');
    });
});
</script>

<style>
.card-body {
    min-height: 400px;
}
.dropdown {
    display: inline-block;
    margin-right: 1rem;
    margin-bottom: 1rem;
}
.dropdown-menu {
    padding: 10px;
}
.badge {
    margin-left: 5px;
}
.custom-control {
    margin-bottom: 5px;
}
.custom-control-input:disabled ~ .custom-control-label {
    opacity: 0.5;
}
</style>
<?= $this->endSection() ?>
