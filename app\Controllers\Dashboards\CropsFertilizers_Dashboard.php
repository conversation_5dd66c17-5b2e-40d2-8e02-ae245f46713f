<?php namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\FertilizersModel;
use App\Models\CropsFarmFertilizerDataModel;
use App\Models\FarmerInformationModel;

class CropsFertilizers_Dashboard extends BaseController
{
    protected $session;
    protected $validation;
    protected $fertilizerModel;
    protected $cropFertilizerModel;
    protected $farmerModel;

    public function __construct()
    {
        $this->session = \Config\Services::session();
        $this->validation = \Config\Services::validation();
        helper(['info', 'form', 'url', 'text']);
        
        $this->fertilizerModel = new FertilizersModel();
        $this->cropFertilizerModel = new CropsFarmFertilizerDataModel();
        $this->farmerModel = new FarmerInformationModel();
    }

    public function index()
    {
        try {
            $fertilizerData = $this->cropFertilizerModel->getFertilizerReportData();

            if (empty($fertilizerData)) {
                log_message('warning', 'No fertilizer data returned from model');
            }

            $data = [
                'title' => 'Crops Fertilizers Data',
                'menu' => 'crops-fertilizers',
                'fertilizerData' => $fertilizerData
            ];

            return view('dashboard_reports/dashboard_crops_fertilizers', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error in CropsFertilizers_Dashboard::index: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());

            $data = [
                'title' => 'Crops Fertilizers Data',
                'menu' => 'crops-fertilizers',
                'fertilizerData' => [],
                'error' => 'An error occurred while loading the data. Please try again later.'
            ];

            return view('dashboard_reports/dashboard_crops_fertilizers', $data);
        }
    }
}
