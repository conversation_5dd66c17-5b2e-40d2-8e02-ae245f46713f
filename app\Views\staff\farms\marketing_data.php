<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped text-nowrap" id="farmersTable">
                <thead>
                    <tr>
                        <th>Farmer Code</th>
                        <th>Name</th>
                        <th>Gender</th>
                        <th>Location</th>
                        <th>Contact</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($farmers as $farmer): ?>
                        <tr>
                            <td><?= esc($farmer['farmer_code']) ?></td>
                            <td><?= esc($farmer['given_name']) ?> <?= esc($farmer['surname']) ?></td>
                            <td><?= esc($farmer['gender']) ?></td>
                            <td>
                                <?= esc($farmer['village']) ?>
                            </td>
                            <td>
                                <?= esc($farmer['phone']) ?: 'N/A' ?>
                                <?php if($farmer['email']): ?>
                                    <br>
                                    <?= esc($farmer['email']) ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?= base_url('staff/farms/view-market-data/' . $farmer['id']) ?>" 
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> View Marketing Data
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#farmersTable').DataTable({
        responsive: false,
        processing: true,
        order: [[0, 'desc']],
        columnDefs: [
            { 
                targets: -1, // Last column (Actions)
                orderable: false 
            }
        ]
    });
});
</script>
<?= $this->endSection() ?> 