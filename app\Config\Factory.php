<?php

namespace Config;

use CodeIgniter\Config\Factory as BaseFactory;

/**
 * Factories Configuration file.
 *
 * Provides overriding directives for how
 * Factories should handle discovery and
 * instantiation of specific components.
 */
class Factory extends BaseFactory
{
    /**
     * Specifies that Models should always favor child
     * classes to allow easy extension of module Models.
     *
     * @var array
     */
    public $models = [
        'preferApp' => true,
    ];
    
    /**
     * Optimize config loading
     */
    public $config = [
        'getShared' => true,
        'preferApp' => true,
    ];
}
