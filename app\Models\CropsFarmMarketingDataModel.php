<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmMarketingDataModel extends Model
{
    protected $table = 'crops_farm_marketing_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        // Primary identifiers
        'exercise_id',
        'farmer_id',
        'block_id',
        'crop_id',

        // Location hierarchy
        'country_id',
        'province_id',
        'district_id',
        'llg_id',

        // Market details
        'market_date',
        'market_stage',
        'buyer_id',
        'selling_location',

        // Item details
        'product',
        'product_type',
        'description',

        // Quantity and pricing
        'unit_of_measure',
        'unit',
        'quantity',
        'market_price_per_unit',
        'total_freight_cost',

        // Additional info
        'remarks',

        // Metadata
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $validationRules = [];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Get market data summary by crop
    public function getMarketSummaryByCrop($conditions = [])
    {
        $builder = $this->builder();
        $builder->select('
            crops_farm_marketing_data.crop_id,
            adx_crops.crop_name,
            adx_crops.crop_color_code,
            MIN(crops_farm_marketing_data.market_price_per_unit) as min_price,
            MAX(crops_farm_marketing_data.market_price_per_unit) as max_price,
            AVG(crops_farm_marketing_data.market_price_per_unit) as avg_price,
            MIN(crops_farm_marketing_data.total_freight_cost) as min_freight,
            MAX(crops_farm_marketing_data.total_freight_cost) as max_freight,
            AVG(crops_farm_marketing_data.total_freight_cost) as avg_freight,
            SUM(crops_farm_marketing_data.quantity) as total_quantity,
            crops_farm_marketing_data.unit_of_measure,
            COUNT(DISTINCT crops_farm_marketing_data.buyer_id) as total_buyers,
            COUNT(*) as total_transactions,
            GROUP_CONCAT(DISTINCT crops_farm_marketing_data.selling_location) as selling_locations,
            adx_country.name as country_name,
            adx_province.name as province_name,
            adx_district.name as district_name,
            adx_llg.name as llg_name
        ')
        ->join('adx_crops', 'adx_crops.id = crops_farm_marketing_data.crop_id')
        ->join('farmer_information', 'farmer_information.id = crops_farm_marketing_data.farmer_id')
        ->join('adx_country', 'adx_country.id = crops_farm_marketing_data.country_id')
        ->join('adx_province', 'adx_province.id = crops_farm_marketing_data.province_id')
        ->join('adx_district', 'adx_district.id = crops_farm_marketing_data.district_id')
        ->join('adx_llg', 'adx_llg.id = crops_farm_marketing_data.llg_id')
        ->where('crops_farm_marketing_data.status', 'active')
        ->where('farmer_information.status', 'active');

        // Apply conditions if any
        if (!empty($conditions)) {
            $builder->where($conditions);
        }

        return $builder->groupBy('crops_farm_marketing_data.crop_id')
            ->orderBy('adx_crops.crop_name', 'ASC')
            ->get()
            ->getResultArray();
    }

    // Get market data summary by buyer
    public function getMarketSummaryByBuyer($conditions = [])
    {
        $builder = $this->builder();
        $builder->select('
            crop_buyers.id as buyer_id,
            crop_buyers.name as buyer_name,
            COUNT(*) as transaction_count,
            SUM(crops_farm_marketing_data.quantity) as total_quantity,
            crops_farm_marketing_data.unit_of_measure,
            SUM(crops_farm_marketing_data.market_price_per_unit * crops_farm_marketing_data.quantity) as total_value,
            AVG(crops_farm_marketing_data.market_price_per_unit) as avg_price,
            SUM(crops_farm_marketing_data.total_freight_cost) as total_freight_cost,
            GROUP_CONCAT(DISTINCT crops_farm_marketing_data.selling_location) as selling_locations,
            adx_country.name as country_name,
            adx_province.name as province_name,
            adx_district.name as district_name,
            adx_llg.name as llg_name
        ')
        ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id')
        ->join('farmer_information', 'farmer_information.id = crops_farm_marketing_data.farmer_id')
        ->join('adx_country', 'adx_country.id = crops_farm_marketing_data.country_id')
        ->join('adx_province', 'adx_province.id = crops_farm_marketing_data.province_id')
        ->join('adx_district', 'adx_district.id = crops_farm_marketing_data.district_id')
        ->join('adx_llg', 'adx_llg.id = crops_farm_marketing_data.llg_id')
        ->where('crops_farm_marketing_data.status', 'active')
        ->where('farmer_information.status', 'active');

        // Apply conditions if any
        if (!empty($conditions)) {
            $builder->where($conditions);
        }

        return $builder->groupBy('crop_buyers.id')
            ->orderBy('total_value', 'DESC')
            ->get()
            ->getResultArray();
    }

    // Get market data report
    public function getMarketingReportData($conditions = [])
    {
        $builder = $this->builder();
        $builder->select('
            crops_farm_marketing_data.*,
            farmer_information.given_name,
            farmer_information.surname,
            farmer_information.farmer_code,
            adx_crops.crop_name,
            adx_crops.crop_color_code,
            IFNULL(adx_country.name, "Unknown") as country_name,
            IFNULL(adx_province.name, "Unknown") as province_name,
            IFNULL(adx_district.name, "Unknown") as district_name,
            IFNULL(adx_llg.name, "Unknown") as llg_name,
            crop_buyers.name as buyer_name
        ')
        ->join('farmer_information', 'farmer_information.id = crops_farm_marketing_data.farmer_id')
        ->join('adx_crops', 'adx_crops.id = crops_farm_marketing_data.crop_id')
        ->join('adx_country', 'adx_country.id = crops_farm_marketing_data.country_id AND crops_farm_marketing_data.country_id > 0', 'left')
        ->join('adx_province', 'adx_province.id = crops_farm_marketing_data.province_id AND crops_farm_marketing_data.province_id > 0', 'left')
        ->join('adx_district', 'adx_district.id = crops_farm_marketing_data.district_id AND crops_farm_marketing_data.district_id > 0', 'left')
        ->join('adx_llg', 'adx_llg.id = crops_farm_marketing_data.llg_id AND crops_farm_marketing_data.llg_id > 0', 'left')
        ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id', 'left')
        ->where('crops_farm_marketing_data.status', 'active')
        ->where('farmer_information.status', 'active');

        // Apply conditions if any
        if (!empty($conditions)) {
            $builder->where($conditions);
        }

        // Log the generated SQL query for debugging
        log_message('debug', 'Marketing Report SQL: ' . $builder->getCompiledSelect(false));

        return $builder->orderBy('crops_farm_marketing_data.market_date', 'DESC')
            ->get()
            ->getResultArray();
    }
}
