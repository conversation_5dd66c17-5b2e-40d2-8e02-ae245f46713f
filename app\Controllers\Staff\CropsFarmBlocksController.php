<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Helpers\DummyDataHelper;

class CropsFarmBlocksController extends BaseController
{
    public function __construct()
    {
        helper(['form', 'url']);
    }

    /**
     * Display a listing of crop farm blocks
     * GET /staff/crops-farm-blocks
     */
    public function index()
    {
        $cropBlocks = DummyDataHelper::getCropFarmBlocks();

        $data = [
            'title' => 'Crops Farm Blocks',
            'page_header' => 'Crops Farm Blocks Management',
            'blocks' => $cropBlocks,
            'total_blocks' => count($cropBlocks),
            'crops' => DummyDataHelper::getCrops()
        ];

        return view('staff/crops_farm_blocks/index', $data);
    }

    /**
     * Show the form for creating a new crop farm block
     * GET /staff/crops-farm-blocks/create
     */
    public function create()
    {
        $data = [
            'title' => 'Add New Crop Farm Block',
            'page_header' => 'Add New Crop Farm Block',
            'farmers' => DummyDataHelper::getFarmers(),
            'crops' => DummyDataHelper::getCrops(),
            'provinces' => DummyDataHelper::getProvinces(),
            'districts' => DummyDataHelper::getDistricts(),
            'exercises' => [
                ['id' => 1, 'title' => 'Western Province Agricultural Survey 2024 Q1'],
                ['id' => 2, 'title' => 'Southern Highlands Coffee Assessment 2024'],
                ['id' => 3, 'title' => 'Kiunga District Baseline Study 2024']
            ]
        ];

        return view('staff/crops_farm_blocks/create', $data);
    }

    /**
     * Store a newly created crop farm block
     * POST /staff/crops-farm-blocks
     */
    public function store()
    {
        // Simulate successful creation
        session()->setFlashdata('success', 'Crop farm block created successfully! (Demo Mode - No data saved)');
        return redirect()->to('staff/crops-farm-blocks');
    }

    /**
     * Display the specified crop farm block
     * GET /staff/crops-farm-blocks/{id}
     */
    public function show($id)
    {
        $cropBlocks = DummyDataHelper::getCropFarmBlocks();
        $block = null;
        
        foreach ($cropBlocks as $b) {
            if ($b['id'] == $id) {
                $block = $b;
                break;
            }
        }
        
        if (!$block) {
            session()->setFlashdata('error', 'Crop farm block not found');
            return redirect()->to('staff/crops-farm-blocks');
        }

        // Generate dummy agricultural data for this block
        $cropsData = [
            ['id' => 1, 'crop_name' => $block['crop_name'], 'action_type' => 'add', 'number_of_plants' => 500, 'hectares' => 2.5, 'action_date' => '2024-01-15'],
            ['id' => 2, 'crop_name' => $block['crop_name'], 'action_type' => 'add', 'number_of_plants' => 200, 'hectares' => 1.0, 'action_date' => '2024-02-10'],
        ];

        $fertilizerData = [
            ['id' => 1, 'fertilizer_name' => 'NPK 15-15-15', 'quantity' => 50, 'unit' => 'kg', 'action_date' => '2024-01-20'],
            ['id' => 2, 'fertilizer_name' => 'Urea', 'quantity' => 25, 'unit' => 'kg', 'action_date' => '2024-02-15'],
        ];

        $harvestData = [
            ['id' => 1, 'item' => $block['crop_name'], 'quantity' => 1200, 'unit' => 'kg', 'harvest_date' => '2024-03-15'],
        ];

        $data = [
            'title' => 'Crop Farm Block - ' . $block['block_code'],
            'page_header' => 'Crop Farm Block Details',
            'block' => $block,
            'crops_data' => $cropsData,
            'fertilizer_data' => $fertilizerData,
            'harvest_data' => $harvestData
        ];

        return view('staff/crops_farm_blocks/show', $data);
    }

    /**
     * Show the form for editing the specified crop farm block
     * GET /staff/crops-farm-blocks/{id}/edit
     */
    public function edit($id)
    {
        $cropBlocks = DummyDataHelper::getCropFarmBlocks();
        $block = null;
        
        foreach ($cropBlocks as $b) {
            if ($b['id'] == $id) {
                $block = $b;
                break;
            }
        }
        
        if (!$block) {
            session()->setFlashdata('error', 'Crop farm block not found');
            return redirect()->to('staff/crops-farm-blocks');
        }

        $data = [
            'title' => 'Edit Crop Farm Block - ' . $block['block_code'],
            'page_header' => 'Edit Crop Farm Block',
            'block' => $block,
            'farmers' => DummyDataHelper::getFarmers(),
            'crops' => DummyDataHelper::getCrops(),
            'provinces' => DummyDataHelper::getProvinces(),
            'districts' => DummyDataHelper::getDistricts()
        ];

        return view('staff/crops_farm_blocks/edit', $data);
    }

    /**
     * Update the specified crop farm block
     * PUT /staff/crops-farm-blocks/{id}
     */
    public function update($id)
    {
        // Simulate successful update
        session()->setFlashdata('success', 'Crop farm block updated successfully! (Demo Mode - No data saved)');
        return redirect()->to('staff/crops-farm-blocks/' . $id);
    }

    /**
     * Remove the specified crop farm block
     * DELETE /staff/crops-farm-blocks/{id}
     */
    public function destroy($id)
    {
        // Simulate successful deletion
        session()->setFlashdata('success', 'Crop farm block deleted successfully! (Demo Mode - No data actually deleted)');
        return redirect()->to('staff/crops-farm-blocks');
    }
}