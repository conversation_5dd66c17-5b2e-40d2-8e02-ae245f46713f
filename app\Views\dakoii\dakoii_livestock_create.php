<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">Add New Livestock</h2>
            <p class="text-muted mb-0">Create a new livestock type for the system</p>
        </div>
        <a href="<?= base_url('dakoii/data/livestock') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Livestock
        </a>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Validation Errors -->
    <?php if (session()->get('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->get('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus"></i> Livestock Information
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open_multipart('dakoii/data/livestock/store') ?>
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Livestock Name <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control <?= session()->get('errors.name') ? 'is-invalid' : '' ?>" 
                                       id="name" 
                                       name="name" 
                                       value="<?= old('name') ?>" 
                                       required>
                                <?php if (session()->get('errors.name')): ?>
                                    <div class="invalid-feedback">
                                        <?= session()->get('errors.name') ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color_code" class="form-label">Color Code</label>
                                <div class="input-group">
                                    <input type="color" 
                                           class="form-control form-control-color <?= session()->get('errors.color_code') ? 'is-invalid' : '' ?>" 
                                           id="color_code" 
                                           name="color_code" 
                                           value="<?= old('color_code', '#6f42c1') ?>"
                                           style="width: 60px;">
                                    <input type="text" 
                                           class="form-control" 
                                           id="color_text" 
                                           value="<?= old('color_code', '#6f42c1') ?>" 
                                           readonly>
                                </div>
                                <div class="form-text">Choose a color to represent this livestock</div>
                                <?php if (session()->get('errors.color_code')): ?>
                                    <div class="invalid-feedback">
                                        <?= session()->get('errors.color_code') ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="icon" class="form-label">Livestock Icon</label>
                        <input type="file" 
                               class="form-control <?= session()->get('errors.icon') ? 'is-invalid' : '' ?>" 
                               id="icon" 
                               name="icon" 
                               accept="image/*">
                        <div class="form-text">Upload an icon for this livestock (optional, max 2MB)</div>
                        <?php if (session()->get('errors.icon')): ?>
                            <div class="invalid-feedback">
                                <?= session()->get('errors.icon') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" 
                                  id="remarks" 
                                  name="remarks" 
                                  rows="3" 
                                  placeholder="Additional notes about this livestock..."><?= old('remarks') ?></textarea>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('dakoii/data/livestock') ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save"></i> Save Livestock
                        </button>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Color picker sync
    const colorPicker = document.getElementById('color_code');
    const colorText = document.getElementById('color_text');

    colorPicker.addEventListener('input', function() {
        colorText.value = this.value;
    });
});
</script>

<?= $this->endSection() ?>
