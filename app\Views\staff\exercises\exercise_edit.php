<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Back Button and Title -->
<div class="row mb-4">
    <div class="col-12">
        <a href="<?= base_url('exercises') ?>" class="btn btn-outline-secondary mb-3">
            <i class="fas fa-arrow-left me-2"></i>Back to Exercises
        </a>
        <h2>
            <i class="fas fa-edit me-2"></i>Edit Exercise
            <span class="badge bg-<?= $exercise['status'] == 'active' ? 'success' : ($exercise['status'] == 'draft' ? 'warning' : ($exercise['status'] == 'cancelled' ? 'danger' : ($exercise['status'] == 'submitted' ? 'info' : 'primary'))) ?>">
                <?= ucfirst(esc($exercise['status'])) ?>
            </span>
        </h2>
        <p class="text-muted">Update exercise details and assigned officers</p>
    </div>
</div>

<!-- Edit Exercise Form -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0"><i class="fas fa-clipboard-list me-2"></i>Exercise Details</h5>
    </div>
    <div class="card-body">
        <form action="<?= base_url('exercises/update/' . $exercise['id']) ?>" method="post">
            <?= csrf_field() ?>
            
            <div class="row">
                <!-- Basic Details Section -->
                <div class="col-12">
                    <h6 class="border-bottom pb-2 mb-3">Basic Information</h6>
                    
                    <!-- Hidden fields for organization and location from session -->
                    <input type="hidden" name="org_id" value="<?= session()->get('org_id') ?>">
                    <input type="hidden" name="country_id" value="<?= session()->get('orgcountry_id') ?>">
                    <input type="hidden" name="province_id" value="<?= session()->get('orgprovince_id') ?>">
                    <input type="hidden" name="district_id" value="<?= session()->get('district_id') ?>">
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Exercise Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required 
                               value="<?= old('title', $exercise['title']) ?>" placeholder="e.g. Q2 Crop Data Collection">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                 placeholder="Brief description of the exercise purpose and objectives"><?= old('description', $exercise['description']) ?></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_from" class="form-label">Start Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date_from" name="date_from" required 
                                   value="<?= old('date_from', date('Y-m-d', strtotime($exercise['date_from']))) ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date_to" class="form-label">End Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date_to" name="date_to" required 
                                   value="<?= old('date_to', date('Y-m-d', strtotime($exercise['date_to']))) ?>">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Officer Assignment Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <h6 class="border-bottom pb-2 mb-3">Officer Assignment</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="officer_responsible_id" class="form-label">Officer Responsible <span class="text-danger">*</span></label>
                            <select class="form-select" id="officer_responsible_id" name="officer_responsible_id" required>
                                <option value="">Select Responsible Officer</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['id'] ?>" <?= old('officer_responsible_id', $exercise['officer_responsible_id']) == $user['id'] ? 'selected' : '' ?>>
                                        <?= esc($user['name'] ?? $user['username']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Additional Field Officers</label>
                            <select class="form-select" id="officers" name="officers[]" multiple>
                                <?php 
                                $assignedOfficers = array_column($officers, 'user_id');
                                foreach ($users as $user): 
                                ?>
                                    <option value="<?= $user['id'] ?>" <?= in_array($user['id'], $assignedOfficers) ? 'selected' : '' ?>>
                                        <?= esc($user['name'] ?? $user['username']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Hold Ctrl (or Cmd on Mac) to select multiple officers</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Status Update Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <h6 class="border-bottom pb-2 mb-3">Status</h6>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Current Status</label>
                        <div>
                            <span class="badge bg-<?= $exercise['status'] == 'active' ? 'success' : ($exercise['status'] == 'draft' ? 'warning' : ($exercise['status'] == 'cancelled' ? 'danger' : ($exercise['status'] == 'submitted' ? 'info' : 'primary'))) ?> p-2">
                                <?= ucfirst(esc($exercise['status'])) ?>
                            </span>
                            <?php if ($exercise['status_at']): ?>
                                <small class="text-muted ms-2">Updated on <?= date('M d, Y H:i', strtotime($exercise['status_at'])) ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-primary btn-status-update">
                            <i class="fas fa-exchange-alt me-2"></i>Change Status
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 text-end">
                <a href="<?= base_url('exercises') ?>" class="btn btn-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">Update Exercise</button>
            </div>
        </form>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusUpdateModal" tabindex="-1" aria-labelledby="statusUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusUpdateModalLabel">Update Exercise Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="statusUpdateForm">
                    <div class="mb-3">
                        <label for="status_update" class="form-label">Status</label>
                        <select class="form-select" id="status_update" name="status" required>
                            <option value="">Select Status</option>
                            <option value="draft" <?= $exercise['status'] == 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="active" <?= $exercise['status'] == 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="submitted" <?= $exercise['status'] == 'submitted' ? 'selected' : '' ?>>Submitted</option>
                            <option value="approved" <?= $exercise['status'] == 'approved' ? 'selected' : '' ?>>Approved</option>
                            <option value="cancelled" <?= $exercise['status'] == 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="status_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="status_remarks" name="remarks" rows="3"><?= $exercise['status_remarks'] ?></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmStatusUpdate">Update Status</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize Select2 for better dropdown experience
    $('#officers').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select officers'
    });

    // Date validation
    $('#date_to').on('change', function() {
        const startDate = new Date($('#date_from').val());
        const endDate = new Date($(this).val());
        
        if (endDate < startDate) {
            toastr.error('End date cannot be before start date');
            $(this).val('');
        }
    });

    // Handle status update button click
    $('.btn-status-update').on('click', function() {
        $('#statusUpdateModal').modal('show');
    });

    // Handle confirm status update
    $('#confirmStatusUpdate').on('click', function() {
        const status = $('#status_update').val();
        const remarks = $('#status_remarks').val();
        
        if (!status) {
            toastr.error('Please select a status');
            return;
        }
        
        // Send AJAX request to update status
        $.ajax({
            url: '<?= base_url('exercises/updateStatus/' . $exercise['id']) ?>',
            type: 'POST',
            data: {
                status: status,
                remarks: remarks
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message);
                }
                $('#statusUpdateModal').modal('hide');
            },
            error: function() {
                toastr.error('An error occurred while updating status.');
                $('#statusUpdateModal').modal('hide');
            }
        });
    });
});
</script>
<?= $this->endSection() ?> 