<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Report Generation Section -->
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Generate Report</h5>
                </div>
                <div class="card-body">
                    <?= form_open('staff/generate-report', ['id' => 'reportForm', 'method' => 'get']) ?>
                        <div class="mb-3">
                            <label class="form-label">Report Type</label>
                            <select class="form-select" name="report_type" required>
                                <option value="">Select Report Type</option>
                                <option value="crop_yield">Crop Yield Report</option>
                                <option value="field_survey">Field Survey Report</option>
                                <option value="monthly">Monthly Progress Report</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date Range</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="date" class="form-control" name="start_date" required>
                                    <small class="text-muted">Start Date</small>
                                </div>
                                <div class="col-6">
                                    <input type="date" class="form-control" name="end_date" required>
                                    <small class="text-muted">End Date</small>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Location</label>
                            <select class="form-select" name="location">
                                <option value="all">All Locations</option>
                                <?php foreach ($locations ?? [] as $location): ?>
                                    <option value="<?= $location['location_id'] ?>">
                                        <?= esc($location['location_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-agri w-100">
                            <i class="fas fa-file-export me-2"></i> Generate Report
                        </button>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Dataset Records</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-success btn-sm" id="filterBtn">
                            <i class="fas fa-filter me-1"></i> Filter
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" id="exportBtn">
                            <i class="fas fa-download me-1"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="datasetsTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Location</th>
                                    <th>Group</th>
                                    <th>Crop</th>
                                    <th>Item Name</th>
                                    <th>Quantity</th>
                                    <th>Target</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($datasets as $dataset): ?>
                                    <tr>
                                        <td><?= date('Y-m-d', strtotime($dataset['created_at'])) ?></td>
                                        <td><?= esc($dataset['location_name']) ?></td>
                                        <td><?= esc($dataset['group_name']) ?></td>
                                        <td><?= esc($dataset['crop_name']) ?></td>
                                        <td><?= esc($dataset['item_name']) ?></td>
                                        <td>
                                            <?= esc($dataset['quantity']) ?>
                                            <?= esc($dataset['unit']) ?>
                                        </td>
                                        <td><?= esc($dataset['target']) ?></td>
                                        <td>
                                            <span class="badge <?= $dataset['status'] === 'active' ? 'bg-success' : 'bg-danger' ?>">
                                                <?= ucfirst($dataset['status']) ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Analytics -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Crop Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="cropChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Monthly Progress (<?= date('Y') ?>)</h5>
                </div>
                <div class="card-body">
                    <canvas id="progressChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable
    let table = $('#datasetsTable').DataTable({
        order: [[0, 'desc']],
        pageLength: 10,
        responsive: true
    });

    // Handle report form submission
    $('#reportForm').on('submit', function(e) {
        e.preventDefault();
        $.get($(this).attr('action'), $(this).serialize(), function(response) {
            if (response.success) {
                toastr.success(response.message);
                table.clear();
                response.data.forEach(function(dataset) {
                    table.row.add([
                        dataset.created_at.split(' ')[0],
                        dataset.location_name,
                        dataset.group_name,
                        dataset.crop_name,
                        dataset.item_name,
                        dataset.quantity + ' ' + dataset.unit,
                        dataset.target,
                        `<span class="badge ${dataset.status === 'active' ? 'bg-success' : 'bg-danger'}">
                            ${dataset.status.charAt(0).toUpperCase() + dataset.status.slice(1)}
                        </span>`
                    ]);
                });
                table.draw();
            } else {
                toastr.error(response.message);
            }
        });
    });

    // Initialize Crop Distribution Chart
    new Chart(document.getElementById('cropChart'), {
        type: 'pie',
        data: {
            labels: <?= json_encode($cropData['labels']) ?>,
            datasets: [{
                data: <?= json_encode($cropData['data']) ?>,
                backgroundColor: [
                    '#2e7d32', '#1b5e20', '#4caf50', '#81c784', '#a5d6a7',
                    '#c8e6c9', '#388e3c', '#66bb6a', '#43a047', '#519657'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            let value = context.raw || 0;
                            let total = context.dataset.data.reduce((a, b) => a + b, 0);
                            let percentage = ((value * 100) / total).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });

    // Initialize Monthly Progress Chart
    new Chart(document.getElementById('progressChart'), {
        type: 'line',
        data: {
            labels: <?= json_encode($monthlyData['labels']) ?>,
            datasets: [{
                label: 'Quantity',
                data: <?= json_encode($monthlyData['data']) ?>,
                borderColor: '#2e7d32',
                backgroundColor: 'rgba(46, 125, 50, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Export button functionality
    $('#exportBtn').click(function() {
        let csvContent = "data:text/csv;charset=utf-8,";
        
        // Add headers
        let headers = [];
        $('#datasetsTable thead th').each(function() {
            headers.push($(this).text());
        });
        csvContent += headers.join(",") + "\r\n";
        
        // Add data
        table.rows().every(function() {
            let data = this.data();
            // Clean the data (remove HTML tags)
            data = data.map(item => item.replace(/<[^>]*>/g, ''));
            csvContent += data.join(",") + "\r\n";
        });
        
        // Create download link
        let encodedUri = encodeURI(csvContent);
        let link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "report_" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);
        link.click();
    });
});
</script>
<?= $this->endSection() ?>

<style>
.btn-agri {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    border: none;
    transition: all 0.3s ease;
}

.btn-agri:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46,125,50,0.2);
    color: white;
}
</style>
<?= $this->endSection() ?> 