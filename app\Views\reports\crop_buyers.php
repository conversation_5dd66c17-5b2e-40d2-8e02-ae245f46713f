<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Crop Buyers Report</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">Home</a></li>
                    <li class="breadcrumb-item active">Crop Buyers</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Summary Cards -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= number_format($total_buyers) ?></h3>
                        <p>Total Buyers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?= number_format($total_transactions) ?></h3>
                        <p>Total Transactions</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>K<?= number_format($total_value, 2) ?></h3>
                        <p>Total Transaction Value</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= number_format($total_crops) ?></h3>
                        <p>Different Crops</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Buyers List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-users mr-1"></i>
                            Crop Buyers List
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="buyersTable">
                                <thead>
                                    <tr>
                                        <th>Buyer Name</th>
                                        <th>Buyer Code</th>
                                        <th>Contact</th>
                                        <th>Operation</th>
                                        <th>Crops Purchased</th>
                                        <th>Total Transactions</th>
                                        <th>Total Value</th>
                                        <th>Last Purchase</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($buyers as $buyer): ?>
                                    <tr>
                                        <td><?= esc($buyer['name']) ?></td>
                                        <td><?= esc($buyer['buyer_code']) ?></td>
                                        <td>
                                            <i class="fas fa-phone mr-1"></i> <?= esc($buyer['contact_number']) ?><br>
                                            <i class="fas fa-envelope mr-1"></i> <?= esc($buyer['email']) ?><br>
                                            <i class="fas fa-map-marker-alt mr-1"></i> <?= esc($buyer['address']) ?>
                                        </td>
                                        <td>
                                            <?= ucfirst($buyer['operation_span']) ?>
                                            <?php if (!empty($buyer['location_name'])): ?>
                                                <br><small class="text-muted"><?= esc($buyer['location_name']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php 
                                            if (!empty($buyer['crops'])):
                                                $crops = $buyer['crops'];
                                                sort($crops); // Sort alphabetically
                                                foreach ($crops as $crop): 
                                            ?>
                                                <span class="badge badge-success mb-1"><?= esc($crop) ?></span>
                                            <?php 
                                                endforeach;
                                            else:
                                                echo '<span class="text-muted">No crops</span>';
                                            endif;
                                            ?>
                                        </td>
                                        <td class="text-center"><?= number_format($buyer['total_transactions']) ?></td>
                                        <td class="text-right">K<?= number_format($buyer['total_value'], 2) ?></td>
                                        <td><?= date('d M Y', strtotime($buyer['last_purchase'])) ?></td>
                                        <td class="text-center">
                                            <span class="badge badge-<?= $buyer['status'] == 'active' ? 'success' : 'danger' ?>">
                                                <?= ucfirst($buyer['status']) ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crop Distribution Chart -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-1"></i>
                            Crop Distribution
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="cropDistributionChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line mr-1"></i>
                            Monthly Transactions
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyTransactionsChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#buyersTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print"]
    }).buttons().container().appendTo('#buyersTable_wrapper .col-md-6:eq(0)');

    // Collect data from the table for charts
    var cropData = {};
    var monthlyData = {};
    
    $('#buyersTable tbody tr').each(function() {
        // Get crops from badges
        var cropBadges = $(this).find('td:eq(4) .badge');
        cropBadges.each(function() {
            var cropName = $(this).text();
            cropData[cropName] = (cropData[cropName] || 0) + 1;
        });

        // Get transaction value and date
        var value = parseFloat($(this).find('td:eq(5)').text().replace('K', '').replace(',', ''));
        var date = $(this).find('td:eq(6)').text();
        if (date) {
            var month = date.substring(3); // Get "MMM YYYY" format
            monthlyData[month] = (monthlyData[month] || 0) + value;
        }
    });

    // Convert data for charts
    var cropLabels = Object.keys(cropData).sort();
    var cropValues = cropLabels.map(function(key) { return cropData[key]; });

    var monthLabels = Object.keys(monthlyData).sort();
    var monthValues = monthLabels.map(function(key) { return monthlyData[key]; });

    // Crop Distribution Chart
    var cropCtx = document.getElementById('cropDistributionChart').getContext('2d');
    new Chart(cropCtx, {
        type: 'doughnut',
        data: {
            labels: cropLabels,
            datasets: [{
                data: cropValues,
                backgroundColor: [
                    '#28a745',
                    '#17a2b8',
                    '#ffc107',
                    '#dc3545',
                    '#6c757d',
                    '#007bff',
                    '#20c997',
                    '#6610f2'
                ]
            }]
        },
        options: {
            maintainAspectRatio: false,
            responsive: true,
            plugins: {
                legend: {
                    position: 'right'
                },
                title: {
                    display: true,
                    text: 'Number of Buyers per Crop'
                }
            }
        }
    });

    // Monthly Transactions Chart
    var transCtx = document.getElementById('monthlyTransactionsChart').getContext('2d');
    new Chart(transCtx, {
        type: 'line',
        data: {
            labels: monthLabels,
            datasets: [{
                label: 'Transaction Value',
                data: monthValues,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            maintainAspectRatio: false,
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Monthly Transaction Values'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'K' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
