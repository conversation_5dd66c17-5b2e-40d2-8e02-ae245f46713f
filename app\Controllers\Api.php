<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;
use App\Models\DistrictModel;
use App\Models\LlgModel;

class Api extends ResourceController
{
    protected $format = 'json';

    public function get_districts()
    {
        try {
            $province_id = $this->request->getPost('province_id');
            
            if (!$province_id) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Province ID is required'
                ]);
            }

            $districts = model(DistrictModel::class)
                ->where('province_id', $province_id)
                ->orderBy('name', 'ASC')
                ->findAll();

            return $this->response->setJSON($districts);
        } catch (\Exception $e) {
            log_message('error', '[API Get Districts] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching districts'
            ]);
        }
    }

    public function get_llgs()
    {
        try {
            $district_id = $this->request->getPost('district_id');
            
            if (!$district_id) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'District ID is required'
                ]);
            }

            $llgs = model(LlgModel::class)
                ->where('district_id', $district_id)
                ->orderBy('name', 'ASC')
                ->findAll();

            return $this->response->setJSON($llgs);
        } catch (\Exception $e) {
            log_message('error', '[API Get LLGs] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching LLGs'
            ]);
        }
    }
} 