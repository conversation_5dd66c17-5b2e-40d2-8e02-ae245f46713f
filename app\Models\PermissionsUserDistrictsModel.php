<?php

namespace App\Models;

use CodeIgniter\Model;

class PermissionsUserDistrictsModel extends Model
{
    protected $table = 'permissions_user_districts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    protected $allowedFields = [
        'org_id',
        'user_id',
        'district_id',
        'default_district',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'org_id' => 'required',
        'user_id' => 'required',
        'district_id' => 'required',
        'default_district' => 'required|in_list[0,1]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Get user's districts with names
    public function getUserDistricts($userId, $orgId)
    {
        return $this->select('permissions_user_districts.*, adx_district.name as district_name, adx_district.districtcode')
                    ->join('adx_district', 'adx_district.id = permissions_user_districts.district_id')
                    ->where('permissions_user_districts.user_id', $userId)
                    ->where('permissions_user_districts.org_id', $orgId)
                    ->findAll();
    }

    // Add a method to check if a district is already assigned to a user
    public function isDistrictAssigned($userId, $districtId, $orgId)
    {
        return $this->where([
            'user_id' => $userId,
            'district_id' => $districtId,
            'org_id' => $orgId
        ])->countAllResults() > 0;
    }
} 