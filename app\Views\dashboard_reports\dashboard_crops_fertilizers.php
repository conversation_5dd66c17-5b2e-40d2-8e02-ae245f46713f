<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                <h1 class="m-0">Crops Fertilizers Dashboard</h1>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <!-- Total Blocks Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="totalBlocksCount">0</h3>
                        <p>Total Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <!-- Total Fertilizers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3 id="totalFertilizers">0</h3>
                        <p>Total Fertilizer Types</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-flask"></i>
                    </div>
                </div>
            </div>
            <!-- Total Quantity Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3 id="totalQuantity">0</h3>
                        <p>Total Quantity</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-weight"></i>
                    </div>
                </div>
            </div>
            <!-- Total Farmers Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3 id="totalFarmers">0</h3>
                        <p>Total Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fertilizer Data Table -->
            <div class="row">
                <div class="col-12">
                <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Fertilizer Usage Data</h3>
                        </div>
                        <div class="card-body">
                        <!-- Location Filters -->
                        <div id="filterContainer" class="mb-3 d-flex flex-wrap gap-2">
                            <!-- Filters will be inserted here by JavaScript -->
                        </div>
                        <div class="table-responsive">
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger">
                                    Error loading data: <?= $error ?>
                                </div>
                            <?php endif; ?>

                            <?php if (empty($fertilizerData)): ?>
                                <div class="alert alert-warning">
                                    No fertilizer data found.
                                </div>
                            <?php endif; ?>

                            <table id="fertilizerTable" class="table table-bordered text-nowrap table-striped">
                                <thead>
                                    <tr>
                                        <th data-column-id="block_code">Block Code</th>
                                        <th data-column-id="farmer_name">Farmer Name</th>
                                        <th data-column-id="crop">Crop</th>
                                        <th data-column-id="fertilizer">Fertilizer</th>
                                        <th data-column-id="quantity">Total Quantity</th>
                                        <th data-column-id="province">Province</th>
                                        <th data-column-id="district">District</th>
                                        <th data-column-id="llg">LLG</th>
                                        <th data-column-id="ward">Ward</th>
                                        <th data-column-id="village">Village</th>
                                        <th data-column-id="action_date">Latest Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if (!empty($fertilizerData)):
                                        foreach ($fertilizerData as $data): 
                                    ?>
                                    <tr>
                                        <td><?= $data['block_code'] ?? 'N/A' ?></td>
                                        <td><?= trim(($data['given_name'] ?? '') . ' ' . ($data['surname'] ?? '')) ?></td>
                                        <td><?= $data['crop_name'] ?? 'N/A' ?></td>
                                        <td><?= $data['fertilizer_name'] ?? 'N/A' ?></td>
                                        <td><?= number_format($data['total_quantity'] ?? 0) ?> <?= $data['unit_of_measure'] ?? '' ?></td>
                                        <td><?= $data['province_name'] ?? 'N/A' ?></td>
                                        <td><?= $data['district_name'] ?? 'N/A' ?></td>
                                        <td><?= $data['llg_name'] ?? 'N/A' ?></td>
                                        <td><?= $data['ward_name'] ?? 'N/A' ?></td>
                                        <td><?= $data['block_site'] ?? 'N/A' ?></td>
                                        <td><?= isset($data['latest_action_date']) ? date('d/m/Y', strtotime($data['latest_action_date'])) : 'N/A' ?></td>
                                    </tr>
                                    <?php 
                                        endforeach;
                                    endif;
                                    ?>
                                </tbody>
                            </table>

                            <?php if (!empty($fertilizerData)): ?>
                            <div class="mt-3">
                                <small class="text-muted">Total records: <?= count($fertilizerData) ?></small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Distribution -->
        <div class="row">
            <!-- Fertilizer Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Fertilizer Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="fertilizerDistributionChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Crop Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Distribution by Fertilizer Usage</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row - Location -->
        <div class="row">
            <!-- Location Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Location Distribution</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="locationChart"></canvas>
                    </div>
                </div>
            </div>
            <!-- Monthly Trend -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Monthly Fertilizer Usage Trend</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
class FilterableTable {
    constructor(tableId) {
        this.table = document.getElementById(tableId);
        this.tableId = tableId;
        this.dataTable = $(`#${tableId}`).DataTable();
        this.filterContainer = document.getElementById('filterContainer');
        this.columnMap = {};
        this.openDropdowns = new Set(); // Track open dropdowns
        this.filters = {
            crop: [],
            fertilizer: [],
            province: [],
            district: [],
            llg: [],
            ward: []
        };
        
        // Initialize column map
        this.initColumnMap();
        this.init();

        // Add drawCallback after initialization
        this.dataTable.on('draw', () => {
            this.updateSummaryCards();
            this.initializeCharts();
            this.updateClosedDropdowns();
        });

        // Add search callback for real-time updates
        this.dataTable.on('search.dt', () => {
            this.updateClosedDropdowns();
        });

        // Add page change callback
        this.dataTable.on('page.dt', () => {
            this.updateClosedDropdowns();
        });

        // Add length change callback
        this.dataTable.on('length.dt', () => {
            this.updateClosedDropdowns();
        });

        // Add order change callback
        this.dataTable.on('order.dt', () => {
            this.updateClosedDropdowns();
        });
    }

    initColumnMap() {
        const headers = this.table.querySelectorAll('th[data-column-id]');
        headers.forEach((header, index) => {
            this.columnMap[header.dataset.columnId] = index;
        });
    }

    init() {
        this.createLocationFilters();
        this.updateSummaryCards();
        this.initializeCharts();
    }

    createLocationFilters() {
        const locations = [
            { id: 'crop', label: 'Crop', columnId: 'crop' },
            { id: 'fertilizer', label: 'Fertilizer', columnId: 'fertilizer' },
            { id: 'province', label: 'Province', columnId: 'province' },
            { id: 'district', label: 'District', columnId: 'district' },
            { id: 'llg', label: 'LLG', columnId: 'llg' },
            { id: 'ward', label: 'Ward', columnId: 'ward' }
        ];

        locations.forEach(location => {
            const filterGroup = this.createFilterDropdown(location);
            this.filterContainer.appendChild(filterGroup);
        });
    }

    createFilterDropdown(location) {
        const container = document.createElement('div');
        container.className = 'dropdown mr-2';
        container.innerHTML = `
            <button class="btn btn-default dropdown-toggle" type="button" 
                    data-toggle="dropdown" data-filter="${location.id}" aria-haspopup="true" aria-expanded="false">
                ${location.label} <span class="badge badge-light"></span>
            </button>
            <div class="dropdown-menu p-2" style="min-width: 250px; max-height: 300px; overflow-y: auto;" data-boundary="viewport">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" 
                           id="selectAll_${location.id}" data-filter="${location.id}">
                    <label class="custom-control-label" for="selectAll_${location.id}">Select All</label>
                </div>
                <hr class="my-2">
                <div id="options_${location.id}"></div>
            </div>
        `;

        // Add event listeners to track open/close state
        $(container).on('show.bs.dropdown', () => {
            this.openDropdowns.add(location.id);
        });

        $(container).on('hide.bs.dropdown', () => {
            this.openDropdowns.delete(location.id);
            // Update the dropdown after it's closed
            this.updateFilterDropdown(location.id, true);
        });

        // Keep dropdown open when clicking inside
        container.querySelector('.dropdown-menu').addEventListener('click', (e) => {
            e.stopPropagation();
        });

        this.populateFilterOptions(location, container);
        return container;
    }

    populateFilterOptions(location, container) {
        const optionsContainer = container.querySelector(`#options_${location.id}`);
        const uniqueValues = new Set();
        const columnIndex = this.columnMap[location.columnId];
        
        // Get unique values from the column
        const columnData = this.dataTable
            .rows()
            .data()
            .toArray()
            .map(row => row[columnIndex])
            .filter(value => value);
        
        columnData.forEach(value => uniqueValues.add(value));

        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${location.id}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${location.id}">
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        this.setupFilterEvents(container, location.id);
    }

    setupFilterEvents(container, filterId) {
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        const checkboxes = container.querySelectorAll(`input[type="checkbox"][data-filter="${filterId}"]:not(#selectAll_${filterId})`);
        const button = container.querySelector('button');
        const dropdownMenu = container.querySelector('.dropdown-menu');

        // Keep dropdown open when clicking checkboxes
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('click', (event) => {
                event.stopPropagation();
                this.handleFilterChange(checkbox, filterId);
                selectAll.checked = Array.from(checkboxes).every(cb => cb.checked);
                selectAll.indeterminate = Array.from(checkboxes).some(cb => cb.checked) && !selectAll.checked;
            });
        });

        // Handle select all checkbox
        selectAll.addEventListener('click', (event) => {
            event.stopPropagation();
            checkboxes.forEach(cb => {
                if (!cb.disabled) {
                    cb.checked = selectAll.checked;
                    this.handleFilterChange(cb, filterId);
                }
            });
        });

        // Keep dropdown open when clicking inside
        dropdownMenu.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Update badge count
        this.updateBadgeCount(button, filterId);
    }

    updateBadgeCount(button, filterId) {
        if (!button) return;
        
        const count = this.filters[filterId].length;
        const badge = button.querySelector('.badge');
        if (badge) {
            badge.textContent = count > 0 ? count : '';
            badge.style.display = count > 0 ? 'inline-block' : 'none';
        }
    }

    handleFilterChange(checkbox, filterId) {
        const value = checkbox.value;
        
        // Update filters array
        if (checkbox.checked) {
            if (!this.filters[filterId].includes(value)) {
                this.filters[filterId].push(value);
            }
        } else {
            this.filters[filterId] = this.filters[filterId].filter(v => v !== value);
        }

        // Reset dependent filters
        const filterOrder = ['crop', 'fertilizer', 'province', 'district', 'llg', 'ward'];
        const currentIndex = filterOrder.indexOf(filterId);
        
        // Only reset filters that come after the current one
        if (currentIndex !== -1) {
            filterOrder.slice(currentIndex + 1).forEach(dependentFilterId => {
                this.filters[dependentFilterId] = [];
                this.resetFilterDropdown(dependentFilterId);
                this.updateFilterDropdown(dependentFilterId);
            });
        }

        // Apply filters to DataTable
        this.applyFilters();
    }

    resetFilterDropdown(filterId) {
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        if (!container) return;

        // Reset checkboxes
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = false;
            cb.disabled = false;
        });

        // Reset select all checkbox
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        if (selectAll) {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        }

        // Reset badge count
        const button = container.querySelector('button');
        this.updateBadgeCount(button, filterId);
    }

    updateClosedDropdowns() {
        const filterOrder = ['crop', 'fertilizer', 'province', 'district', 'llg', 'ward'];
        filterOrder.forEach((filterId, index) => {
            if (!this.openDropdowns.has(filterId)) {
                // Only update if there are no parent filters or if parent filters have values
                const shouldUpdate = filterOrder.slice(0, index).every(parentId => 
                    this.filters[parentId].length === 0 || 
                    this.filters[parentId].some(value => this.getAvailableValuesForFilter(parentId).includes(value))
                );
                
                if (shouldUpdate) {
                    this.updateFilterDropdown(filterId, true);
                } else {
                    this.resetFilterDropdown(filterId);
                }
            }
        });
    }

    getAvailableValuesForFilter(filterId) {
        const columnIndex = this.columnMap[filterId];
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data().toArray();
        const uniqueValues = new Set();

        visibleRows.forEach(row => {
            const value = row[columnIndex];
            if (value) uniqueValues.add(value);
        });

        return Array.from(uniqueValues);
    }

    updateFilterDropdown(filterId, preserveSelection = false) {
        // Skip update if the dropdown is open
        if (this.openDropdowns.has(filterId)) return;

        const filterOrder = ['crop', 'fertilizer', 'province', 'district', 'llg', 'ward'];
        
        // Get all visible rows that match current filters
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data().toArray();
        
        // Get the current filter's container
        const container = this.filterContainer.querySelector(`[data-filter="${filterId}"]`).closest('.dropdown');
        if (!container) return;

        const optionsContainer = container.querySelector(`#options_${filterId}`);
        const currentSelections = preserveSelection ? 
            Array.from(container.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value) : [];
        
        // Clear existing options
        optionsContainer.innerHTML = '';
        
        // Get unique values for this filter that match parent filters
        const uniqueValues = new Set();
        
        visibleRows.forEach(row => {
            // Check if this row matches all parent filters
            const matchesParentFilters = filterOrder.slice(0, filterOrder.indexOf(filterId)).every(parentId => {
                const parentValues = this.filters[parentId];
                return parentValues.length === 0 || parentValues.includes(row[this.columnMap[parentId]]);
            });
            
            if (matchesParentFilters) {
                const value = row[this.columnMap[filterId]];
                if (value) uniqueValues.add(value);
            }
        });

        // Create new options
        Array.from(uniqueValues).sort().forEach((value, index) => {
            const optionId = `${filterId}_${index}`;
            const option = document.createElement('div');
            option.className = 'custom-control custom-checkbox';
            const isChecked = preserveSelection && currentSelections.includes(value);
            option.innerHTML = `
                <input type="checkbox" class="custom-control-input" 
                       id="${optionId}" value="${value}" data-filter="${filterId}"
                       ${isChecked ? 'checked' : ''}>
                <label class="custom-control-label" for="${optionId}">${value}</label>
            `;
            optionsContainer.appendChild(option);
        });

        // Update select all checkbox state
        const selectAll = container.querySelector(`#selectAll_${filterId}`);
        if (selectAll) {
            const checkboxes = container.querySelectorAll(`input[type="checkbox"][data-filter="${filterId}"]:not(#selectAll_${filterId})`);
            selectAll.checked = Array.from(checkboxes).every(cb => cb.checked);
            selectAll.indeterminate = Array.from(checkboxes).some(cb => cb.checked) && !selectAll.checked;
        }

        // Reattach event listeners
        this.setupFilterEvents(container, filterId);

        // Update badge count
        const button = container.querySelector('button');
        this.updateBadgeCount(button, filterId);
    }

    applyFilters() {
        // Clear existing search functions
        $.fn.dataTable.ext.search = [];

        // Add filter function
        $.fn.dataTable.ext.search.push((settings, searchData) => {
            if (settings.nTable.id !== this.tableId) return true;

            return Object.entries(this.filters).every(([key, values]) => {
                if (values.length === 0) return true;
                
                const columnIndex = this.columnMap[key];
                return values.includes(searchData[columnIndex]);
            });
        });

        // Redraw table and update UI
        this.dataTable.draw();
        this.updateSummaryCards();
        this.initializeCharts();
    }

    calculateSummaryData() {
        const visibleRows = this.dataTable.rows({ search: 'applied' }).data();
        let totalQuantity = 0;
        const uniqueBlocks = new Set();
        const uniqueFarmers = new Set();
        const uniqueFertilizers = new Set();

        visibleRows.each(row => {
            const quantity = parseFloat(row[this.columnMap.quantity].split(' ')[0].replace(/[^\d.-]/g, '')) || 0;
            totalQuantity += quantity;
            uniqueBlocks.add(row[this.columnMap.block_code]);
            uniqueFarmers.add(row[this.columnMap.farmer_name]);
            uniqueFertilizers.add(row[this.columnMap.fertilizer]);
        });

        return {
            totalBlocks: uniqueBlocks.size,
            totalQuantity,
            totalFertilizers: uniqueFertilizers.size,
            totalFarmers: uniqueFarmers.size
        };
    }

    updateSummaryCards() {
        const summaryData = this.calculateSummaryData();
        
        const animateCounter = (element, value, decimals = 0) => {
            if (!element) return;
            
            const duration = 1000;
            const startValue = parseFloat(element.textContent.replace(/[^\d.-]/g, '')) || 0;
            const startTime = performance.now();
            
            const update = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentValue = startValue + (value - startValue) * progress;
                element.textContent = currentValue.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            };
            
            requestAnimationFrame(update);
        };

        animateCounter(document.getElementById('totalBlocksCount'), summaryData.totalBlocks);
        animateCounter(document.getElementById('totalFertilizers'), summaryData.totalFertilizers);
        animateCounter(document.getElementById('totalQuantity'), summaryData.totalQuantity, 2);
        animateCounter(document.getElementById('totalFarmers'), summaryData.totalFarmers);
    }

    initializeCharts() {
        const visibleData = this.dataTable.rows({ search: 'applied' }).data();
        const chartData = {
            fertilizers: {},
            crops: {},
            locations: {},
            monthlyTrend: {}
        };

        visibleData.each(row => {
            const fertilizer = row[this.columnMap.fertilizer];
            const crop = row[this.columnMap.crop];
            const district = row[this.columnMap.district];
            const quantity = parseFloat(row[this.columnMap.quantity].split(' ')[0].replace(/[^\d.-]/g, '')) || 0;
            const date = row[this.columnMap.action_date];
            const month = new Date(date.split('/').reverse().join('-')).toLocaleString('default', { month: 'long', year: 'numeric' });

            chartData.fertilizers[fertilizer] = (chartData.fertilizers[fertilizer] || 0) + quantity;
            chartData.crops[crop] = (chartData.crops[crop] || 0) + quantity;
            chartData.locations[district] = (chartData.locations[district] || 0) + quantity;
            chartData.monthlyTrend[month] = (chartData.monthlyTrend[month] || 0) + quantity;
        });

        this.updateChart('fertilizerDistributionChart', {
            type: 'pie',
            data: chartData.fertilizers,
            title: 'Fertilizer Distribution by Usage'
        });

        this.updateChart('cropDistributionChart', {
            type: 'pie',
            data: chartData.crops,
            title: 'Crop Distribution by Fertilizer Usage'
        });

        this.updateChart('locationChart', {
            type: 'bar',
            data: chartData.locations,
            title: 'Location Distribution',
            yAxisLabel: 'Quantity Used'
        });

        const sortedMonthlyData = Object.fromEntries(
            Object.entries(chartData.monthlyTrend).sort((a, b) => {
                return new Date(a[0]) - new Date(b[0]);
            })
        );

        this.updateChart('monthlyTrendChart', {
            type: 'line',
            data: sortedMonthlyData,
            title: 'Monthly Fertilizer Usage Trend',
            yAxisLabel: 'Quantity Used'
        });
    }

    updateChart(chartId, config) {
        const ctx = document.getElementById(chartId).getContext('2d');
        const chartInstance = Chart.getChart(ctx);
        if (chartInstance) {
            chartInstance.destroy();
        }

        const colors = [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 206, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
            'rgba(153, 102, 255, 0.2)',
            'rgba(255, 159, 64, 0.2)'
        ];
        
        const borderColors = colors.map(color => color.replace('0.2', '1'));

        new Chart(ctx, {
            type: config.type,
            data: {
                labels: Object.keys(config.data),
                datasets: [{
                    data: Object.values(config.data),
                    backgroundColor: config.type === 'pie' ? colors : colors[0],
                    borderColor: config.type === 'pie' ? borderColors : borderColors[0],
                    borderWidth: 1,
                    tension: config.type === 'line' ? 0.4 : 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: config.type === 'pie' ? 'right' : 'top'
                    },
                    title: {
                        display: true,
                        text: config.title
                    }
                },
                scales: (config.type === 'bar' || config.type === 'line') ? {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: config.yAxisLabel
                        }
                    }
                } : undefined
            }
        });
    }
}

$(document).ready(function() {
    // Initialize DataTable with all options
    const dataTable = $('#fertilizerTable').DataTable({
        responsive: false,
        lengthChange: true,
        autoWidth: false,
        buttons: ["copy", "excel", "pdf", "print"],
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'All']],
        order: [[10, 'desc']] // Sort by action_date column
    });

    // Move buttons to the correct container
    dataTable.buttons().container().appendTo('#fertilizerTable_wrapper .col-md-6:eq(0)');

    // Prevent dropdowns from closing on click
    $(document).on('click', '.dropdown-menu', function(e) {
        e.stopPropagation();
    });

    // Initialize FilterableTable
    new FilterableTable('fertilizerTable');
});
</script>

<style>
.card-body {
    min-height: 400px;
}
.dropdown {
    display: inline-block;
    margin-right: 1rem;
    margin-bottom: 1rem;
}
.dropdown-menu {
    padding: 10px;
}
.badge {
    margin-left: 5px;
}
.custom-control {
    margin-bottom: 5px;
}
.custom-control-input:disabled ~ .custom-control-label {
    opacity: 0.5;
}
.table-responsive {
    min-height: 400px;
}
.dt-buttons {
    margin-bottom: 1rem;
}
</style>
<?= $this->endSection() ?>
