<?php

namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\CropsFarmCropsDataModel;
use App\Models\CropsFarmBlockModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsModel;
use App\Models\provinceModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\wardModel;

class Crops_Dashboard extends BaseController
{
    protected $cropsDataModel;
    protected $farmBlockModel;
    protected $farmerModel;
    protected $cropsModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $session;

    public function __construct()
    {
        $this->cropsDataModel = new CropsFarmCropsDataModel();
        $this->farmBlockModel = new CropsFarmBlockModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropsModel = new CropsModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->wardModel = new wardModel();
        $this->session = \Config\Services::session();
        helper(['form', 'url', 'array', 'date', 'info']);
    }

    public function view($block_id = null)
    {
        $district_id = $this->session->get('district_id');
        
        $builder = $this->cropsDataModel->select('
            crops_farm_blocks.id as block_id,
            crops_farm_blocks.block_code,
            crops_farm_blocks.block_site,
            crops_farm_blocks.status,
            farmer_information.given_name,
            farmer_information.surname,
            adx_crops.crop_name,
            adx_crops.crop_color_code,
            adx_province.name as province_name,
            adx_district.name as district_name,
            adx_llg.name as llg_name,
            adx_ward.name as ward_name,
            crops_farm_crops_data.breed,
            crops_farm_crops_data.action_date as latest_action_date,
            crops_farm_crops_data.action_type,
            crops_farm_crops_data.number_of_plants,
            crops_farm_crops_data.hectares,
            MAX(CASE WHEN crops_farm_crops_data.action_type = "add" THEN crops_farm_crops_data.number_of_plants ELSE 0 END) as total_plants_added,
            MAX(CASE WHEN crops_farm_crops_data.action_type = "remove" THEN crops_farm_crops_data.number_of_plants ELSE 0 END) as total_plants_removed,
            MAX(CASE WHEN crops_farm_crops_data.action_type = "add" THEN crops_farm_crops_data.hectares ELSE 0 END) as total_hectares_added,
            MAX(CASE WHEN crops_farm_crops_data.action_type = "remove" THEN crops_farm_crops_data.hectares ELSE 0 END) as total_hectares_removed
        ')
        ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
        ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
        ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
        ->join('adx_province', 'adx_province.id = crops_farm_blocks.province_id')
        ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
        ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
        ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
        ->where('crops_farm_crops_data.status', 'active');

        if ($district_id) {
            $builder->where('crops_farm_blocks.district_id', $district_id);
        }

        if ($block_id) {
            $builder->where('crops_farm_blocks.id', $block_id);
        }

        $crops_data = $builder->groupBy('crops_farm_blocks.id')
                             ->orderBy('latest_action_date', 'DESC')
                             ->findAll();

        $data = [
            'title' => 'Crops Dashboard',
            'page_header' => 'Crops Data Dashboard',
            'menu' => 'crops-data',
            'crops_data' => $crops_data
        ];

        return view('dashboard_reports/dashboard_crops', $data);
    }
} 