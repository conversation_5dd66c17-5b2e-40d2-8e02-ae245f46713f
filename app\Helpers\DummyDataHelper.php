<?php

namespace App\Helpers;

class DummyDataHelper
{
    // Master Data
    public static function getCrops()
    {
        return [
            ['id' => 1, 'crop_name' => 'Sweet Potato', 'crop_icon' => 'sweet-potato.png', 'crop_color_code' => '#FF6B35'],
            ['id' => 2, 'crop_name' => 'Taro', 'crop_icon' => 'taro.png', 'crop_color_code' => '#8B4513'],
            ['id' => 3, 'crop_name' => 'Banana', 'crop_icon' => 'banana.png', 'crop_color_code' => '#FFD700'],
            ['id' => 4, 'crop_name' => 'Coconut', 'crop_icon' => 'coconut.png', 'crop_color_code' => '#8B4513'],
            ['id' => 5, 'crop_name' => 'Coffee', 'crop_icon' => 'coffee.png', 'crop_color_code' => '#6F4E37'],
            ['id' => 6, 'crop_name' => 'Cocoa', 'crop_icon' => 'cocoa.png', 'crop_color_code' => '#D2691E']
        ];
    }

    public static function getFertilizers()
    {
        return [
            ['id' => 1, 'name' => 'NPK 15-15-15', 'icon' => 'npk.png', 'color_code' => '#32CD32'],
            ['id' => 2, 'name' => 'Urea', 'icon' => 'urea.png', 'color_code' => '#FFFFFF'],
            ['id' => 3, 'name' => 'Super Phosphate', 'icon' => 'phosphate.png', 'color_code' => '#D3D3D3'],
            ['id' => 4, 'name' => 'Potash', 'icon' => 'potash.png', 'color_code' => '#FF69B4'],
            ['id' => 5, 'name' => 'Organic Compost', 'icon' => 'compost.png', 'color_code' => '#8B4513']
        ];
    }

    public static function getPesticides()
    {
        return [
            ['id' => 1, 'name' => 'Roundup', 'icon' => 'herbicide.png', 'color_code' => '#228B22'],
            ['id' => 2, 'name' => 'Malathion', 'icon' => 'insecticide.png', 'color_code' => '#DC143C'],
            ['id' => 3, 'name' => 'Copper Fungicide', 'icon' => 'fungicide.png', 'color_code' => '#B87333'],
            ['id' => 4, 'name' => 'Neem Oil', 'icon' => 'neem.png', 'color_code' => '#6B8E23']
        ];
    }

    public static function getLivestock()
    {
        return [
            ['id' => 1, 'name' => 'Pig', 'icon' => 'pig.png', 'color_code' => '#FFC0CB'],
            ['id' => 2, 'name' => 'Chicken', 'icon' => 'chicken.png', 'color_code' => '#FFFF00'],
            ['id' => 3, 'name' => 'Duck', 'icon' => 'duck.png', 'color_code' => '#87CEEB'],
            ['id' => 4, 'name' => 'Cattle', 'icon' => 'cattle.png', 'color_code' => '#8B4513'],
            ['id' => 5, 'name' => 'Goat', 'icon' => 'goat.png', 'color_code' => '#DEB887']
        ];
    }

    // Geographic Data
    public static function getProvinces()
    {
        return [
            ['id' => 1, 'name' => 'Western Province', 'provincecode' => 'WP', 'country_id' => 1],
            ['id' => 2, 'name' => 'Southern Highlands Province', 'provincecode' => 'SP', 'country_id' => 1],
            ['id' => 3, 'name' => 'Eastern Highlands Province', 'provincecode' => 'EP', 'country_id' => 1],
            ['id' => 4, 'name' => 'Morobe Province', 'provincecode' => 'MP', 'country_id' => 1],
            ['id' => 5, 'name' => 'National Capital District', 'provincecode' => 'NCD', 'country_id' => 1]
        ];
    }

    public static function getDistricts()
    {
        return [
            ['id' => 1, 'name' => 'Daru District', 'districtcode' => 'WP001', 'province_id' => 1],
            ['id' => 2, 'name' => 'Kiunga District', 'districtcode' => 'WP002', 'province_id' => 1],
            ['id' => 3, 'name' => 'Mendi District', 'districtcode' => 'SP001', 'province_id' => 2],
            ['id' => 4, 'name' => 'Nipa District', 'districtcode' => 'SP002', 'province_id' => 2],
            ['id' => 5, 'name' => 'Goroka District', 'districtcode' => 'EP001', 'province_id' => 3]
        ];
    }

    // Farmers Data
    public static function getFarmers()
    {
        return [
            [
                'id' => 1,
                'farmer_code' => 'F001',
                'given_name' => 'Michael',
                'surname' => 'Temu',
                'full_name' => 'Michael Temu',
                'date_of_birth' => '1975-05-15',
                'age' => 49,
                'gender' => 'Male',
                'village' => 'Boboa',
                'ward_name' => 'Ward 1 Daru',
                'llg_name' => 'Daru Urban LLG',
                'district_name' => 'Daru District',
                'province_name' => 'Western Province',
                'phone' => '+************',
                'email' => '',
                'address' => 'Boboa Village, Daru',
                'marital_status' => 'Married',
                'education_level' => 'Primary School',
                'status' => 'active',
                'created_at' => '2024-01-15 10:30:00',
                'total_farm_blocks' => 3,
                'total_crops' => 2,
                'total_livestock_blocks' => 1
            ],
            [
                'id' => 2,
                'farmer_code' => 'F002',
                'given_name' => 'Sarah',
                'surname' => 'Kila',
                'full_name' => 'Sarah Kila',
                'date_of_birth' => '1982-08-22',
                'age' => 42,
                'gender' => 'Female',
                'village' => 'Daru Town',
                'ward_name' => 'Ward 2 Daru',
                'llg_name' => 'Daru Urban LLG',
                'district_name' => 'Daru District',
                'province_name' => 'Western Province',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'address' => 'Daru Town Ward 2',
                'marital_status' => 'Single',
                'education_level' => 'Secondary School',
                'status' => 'active',
                'created_at' => '2024-01-18 14:20:00',
                'total_farm_blocks' => 2,
                'total_crops' => 3,
                'total_livestock_blocks' => 2
            ],
            [
                'id' => 3,
                'farmer_code' => 'F003',
                'given_name' => 'James',
                'surname' => 'Waigani',
                'full_name' => 'James Waigani',
                'date_of_birth' => '1968-12-10',
                'age' => 55,
                'gender' => 'Male',
                'village' => 'Sigabadaru',
                'ward_name' => 'Ward 3 Daru Rural',
                'llg_name' => 'Daru Rural LLG',
                'district_name' => 'Daru District',
                'province_name' => 'Western Province',
                'phone' => '+************',
                'email' => '',
                'address' => 'Sigabadaru Village',
                'marital_status' => 'Married',
                'education_level' => 'No Formal Education',
                'status' => 'active',
                'created_at' => '2024-01-20 09:15:00',
                'total_farm_blocks' => 4,
                'total_crops' => 1,
                'total_livestock_blocks' => 1
            ],
            [
                'id' => 4,
                'farmer_code' => 'F004',
                'given_name' => 'Grace',
                'surname' => 'Mendi',
                'full_name' => 'Grace Mendi',
                'date_of_birth' => '1978-03-18',
                'age' => 46,
                'gender' => 'Female',
                'village' => 'Kiunga Central',
                'ward_name' => 'Ward 1 Kiunga',
                'llg_name' => 'Kiunga Urban LLG',
                'district_name' => 'Kiunga District',
                'province_name' => 'Western Province',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'address' => 'Kiunga Central Ward 1',
                'marital_status' => 'Married',
                'education_level' => 'High School',
                'status' => 'active',
                'created_at' => '2024-02-01 11:45:00',
                'total_farm_blocks' => 1,
                'total_crops' => 1,
                'total_livestock_blocks' => 1
            ],
            [
                'id' => 5,
                'farmer_code' => 'F005',
                'given_name' => 'Paul',
                'surname' => 'Highland',
                'full_name' => 'Paul Highland',
                'date_of_birth' => '1985-07-25',
                'age' => 39,
                'gender' => 'Male',
                'village' => 'Mendi Town',
                'ward_name' => 'Ward 1 Mendi',
                'llg_name' => 'Mendi Urban LLG',
                'district_name' => 'Mendi District',
                'province_name' => 'Southern Highlands Province',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'address' => 'Mendi Town Ward 1',
                'marital_status' => 'Single',
                'education_level' => 'Technical/Vocational',
                'status' => 'active',
                'created_at' => '2024-02-05 08:30:00',
                'total_farm_blocks' => 2,
                'total_crops' => 1,
                'total_livestock_blocks' => 0
            ]
        ];
    }

    // Crop Farm Blocks
    public static function getCropFarmBlocks()
    {
        return [
            [
                'id' => 1,
                'block_code' => 'CB001',
                'farmer_id' => 1,
                'farmer_name' => 'Michael Temu',
                'crop_id' => 1,
                'crop_name' => 'Sweet Potato',
                'crop_color' => '#FF6B35',
                'village' => 'Boboa',
                'block_site' => 'Boboa Garden Block A',
                'lon' => '143.2092',
                'lat' => '-9.0763',
                'province_name' => 'Western Province',
                'district_name' => 'Daru District',
                'status' => 'active',
                'created_at' => '2024-01-15 10:45:00',
                'exercise_title' => 'Western Province Agricultural Survey 2024 Q1',
                'total_crops_data' => 5,
                'total_fertilizer_data' => 3,
                'total_harvest_data' => 2
            ],
            [
                'id' => 2,
                'block_code' => 'CB002',
                'farmer_id' => 1,
                'farmer_name' => 'Michael Temu',
                'crop_id' => 2,
                'crop_name' => 'Taro',
                'crop_color' => '#8B4513',
                'village' => 'Boboa',
                'block_site' => 'Boboa Wetland Block',
                'lon' => '143.2105',
                'lat' => '-9.0770',
                'province_name' => 'Western Province',
                'district_name' => 'Daru District',
                'status' => 'active',
                'created_at' => '2024-01-16 11:30:00',
                'exercise_title' => 'Western Province Agricultural Survey 2024 Q1',
                'total_crops_data' => 3,
                'total_fertilizer_data' => 2,
                'total_harvest_data' => 1
            ],
            [
                'id' => 3,
                'block_code' => 'CB003',
                'farmer_id' => 2,
                'farmer_name' => 'Sarah Kila',
                'crop_id' => 3,
                'crop_name' => 'Banana',
                'crop_color' => '#FFD700',
                'village' => 'Daru Town',
                'block_site' => 'Urban Banana Grove',
                'lon' => '143.2115',
                'lat' => '-9.0750',
                'province_name' => 'Western Province',
                'district_name' => 'Daru District',
                'status' => 'active',
                'created_at' => '2024-01-20 09:20:00',
                'exercise_title' => 'Western Province Agricultural Survey 2024 Q1',
                'total_crops_data' => 8,
                'total_fertilizer_data' => 4,
                'total_harvest_data' => 6
            ],
            [
                'id' => 4,
                'block_code' => 'CB004',
                'farmer_id' => 5,
                'farmer_name' => 'Paul Highland',
                'crop_id' => 5,
                'crop_name' => 'Coffee',
                'crop_color' => '#6F4E37',
                'village' => 'Mendi Town',
                'block_site' => 'Highland Coffee Plantation',
                'lon' => '143.6565',
                'lat' => '-6.1478',
                'province_name' => 'Southern Highlands Province',
                'district_name' => 'Mendi District',
                'status' => 'active',
                'created_at' => '2024-02-05 14:15:00',
                'exercise_title' => 'Southern Highlands Coffee Assessment 2024',
                'total_crops_data' => 12,
                'total_fertilizer_data' => 6,
                'total_harvest_data' => 4
            ],
            [
                'id' => 5,
                'block_code' => 'CB005',
                'farmer_id' => 4,
                'farmer_name' => 'Grace Mendi',
                'crop_id' => 4,
                'crop_name' => 'Coconut',
                'crop_color' => '#8B4513',
                'village' => 'Kiunga Central',
                'block_site' => 'Riverside Coconut Grove',
                'lon' => '141.2957',
                'lat' => '-6.1214',
                'province_name' => 'Western Province',
                'district_name' => 'Kiunga District',
                'status' => 'active',
                'created_at' => '2024-02-01 16:00:00',
                'exercise_title' => 'Kiunga District Baseline Study 2024',
                'total_crops_data' => 6,
                'total_fertilizer_data' => 2,
                'total_harvest_data' => 3
            ]
        ];
    }

    // Livestock Farm Blocks
    public static function getLivestockFarmBlocks()
    {
        return [
            [
                'id' => 1,
                'block_code' => 'LB001',
                'farmer_id' => 1,
                'farmer_name' => 'Michael Temu',
                'village' => 'Boboa',
                'block_site' => 'Boboa Pig Farm',
                'lon' => '143.2088',
                'lat' => '-9.0768',
                'province_name' => 'Western Province',
                'district_name' => 'Daru District',
                'status' => 'active',
                'created_at' => '2024-01-15 12:00:00',
                'total_livestock_data' => 3
            ],
            [
                'id' => 2,
                'block_code' => 'LB002',
                'farmer_id' => 2,
                'farmer_name' => 'Sarah Kila',
                'village' => 'Daru Town',
                'block_site' => 'Urban Poultry House',
                'lon' => '143.2120',
                'lat' => '-9.0745',
                'province_name' => 'Western Province',
                'district_name' => 'Daru District',
                'status' => 'active',
                'created_at' => '2024-01-18 15:30:00',
                'total_livestock_data' => 2
            ],
            [
                'id' => 3,
                'block_code' => 'LB003',
                'farmer_id' => 4,
                'farmer_name' => 'Grace Mendi',
                'village' => 'Kiunga Central',
                'block_site' => 'Kiunga Cattle Station',
                'lon' => '141.2960',
                'lat' => '-6.1220',
                'province_name' => 'Western Province',
                'district_name' => 'Kiunga District',
                'status' => 'active',
                'created_at' => '2024-02-01 17:00:00',
                'total_livestock_data' => 1
            ]
        ];
    }

    // Dashboard Statistics
    public static function getDashboardStats()
    {
        return [
            'total_farmers' => 5,
            'total_crop_blocks' => 5,
            'total_livestock_blocks' => 3,
            'total_exercises' => 3,
            'active_users' => 15,
            'total_provinces' => 5,
            'total_districts' => 8,
            'total_crops' => 6,
            'total_fertilizers' => 5,
            'total_pesticides' => 4,
            'total_livestock' => 5,
            'crop_data_entries' => 34,
            'fertilizer_applications' => 17,
            'harvest_records' => 16,
            'field_visits' => 8,
            'trainings_conducted' => 12,
            'inputs_distributed' => 25
        ];
    }

    // Reports Data
    public static function getFarmersReport()
    {
        $farmers = self::getFarmers();
        $totalFarmers = count($farmers);
        $maleCount = array_filter($farmers, fn($f) => $f['gender'] === 'Male');
        $femaleCount = array_filter($farmers, fn($f) => $f['gender'] === 'Female');
        $marriedCount = array_filter($farmers, fn($f) => $f['marital_status'] === 'Married');

        return [
            'total_farmers' => $totalFarmers,
            'male_farmers' => count($maleCount),
            'female_farmers' => count($femaleCount),
            'married_farmers' => count($marriedCount),
            'average_age' => 46,
            'farmers_with_email' => 2,
            'farmers_with_phone' => 5,
            'by_province' => [
                'Western Province' => 4,
                'Southern Highlands Province' => 1
            ],
            'by_education' => [
                'No Formal Education' => 1,
                'Primary School' => 1,
                'Secondary School' => 1,
                'High School' => 1,
                'Technical/Vocational' => 1
            ]
        ];
    }

    // Extension Activities
    public static function getFieldVisits()
    {
        return [
            [
                'id' => 1,
                'province_name' => 'Western Province',
                'district_name' => 'Daru District',
                'locations' => ['Boboa Village', 'Daru Town'],
                'date_start' => '2024-01-20',
                'date_end' => '2024-01-22',
                'purpose' => 'Farm assessment and technical advisory',
                'officers' => ['John Smith', 'Mary Wilson'],
                'beneficiaries' => 15,
                'status' => 'completed'
            ],
            [
                'id' => 2,
                'province_name' => 'Southern Highlands Province',
                'district_name' => 'Mendi District',
                'locations' => ['Mendi Town', 'Highland Villages'],
                'date_start' => '2024-02-05',
                'date_end' => '2024-02-07',
                'purpose' => 'Coffee quality improvement consultation',
                'officers' => ['Mary Wilson'],
                'beneficiaries' => 8,
                'status' => 'completed'
            ]
        ];
    }

    public static function getTrainings()
    {
        return [
            [
                'id' => 1,
                'province_name' => 'Western Province',
                'district_name' => 'Daru District',
                'locations' => ['Daru Community Center'],
                'date_start' => '2024-01-25',
                'date_end' => '2024-01-26',
                'topic' => 'Sustainable Sweet Potato Production',
                'objectives' => 'Improve yield and quality of sweet potato crops',
                'trainers' => ['John Smith', 'External Expert'],
                'attendees' => 25,
                'status' => 'completed'
            ],
            [
                'id' => 2,
                'province_name' => 'Western Province',
                'district_name' => 'Kiunga District',
                'locations' => ['Kiunga Agricultural Station'],
                'date_start' => '2024-02-10',
                'date_end' => '2024-02-11',
                'topic' => 'Integrated Pest Management',
                'objectives' => 'Reduce pesticide use while maintaining crop protection',
                'trainers' => ['Peter Johnson'],
                'attendees' => 18,
                'status' => 'completed'
            ]
        ];
    }
}