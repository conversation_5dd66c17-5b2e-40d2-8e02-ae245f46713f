<?php

function datetimeforms($date)
{
    if ($date == "0000-00-00") {
        echo "No Date Set";
    } else {
        if (!empty($date)) {
            $dtime = new DateTime($date);
            print $dtime->format("d M Y H:ia");
        } else {
            echo "-";
        }
    }
}

function dateforms($date)
{
    if ($date == "0000-00-00") {
        echo "No Date Set";
    } else {

        if (!empty($date)) {
            $dtime = new DateTime($date);

            print $dtime->format("d M Y");
        } else {
            echo "-";
        }
    }
}

function getAge($date)
{
    if (!empty($date)) {
        $dateOfBirth = $date;
        $today = date("Y-m-d");
        $diff = date_diff(date_create($dateOfBirth), date_create($today));
        $age = $diff->format('%y');
        if ($age == date("Y")) {
            echo "--";
        } else {
            return $age;
        }
    } else {
        echo "-";
    }
}

function getDateAgo($date)
{
    if (!empty($date)) {
        $dateAgo = round((strtotime(date("Y-m-d H:i:s")) - strtotime($date)) / 86400);
        echo $dateAgo;
    } else {
        echo "-";
    }
}

function minstoexpire($date){
     //expire date count down
     $future_date = new DateTime($date);
     $current_date = new DateTime();

     // calculate the time difference between the two dates
     $time_diff = $future_date->diff($current_date);

     // extract the remaining days, hours, minutes, and seconds from the time difference
     $days = $time_diff->days;
     $hours = $time_diff->h;
     $minutes = $time_diff->i;
     $seconds = $time_diff->s;

     return $minutes;
     // output the remaining time
    
}

/**
 * Info Helper
 * Contains utility functions for handling profile images and other info
 */

if (!function_exists('imgcheck')) {
    /**
     * Checks if an image exists and returns the proper image URL
     * If no image is provided or the image doesn't exist, returns a default image
     *
     * @param string $image The image path to check
     * @return string The URL to the image or default image
     */
    function imgcheck($image = null)
    {
        // If no image provided or empty string
        if (empty($image)) {
            return base_url('public/assets/system_img/no-users-img.png');
        }

        // Check if the image path is a URL (starts with http)
        if (str_starts_with($image, 'http')) {
            return $image;
        }

        // Check if the file exists in uploads directory
        $filepath = FCPATH . 'public/uploads/' . $image;
        if (file_exists($filepath) && !is_dir($filepath)) {
            return base_url('public/uploads/' . $image);
        }

        // Check if the file exists in system_img directory
        $filepath = FCPATH . 'public/assets/system_img/' . $image;
        if (file_exists($filepath) && !is_dir($filepath)) {
            return base_url('public/assets/system_img/' . $image);
        }

        // Return default image if file doesn't exist
        return base_url('public/assets/system_img/no-users-img.png');
    }
}

function getfileExtension($filepath){
    
    if(!empty($filepath)){
        echo $fileExt = pathinfo($filepath,PATHINFO_EXTENSION);
    }else{
        echo "No File";
    }
    
}


function removeCommaWithEmptySpace($string) {
    $result = str_replace(', ', ' ', $string);
    $result = str_replace(',,', ' ', $result);
    return $result;
  }
  
  function calculate_age($dob)
  {
      return date_diff(date_create($dob), date_create('today'))->y;
  }
  
  /**
   * Get district name by district ID
   *
   * @param int|null $district_id
   * @return string
   */
  function get_district_name($district_id) 
  {
      if (!$district_id) {
          return 'No District Assigned';
      }

      $db = \Config\Database::connect();
      $builder = $db->table('districts');
      $district = $builder->where('id', $district_id)->get()->getRowArray();
      
      return $district ? $district['name'] : 'Unknown District';
  }
  
  