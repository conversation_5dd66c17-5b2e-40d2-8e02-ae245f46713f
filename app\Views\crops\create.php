<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Add New Crop</h3>
        </div>
        <div class="card-body">
            <?php if (session()->has('errors')) : ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php foreach (session('errors') as $error) : ?>
                            <li><?= $error ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form action="<?= site_url('crops/create') ?>" method="post">
                <?= csrf_field() ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="crop_name" class="form-label">Crop Name</label>
                            <input type="text" class="form-control" id="crop_name" name="crop_name" 
                                value="<?= old('crop_name') ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="scientific_name" class="form-label">Scientific Name</label>
                            <input type="text" class="form-control" id="scientific_name" name="scientific_name" 
                                value="<?= old('scientific_name') ?>">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="3"><?= old('description') ?></textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="growing_season" class="form-label">Growing Season</label>
                            <input type="text" class="form-control" id="growing_season" name="growing_season" 
                                value="<?= old('growing_season') ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="average_growing_period" class="form-label">Average Growing Period (days)</label>
                            <input type="number" class="form-control" id="average_growing_period" name="average_growing_period" 
                                value="<?= old('average_growing_period') ?>">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="ideal_temperature" class="form-label">Ideal Temperature</label>
                            <input type="text" class="form-control" id="ideal_temperature" name="ideal_temperature" 
                                value="<?= old('ideal_temperature') ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="ideal_soil_ph" class="form-label">Ideal Soil pH</label>
                            <input type="text" class="form-control" id="ideal_soil_ph" name="ideal_soil_ph" 
                                value="<?= old('ideal_soil_ph') ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="water_requirement" class="form-label">Water Requirement</label>
                            <input type="text" class="form-control" id="water_requirement" name="water_requirement" 
                                value="<?= old('water_requirement') ?>">
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Save Crop</button>
                    <a href="<?= site_url('crops') ?>" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?> 