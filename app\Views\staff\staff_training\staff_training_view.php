<?= $this->extend('staff/templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="card-title mb-0">Training Details</h4>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group">
                            <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <a href="<?= base_url('staff/extension/trainings/participants/' . $training['id']) ?>" class="btn btn-primary">
                                <i class="fas fa-users"></i> Manage Participants
                            </a>
                            <a href="<?= base_url('staff/extension/trainings/edit/' . $training['id']) ?>" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php 
                    // Parse JSON fields
                    $locations = json_decode($training['locations'], true);
                    $gps = json_decode($training['gps'], true);
                    $content = json_decode($training['content'], true);
                    $materials = json_decode($training['materials'], true);
                    $attendees = json_decode($training['attendees'], true);
                    
                    // Status badge
                    $statusLabels = [
                        '1' => '<span class="badge bg-success">Completed</span>',
                        '2' => '<span class="badge bg-warning">Ongoing</span>',
                        '3' => '<span class="badge bg-primary">Scheduled</span>',
                        '4' => '<span class="badge bg-danger">Cancelled</span>'
                    ];
                    
                    $statusLabel = $statusLabels[$training['status']] ?? '<span class="badge bg-secondary">Unknown</span>';
                ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3 border-bottom pb-2">General Information</h5>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-4 col-form-label fw-bold">Training Topic:</label>
                            <div class="col-sm-8">
                                <p class="form-control-plaintext"><?= esc($training['topic']) ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-4 col-form-label fw-bold">Status:</label>
                            <div class="col-sm-8">
                                <p class="form-control-plaintext"><?= $statusLabel ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-4 col-form-label fw-bold">Start Date:</label>
                            <div class="col-sm-8">
                                <p class="form-control-plaintext"><?= date('d M Y', strtotime($training['date_start'])) ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-4 col-form-label fw-bold">End Date:</label>
                            <div class="col-sm-8">
                                <p class="form-control-plaintext"><?= date('d M Y', strtotime($training['date_end'])) ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-4 col-form-label fw-bold">Venue:</label>
                            <div class="col-sm-8">
                                <p class="form-control-plaintext"><?= esc($locations['venue'] ?? 'N/A') ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-4 col-form-label fw-bold">Location:</label>
                            <div class="col-sm-8">
                                <p class="form-control-plaintext"><?= esc($locations['location'] ?? 'N/A') ?></p>
                            </div>
                        </div>
                        
                        <?php if (!empty($gps['latitude']) && !empty($gps['longitude'])): ?>
                        <div class="mb-3 row">
                            <label class="col-sm-4 col-form-label fw-bold">GPS Coordinates:</label>
                            <div class="col-sm-8">
                                <p class="form-control-plaintext">
                                    <?= esc($gps['latitude'] ?? 'N/A') ?>, <?= esc($gps['longitude'] ?? 'N/A') ?>
                                </p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="mb-3 border-bottom pb-2">Participants Summary</h5>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-5 col-form-label fw-bold">Total Participants:</label>
                            <div class="col-sm-7">
                                <p class="form-control-plaintext"><?= count($attendees ?? []) ?></p>
                            </div>
                        </div>
                        
                        <?php if (!empty($attendees)): ?>
                        <?php 
                            $maleCount = 0;
                            $femaleCount = 0;
                            $farmerCount = 0;
                            $officialCount = 0;
                            $otherCount = 0;
                            
                            foreach ($attendees as $attendee) {
                                if (isset($attendee['gender'])) {
                                    if (strtolower($attendee['gender']) == 'male') {
                                        $maleCount++;
                                    } elseif (strtolower($attendee['gender']) == 'female') {
                                        $femaleCount++;
                                    }
                                }
                                
                                if (isset($attendee['type'])) {
                                    if (strtolower($attendee['type']) == 'farmer') {
                                        $farmerCount++;
                                    } elseif (strtolower($attendee['type']) == 'official') {
                                        $officialCount++;
                                    } else {
                                        $otherCount++;
                                    }
                                }
                            }
                        ?>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-5 col-form-label fw-bold">Male Participants:</label>
                            <div class="col-sm-7">
                                <p class="form-control-plaintext"><?= $maleCount ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-5 col-form-label fw-bold">Female Participants:</label>
                            <div class="col-sm-7">
                                <p class="form-control-plaintext"><?= $femaleCount ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-5 col-form-label fw-bold">Farmers:</label>
                            <div class="col-sm-7">
                                <p class="form-control-plaintext"><?= $farmerCount ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-5 col-form-label fw-bold">Officials:</label>
                            <div class="col-sm-7">
                                <p class="form-control-plaintext"><?= $officialCount ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3 row">
                            <label class="col-sm-5 col-form-label fw-bold">Others:</label>
                            <div class="col-sm-7">
                                <p class="form-control-plaintext"><?= $otherCount ?></p>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-info">
                            No participants have been added yet. 
                            <a href="<?= base_url('staff/extension/trainings/participants/' . $training['id']) ?>" class="alert-link">
                                Click here to add participants
                            </a>.
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <h5 class="mb-3 border-bottom pb-2">Training Details</h5>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Training Objectives:</label>
                            <div class="p-2 bg-light rounded">
                                <?= nl2br(esc($training['objectives'])) ?>
                            </div>
                        </div>
                        
                        <?php if (!empty($content['modules'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Training Modules:</label>
                            <div class="p-2 bg-light rounded">
                                <?= nl2br(esc($content['modules'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($content['activities'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Training Activities:</label>
                            <div class="p-2 bg-light rounded">
                                <?= nl2br(esc($content['activities'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($materials['materials'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Training Materials:</label>
                            <div class="p-2 bg-light rounded">
                                <?= nl2br(esc($materials['materials'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($content['notes'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Additional Notes:</label>
                            <div class="p-2 bg-light rounded">
                                <?= nl2br(esc($content['notes'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <a href="<?= base_url('staff/extension/trainings/edit/' . $training['id']) ?>" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit Training
                            </a>
                            <a href="<?= base_url('staff/extension/trainings/participants/' . $training['id']) ?>" class="btn btn-primary">
                                <i class="fas fa-users"></i> Manage Participants
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?> 