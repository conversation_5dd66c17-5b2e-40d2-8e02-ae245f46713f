<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">
                    <i class="fas fa-user-circle mr-2"></i>
                    Farmer Profile
                </h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('reports/farmers-report') ?>">Farmers Report</a></li>
                    <li class="breadcrumb-item active">Farmer Profile</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <div class="col-md-3">
                <div class="info-box bg-success">
                    <span class="info-box-icon"><i class="fas fa-user"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Farmer Code</span>
                        <span class="info-box-number"><?= esc($farmer['farmer_code']) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-info">
                    <span class="info-box-icon"><i class="fas fa-map-marker-alt"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Farm Blocks</span>
                        <span class="info-box-number"><?= $farmer['total_blocks'] ?></span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-warning">
                    <span class="info-box-icon"><i class="fas fa-dollar-sign"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Revenue</span>
                        <span class="info-box-number">K<?= number_format($farmer['total_revenue'], 2) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-danger">
                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Children</span>
                        <span class="info-box-number"><?= count($farmer['children']) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Personal Information -->
            <div class="col-md-6">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-circle mr-1"></i>
                            Personal Information
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?= esc($farmer['given_name']) ?> <?= esc($farmer['surname']) ?></p>
                                <p><strong>Gender:</strong> <?= esc($farmer['gender']) ?></p>
                                <p><strong>Date of Birth:</strong> <?= esc($farmer['date_of_birth']) ?></p>
                                <p><strong>Marital Status:</strong> <?= esc($farmer['marital_status']) ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Phone:</strong> <?= esc($farmer['phone']) ?></p>
                                <p><strong>Email:</strong> <?= esc($farmer['email']) ?></p>
                                <p><strong>Address:</strong> <?= esc($farmer['address']) ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Information -->
            <div class="col-md-6">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map-marker-alt mr-1"></i>
                            Location Information
                        </h3>
                    </div>
                    <div class="card-body">
                        <p><strong>Village:</strong> <?= esc($farmer['village']) ?></p>
                        <p><strong>Ward:</strong> <?= esc($farmer['ward_name']) ?></p>
                        <p><strong>LLG:</strong> <?= esc($farmer['llg_name']) ?></p>
                        <p><strong>District:</strong> <?= esc($farmer['district_name']) ?></p>
                        <p><strong>Province:</strong> <?= esc($farmer['province_name']) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Farm Blocks -->
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map mr-1"></i>
                            Farm Blocks
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Block Code</th>
                                        <th>Crop</th>
                                        <th>Location</th>
                                        <th>Site</th>
                                        <th>Coordinates</th>
                                        <th>Remarks</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($farmer['farm_blocks'] as $block): ?>
                                    <tr>
                                        <td><?= esc($block['block_code']) ?></td>
                                        <td><?= esc($block['crop_name']) ?></td>
                                        <td>
                                            <?= esc($block['village']) ?>
                                            <?= $block['ward_name'] ? ', ' . esc($block['ward_name']) : '' ?>
                                            <?= $block['llg_name'] ? ', ' . esc($block['llg_name']) : '' ?>
                                            <?= $block['district_name'] ? ', ' . esc($block['district_name']) : '' ?>
                                        </td>
                                        <td><?= esc($block['block_site']) ?></td>
                                        <td><?= esc($block['lat']) ?>, <?= esc($block['lon']) ?></td>
                                        <td><?= esc($block['remarks']) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Marketing Data -->
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line mr-1"></i>
                            Marketing Data
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Quantity</th>
                                        <th>Unit</th>
                                        <th>Price/Unit</th>
                                        <th>Total</th>
                                        <th>Market Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($farmer['marketing_data'] as $data): ?>
                                    <tr>
                                        <td><?= esc($data['item']) ?></td>
                                        <td><?= esc($data['quantity']) ?></td>
                                        <td><?= esc($data['unit_of_measure']) ?></td>
                                        <td>K<?= number_format($data['market_price_per_unit'], 2) ?></td>
                                        <td>K<?= number_format($data['quantity'] * $data['market_price_per_unit'], 2) ?></td>
                                        <td><?= date('Y-m-d', strtotime($data['market_date'])) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Children Information -->
        <div class="row">
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-child mr-1"></i>
                            Children Information
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Gender</th>
                                        <th>Date of Birth</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($farmer['children'] as $child): ?>
                                    <tr>
                                        <td><?= esc($child['name']) ?></td>
                                        <td><?= esc($child['gender']) ?></td>
                                        <td><?= date('d M Y', strtotime($child['date_of_birth'])) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.info-box {
    min-height: 100px;
    padding: 15px;
    border-radius: 0.25rem;
}
.info-box-icon {
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 2rem;
}
.info-box-content {
    padding: 5px 10px;
    margin-left: 70px;
}
.info-box-number {
    font-size: 1.5rem;
    font-weight: bold;
}
.table td {
    vertical-align: middle !important;
}
</style>

<?= $this->endSection() ?>
